import React, { useState } from 'react';
import { Search, Package, Calendar, User, ArrowLeft } from 'lucide-react';
import { api } from '../services/api';
import { CreateReturnModal } from '../components/Returns/CreateReturnModal';

interface Order {
  id: string;
  name: string;
  created_at: string;
  financial_status: string;
  fulfillment_status: string;
  total_price: string;
  currency: string;
  customer: {
    first_name: string;
    last_name: string;
    email: string;
  };
  line_items: Array<{
    id: string;
    title: string;
    quantity: number;
    price: string;
    sku: string;
    product_id: string;
    variant_id: string;
  }>;
}

const OrdersPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [error, setError] = useState('');
  const [showCreateReturn, setShowCreateReturn] = useState(false);

  const searchOrders = async () => {
    if (!searchTerm.trim()) {
      setError('Please enter an order number or customer email');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const response = await api.searchOrders(searchTerm);
      setOrders(response.orders || []);
      
      if (response.orders && response.orders.length === 0) {
        setError('No orders found matching your search');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to search orders');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReturn = async (order: Order) => {
    console.log('Create return clicked for order:', order.name);
    setSelectedOrder(order);
    setShowCreateReturn(true);
    console.log('Modal should be open now, showCreateReturn:', true);
  };

  const handleReturnCreated = () => {
    setShowCreateReturn(false);
    setSelectedOrder(null);
    // Optionally refresh the orders list or show a success message
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (selectedOrder) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => setSelectedOrder(null)}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Orders
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Order Details</h1>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Order Information</h3>
              <div className="space-y-2">
                <p><strong>Order:</strong> {selectedOrder.name}</p>
                <p><strong>Date:</strong> {formatDate(selectedOrder.created_at)}</p>
                <p><strong>Status:</strong> {selectedOrder.financial_status}</p>
                <p><strong>Fulfillment:</strong> {selectedOrder.fulfillment_status || 'unfulfilled'}</p>
                <p><strong>Total:</strong> {selectedOrder.currency} {selectedOrder.total_price}</p>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Customer</h3>
              <div className="space-y-2">
                <p><strong>Name:</strong> {selectedOrder.customer.first_name} {selectedOrder.customer.last_name}</p>
                <p><strong>Email:</strong> {selectedOrder.customer.email}</p>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-4">Items</h3>
            <div className="space-y-3">
              {selectedOrder.line_items.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{item.title}</h4>
                    <p className="text-sm text-gray-600">SKU: {item.sku || 'N/A'}</p>
                    <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{selectedOrder.currency} {item.price}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-6 flex gap-3">
            <button
              onClick={() => handleCreateReturn(selectedOrder)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Create Return
            </button>
          </div>
        </div>

        {/* Create Return Modal - moved inside selectedOrder view */}
        {showCreateReturn && selectedOrder && (
          <CreateReturnModal
            isOpen={showCreateReturn}
            onClose={() => setShowCreateReturn(false)}
            order={selectedOrder}
            onReturnCreated={handleReturnCreated}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
        <p className="text-gray-600">Search and manage your store orders</p>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search by order number (e.g., #1001) or customer email"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && searchOrders()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <button
            onClick={searchOrders}
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
      </div>

      {/* Orders List */}
      {orders.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Search Results ({orders.length} orders)</h2>
          </div>
          <div className="divide-y">
            {orders.map((order) => (
              <div key={order.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Package className="h-8 w-8 text-blue-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{order.name}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDate(order.created_at)}
                        </span>
                        <span className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {order.customer.first_name} {order.customer.last_name}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold">{order.currency} {order.total_price}</p>
                    <div className="flex gap-2 mt-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        order.financial_status === 'paid' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {order.financial_status}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        order.fulfillment_status === 'fulfilled' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {order.fulfillment_status || 'unfulfilled'}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="mt-4 flex gap-3">
                  <button
                    onClick={() => setSelectedOrder(order)}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => handleCreateReturn(order)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Create Return
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {orders.length === 0 && !loading && !error && (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Search for Orders</h3>
          <p className="text-gray-600">
            Enter an order number (e.g., #1001) or customer email to find orders and create returns.
          </p>
        </div>
      )}

      {/* Create Return Modal */}
      {showCreateReturn && selectedOrder && (
        <CreateReturnModal
          isOpen={showCreateReturn}
          onClose={() => setShowCreateReturn(false)}
          order={selectedOrder}
          onReturnCreated={handleReturnCreated}
        />
      )}
    </div>
  );
};

export default OrdersPage;

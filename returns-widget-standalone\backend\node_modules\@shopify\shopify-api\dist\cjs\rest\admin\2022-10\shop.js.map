{"version": 3, "file": "shop.js", "sources": ["../../../../../../../rest/admin/2022-10/shop.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  fields?: unknown;\n}\n\nexport class Shop extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"shop.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"shop\",\n      \"plural\": \"shops\"\n    }\n  ];\n\n  public static async current(\n    {\n      session,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<Shop | null> {\n    const result = await this.baseFind<Shop>({\n      session: session,\n      urlIds: {},\n      params: {\"fields\": fields, ...otherArgs},\n    });\n\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async all(\n    {\n      session,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Shop>> {\n    const response = await this.baseFind<Shop>({\n      session: session,\n      urlIds: {},\n      params: {\"fields\": fields, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public address1: string | null;\n  public address2: string | null;\n  public checkout_api_supported: boolean | null;\n  public city: string | null;\n  public country: string | null;\n  public country_code: string | null;\n  public country_name: string | null;\n  public county_taxes: boolean | null;\n  public created_at: string | null;\n  public currency: string | null;\n  public customer_email: string | null;\n  public domain: string | null;\n  public eligible_for_card_reader_giveaway: boolean | null;\n  public eligible_for_payments: boolean | null;\n  public email: string | null;\n  public enabled_presentment_currencies: string[] | null;\n  public finances: boolean | null;\n  public force_ssl: boolean | null;\n  public google_apps_domain: string | null;\n  public google_apps_login_enabled: string | null;\n  public has_discounts: boolean | null;\n  public has_gift_cards: boolean | null;\n  public has_storefront: boolean | null;\n  public iana_timezone: string | null;\n  public id: number | null;\n  public latitude: number | null;\n  public longitude: number | null;\n  public marketing_sms_consent_enabled_at_checkout: boolean | null;\n  public money_format: string | null;\n  public money_in_emails_format: string | null;\n  public money_with_currency_format: string | null;\n  public money_with_currency_in_emails_format: string | null;\n  public multi_location_enabled: boolean | null;\n  public myshopify_domain: string | null;\n  public name: string | null;\n  public password_enabled: boolean | null;\n  public phone: string | null;\n  public plan_display_name: string | null;\n  public plan_name: string | null;\n  public pre_launch_enabled: boolean | null;\n  public primary_locale: string | null;\n  public primary_location_id: number | null;\n  public province: string | null;\n  public province_code: string | null;\n  public requires_extra_payments_agreement: boolean | null;\n  public setup_required: boolean | null;\n  public shop_owner: string | null;\n  public source: string | null;\n  public tax_shipping: string | null;\n  public taxes_included: boolean | null;\n  public timezone: string | null;\n  public transactional_sms_disabled: boolean | null;\n  public updated_at: string | null;\n  public weight_unit: string | null;\n  public zip: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAalH,MAAO,IAAK,SAAQA,SAAI,CAAA;AACrB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,WAAW;KAC1E;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,MAAM;AAClB,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,OAAO,CACzB,EACE,OAAO,EACP,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAO;AACvC,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AACzC,SAAA,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAO;AACzC,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AACzC,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,QAAQ;AACR,IAAA,QAAQ;AACR,IAAA,sBAAsB;AACtB,IAAA,IAAI;AACJ,IAAA,OAAO;AACP,IAAA,YAAY;AACZ,IAAA,YAAY;AACZ,IAAA,YAAY;AACZ,IAAA,UAAU;AACV,IAAA,QAAQ;AACR,IAAA,cAAc;AACd,IAAA,MAAM;AACN,IAAA,iCAAiC;AACjC,IAAA,qBAAqB;AACrB,IAAA,KAAK;AACL,IAAA,8BAA8B;AAC9B,IAAA,QAAQ;AACR,IAAA,SAAS;AACT,IAAA,kBAAkB;AAClB,IAAA,yBAAyB;AACzB,IAAA,aAAa;AACb,IAAA,cAAc;AACd,IAAA,cAAc;AACd,IAAA,aAAa;AACb,IAAA,EAAE;AACF,IAAA,QAAQ;AACR,IAAA,SAAS;AACT,IAAA,yCAAyC;AACzC,IAAA,YAAY;AACZ,IAAA,sBAAsB;AACtB,IAAA,0BAA0B;AAC1B,IAAA,oCAAoC;AACpC,IAAA,sBAAsB;AACtB,IAAA,gBAAgB;AAChB,IAAA,IAAI;AACJ,IAAA,gBAAgB;AAChB,IAAA,KAAK;AACL,IAAA,iBAAiB;AACjB,IAAA,SAAS;AACT,IAAA,kBAAkB;AAClB,IAAA,cAAc;AACd,IAAA,mBAAmB;AACnB,IAAA,QAAQ;AACR,IAAA,aAAa;AACb,IAAA,iCAAiC;AACjC,IAAA,cAAc;AACd,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,YAAY;AACZ,IAAA,cAAc;AACd,IAAA,QAAQ;AACR,IAAA,0BAA0B;AAC1B,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,GAAG;;;;;"}
{"version": 3, "file": "storefront_access_token.js", "sources": ["../../../../../../../rest/admin/2022-10/storefront_access_token.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface DeleteArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n}\n\nexport class StorefrontAccessToken extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"id\"], \"path\": \"storefront_access_tokens/<id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"storefront_access_tokens.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [], \"path\": \"storefront_access_tokens.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"storefront_access_token\",\n      \"plural\": \"storefront_access_tokens\"\n    }\n  ];\n\n  public static async delete(\n    {\n      session,\n      id\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<StorefrontAccessToken>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<StorefrontAccessToken>> {\n    const response = await this.baseFind<StorefrontAccessToken>({\n      session: session,\n      urlIds: {},\n      params: {...otherArgs},\n    });\n\n    return response;\n  }\n\n  public title: string | null;\n  public access_scope: string | null;\n  public access_token: string | null;\n  public created_at: string | null;\n  public id: number | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAgBlH,MAAO,qBAAsB,SAAQA,SAAI,CAAA;AACtC,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oCAAoC,EAAC;AAC7G,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,+BAA+B,EAAC;AAC9F,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,+BAA+B;KAChG;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,yBAAyB;AACrC,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,MAAM,CACxB,EACE,OAAO,EACP,EAAE,EACS,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAwB;AACzD,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;IAEO,aAAa,GAAG,CACrB,EACE,OAAO,EACP,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAwB;AAC1D,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACvB,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,KAAK;AACL,IAAA,YAAY;AACZ,IAAA,YAAY;AACZ,IAAA,UAAU;AACV,IAAA,EAAE;;;;;"}
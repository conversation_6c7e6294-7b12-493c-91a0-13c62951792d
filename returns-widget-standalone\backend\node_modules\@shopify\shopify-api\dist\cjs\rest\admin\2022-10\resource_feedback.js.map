{"version": 3, "file": "resource_feedback.js", "sources": ["../../../../../../../rest/admin/2022-10/resource_feedback.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n}\n\nexport class ResourceFeedback extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"resource_feedback.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [], \"path\": \"resource_feedback.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"resource_feedback\",\n      \"plural\": \"resource_feedbacks\"\n    }\n  ];\n\n  public static async all(\n    {\n      session,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<ResourceFeedback>> {\n    const response = await this.baseFind<ResourceFeedback>({\n      session: session,\n      urlIds: {},\n      params: {...otherArgs},\n    });\n\n    return response;\n  }\n\n  public created_at: string | null;\n  public feedback_generated_at: string | null;\n  public messages: string[] | null;\n  public resource_id: number | null;\n  public resource_type: string | null;\n  public state: string | null;\n  public updated_at: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAYlH,MAAO,gBAAiB,SAAQA,SAAI,CAAA;AACjC,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,wBAAwB,EAAC;AACvF,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,wBAAwB;KACzF;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,mBAAmB;AAC/B,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,GAAG,CACrB,EACE,OAAO,EACP,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAmB;AACrD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACvB,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,UAAU;AACV,IAAA,qBAAqB;AACrB,IAAA,QAAQ;AACR,IAAA,WAAW;AACX,IAAA,aAAa;AACb,IAAA,KAAK;AACL,IAAA,UAAU;;;;;"}
interface CustomRequestInit {
    method?: string;
    headers?: HeadersInit;
    body?: string;
    signal?: AbortSignal;
    keepalive?: boolean;
}
type CustomFetchApi = (url: string, init?: CustomRequestInit) => Promise<Response>;
type OperationVariables$1 = Record<string, any>;
type DataChunk = Buffer | Uint8Array;
type HeadersObject = Record<string, string | string[]>;

interface ResponseErrors {
    networkStatusCode?: number;
    message?: string;
    graphQLErrors?: any[];
    response?: Response;
}
type GQLExtensions = Record<string, any>;
interface FetchResponseBody<TData = any> {
    data?: TData;
    extensions?: GQLExtensions;
    headers?: Headers;
}
interface ClientResponse<TData = any> extends FetchResponseBody<TData> {
    errors?: ResponseErrors;
}
interface ClientStreamResponse<TData = any> extends ClientResponse<TData> {
    hasNext: boolean;
}
interface ClientStreamIterator<TData = any> {
    [Symbol.asyncIterator](): AsyncIterator<ClientStreamResponse<TData>>;
}
interface LogContent {
    type: string;
    content: any;
}
interface HTTPResponseLog extends LogContent {
    type: 'HTTP-Response';
    content: {
        requestParams: Parameters<CustomFetchApi>;
        response: Response;
    };
}
interface HTTPResponseGraphQLDeprecationNotice extends LogContent {
    type: 'HTTP-Response-GraphQL-Deprecation-Notice';
    content: {
        requestParams: Parameters<CustomFetchApi>;
        deprecationNotice: string;
    };
}
interface HTTPRetryLog extends LogContent {
    type: 'HTTP-Retry';
    content: {
        requestParams: Parameters<CustomFetchApi>;
        lastResponse?: Response;
        retryAttempt: number;
        maxRetries: number;
    };
}
type LogContentTypes = HTTPResponseLog | HTTPRetryLog | HTTPResponseGraphQLDeprecationNotice;
type Logger<TLogContentTypes = LogContentTypes> = (logContent: TLogContentTypes) => void;
interface ClientOptions {
    headers: HeadersObject;
    url: string;
    customFetchApi?: CustomFetchApi;
    retries?: number;
    logger?: Logger;
}
interface ClientConfig {
    readonly headers: ClientOptions['headers'];
    readonly url: ClientOptions['url'];
    readonly retries: Required<ClientOptions>['retries'];
}
interface RequestOptions {
    variables?: OperationVariables$1;
    url?: string;
    headers?: HeadersObject;
    retries?: number;
    keepalive?: boolean;
    signal?: AbortSignal;
}
type RequestParams = [operation: string, options?: RequestOptions];
interface GraphQLClient {
    readonly config: ClientConfig;
    fetch: (...props: RequestParams) => Promise<Response>;
    request: <TData = any>(...props: RequestParams) => Promise<ClientResponse<TData>>;
    requestStream: <TData = any>(...props: RequestParams) => Promise<ClientStreamIterator<TData>>;
}

declare function createGraphQLClient({ headers, url, customFetchApi, retries, logger, }: ClientOptions): GraphQLClient;

declare function getErrorMessage(error: any): string;
declare function validateRetries({ client, retries, }: {
    client: string;
    retries?: number;
}): void;

type InputMaybe<_R = never> = never;
interface AllOperations {
    [key: string]: {
        variables: any;
        return: any;
    };
    [key: number | symbol]: never;
}
type UnpackedInput<InputType> = 'input' extends keyof InputType ? InputType['input'] : InputType;
type UnpackedInputMaybe<InputType> = InputType extends InputMaybe<infer R> ? InputMaybe<UnpackedInput<R>> : UnpackedInput<InputType>;
type OperationVariables<Operation extends keyof Operations, Operations extends AllOperations> = Operations[Operation]['variables'] extends Record<string, never> ? Record<string, never> : {
    variables?: {
        [k in keyof Operations[Operation]['variables']]: UnpackedInputMaybe<Operations[Operation]['variables'][k]>;
    };
};
type ResponseWithType<T = any> = Omit<Response, 'json'> & {
    json: () => Promise<T>;
};
type ReturnData<Operation extends keyof Operations, Operations extends AllOperations> = Operation extends keyof Operations ? Operations[Operation]['return'] : any;

interface UnsupportedApiVersionLog extends LogContent {
    type: 'Unsupported_Api_Version';
    content: {
        apiVersion: string;
        supportedApiVersions: string[];
    };
}
type ApiClientLogContentTypes = LogContentTypes | UnsupportedApiVersionLog;
type ApiClientLogger<TLogContentTypes = ApiClientLogContentTypes> = Logger<TLogContentTypes>;
interface ApiClientConfig {
    storeDomain: string;
    apiVersion: string;
    headers: HeadersObject;
    apiUrl: string;
    retries?: number;
}
type ApiClientRequestOptions<Operation extends keyof Operations = string, Operations extends AllOperations = AllOperations> = {
    apiVersion?: string;
    headers?: HeadersObject;
    retries?: number;
    signal?: AbortSignal;
} & (Operation extends keyof Operations ? OperationVariables<Operation, Operations> : {
    variables?: Record<string, any>;
});
type ApiClientRequestParams<Operation extends keyof Operations, Operations extends AllOperations> = [
    operation: Operation,
    options?: ApiClientRequestOptions<Operation, Operations>
];
type ApiClientFetch<Operations extends AllOperations = AllOperations> = <Operation extends keyof Operations = string>(...params: ApiClientRequestParams<Operation, Operations>) => Promise<ResponseWithType<FetchResponseBody<ReturnData<Operation, Operations>>>>;
type ApiClientRequest<Operations extends AllOperations = AllOperations> = <TData = undefined, Operation extends keyof Operations = string>(...params: ApiClientRequestParams<Operation, Operations>) => Promise<ClientResponse<TData extends undefined ? ReturnData<Operation, Operations> : TData>>;
type ApiClientRequestStream<Operations extends AllOperations = AllOperations> = <TData = undefined, Operation extends keyof Operations = string>(...params: ApiClientRequestParams<Operation, Operations>) => Promise<ClientStreamIterator<TData extends undefined ? ReturnData<Operation, Operations> : TData>>;
interface ApiClient<TClientConfig extends ApiClientConfig = ApiClientConfig, Operations extends AllOperations = AllOperations> {
    readonly config: Readonly<TClientConfig>;
    getHeaders: (headers?: HeadersObject) => HeadersObject;
    getApiUrl: (apiVersion?: string) => string;
    fetch: ApiClientFetch<Operations>;
    request: ApiClientRequest<Operations>;
}

declare function validateDomainAndGetStoreUrl({ client, storeDomain, }: {
    client: string;
    storeDomain: string | undefined;
}): string;
declare function validateApiVersion({ client, currentSupportedApiVersions, apiVersion, logger, }: {
    client: string;
    currentSupportedApiVersions: string[];
    apiVersion: string;
    logger?: ApiClientLogger;
}): void;

declare function getCurrentApiVersion(): {
    year: number;
    quarter: number;
    version: string;
};
declare function getCurrentSupportedApiVersions(): string[];

interface GenerateHttpFetchOptions {
    clientLogger: Logger;
    customFetchApi?: CustomFetchApi;
    client?: string;
    defaultRetryWaitTime?: number;
    retriableCodes?: number[];
}
declare function generateHttpFetch({ clientLogger, customFetchApi, client, defaultRetryWaitTime, retriableCodes, }: GenerateHttpFetchOptions): (requestParams: Parameters<CustomFetchApi>, count: number, maxRetries: number) => ReturnType<GraphQLClient["fetch"]>;

declare function generateGetHeaders(config: ApiClientConfig): ApiClient['getHeaders'];
declare function generateGetGQLClientParams<Operations extends AllOperations = AllOperations>({ getHeaders, getApiUrl }: Pick<ApiClient, 'getHeaders' | 'getApiUrl'>): <Operation extends keyof Operations>(operation: Operation, options?: ApiClientRequestOptions<Operation, Operations>) => RequestParams;

export { createGraphQLClient, generateGetGQLClientParams, generateGetHeaders, generateHttpFetch, getCurrentApiVersion, getCurrentSupportedApiVersions, getErrorMessage, validateApiVersion, validateDomainAndGetStoreUrl, validateRetries };
export type { AllOperations, ApiClient, ApiClientConfig, ApiClientFetch, ApiClientLogContentTypes, ApiClientLogger, ApiClientRequest, ApiClientRequestOptions, ApiClientRequestParams, ApiClientRequestStream, ClientConfig, ClientOptions, ClientResponse, ClientStreamIterator, ClientStreamResponse, CustomFetchApi, DataChunk, FetchResponseBody, GQLExtensions, GraphQLClient, HTTPResponseGraphQLDeprecationNotice, HTTPResponseLog, HTTPRetryLog, HeadersObject as Headers, InputMaybe, LogContent, LogContentTypes, Logger, OperationVariables, RequestOptions, RequestParams, ResponseErrors, ResponseWithType, ReturnData, UnsupportedApiVersionLog };

{"version": 3, "file": "smart_collection.js", "sources": ["../../../../../../../rest/admin/2022-10/smart_collection.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  fields?: unknown;\n}\ninterface DeleteArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  limit?: unknown;\n  ids?: unknown;\n  since_id?: unknown;\n  title?: unknown;\n  product_id?: unknown;\n  handle?: unknown;\n  updated_at_min?: unknown;\n  updated_at_max?: unknown;\n  published_at_min?: unknown;\n  published_at_max?: unknown;\n  published_status?: unknown;\n  fields?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n  title?: unknown;\n  product_id?: unknown;\n  updated_at_min?: unknown;\n  updated_at_max?: unknown;\n  published_at_min?: unknown;\n  published_at_max?: unknown;\n  published_status?: unknown;\n}\ninterface OrderArgs {\n  [key: string]: unknown;\n  products?: unknown;\n  sort_order?: unknown;\n  body?: {[key: string]: unknown} | null;\n}\n\nexport class SmartCollection extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"id\"], \"path\": \"smart_collections/<id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [], \"path\": \"smart_collections/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"smart_collections.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"smart_collections/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [], \"path\": \"smart_collections.json\"},\n    {\"http_method\": \"put\", \"operation\": \"order\", \"ids\": [\"id\"], \"path\": \"smart_collections/<id>/order.json\"},\n    {\"http_method\": \"put\", \"operation\": \"put\", \"ids\": [\"id\"], \"path\": \"smart_collections/<id>.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"smart_collection\",\n      \"plural\": \"smart_collections\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      fields = null\n    }: FindArgs\n  ): Promise<SmartCollection | null> {\n    const result = await this.baseFind<SmartCollection>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {\"fields\": fields},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async delete(\n    {\n      session,\n      id\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<SmartCollection>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      limit = null,\n      ids = null,\n      since_id = null,\n      title = null,\n      product_id = null,\n      handle = null,\n      updated_at_min = null,\n      updated_at_max = null,\n      published_at_min = null,\n      published_at_max = null,\n      published_status = null,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<SmartCollection>> {\n    const response = await this.baseFind<SmartCollection>({\n      session: session,\n      urlIds: {},\n      params: {\"limit\": limit, \"ids\": ids, \"since_id\": since_id, \"title\": title, \"product_id\": product_id, \"handle\": handle, \"updated_at_min\": updated_at_min, \"updated_at_max\": updated_at_max, \"published_at_min\": published_at_min, \"published_at_max\": published_at_max, \"published_status\": published_status, \"fields\": fields, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      title = null,\n      product_id = null,\n      updated_at_min = null,\n      updated_at_max = null,\n      published_at_min = null,\n      published_at_max = null,\n      published_status = null,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<SmartCollection>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {},\n      params: {\"title\": title, \"product_id\": product_id, \"updated_at_min\": updated_at_min, \"updated_at_max\": updated_at_max, \"published_at_min\": published_at_min, \"published_at_max\": published_at_max, \"published_status\": published_status, ...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public async order(\n    {\n      products = null,\n      sort_order = null,\n      body = null,\n      ...otherArgs\n    }: OrderArgs\n  ): Promise<unknown> {\n    const response = await this.request<SmartCollection>({\n      http_method: \"put\",\n      operation: \"order\",\n      session: this.session,\n      urlIds: {\"id\": this.id},\n      params: {\"products\": products, \"sort_order\": sort_order, ...otherArgs},\n      body: body,\n      entity: this,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public rules: {[key: string]: unknown} | {[key: string]: unknown}[] | null;\n  public title: string | null;\n  public body_html: string | null;\n  public disjunctive: boolean | null;\n  public handle: string | null;\n  public id: number | null;\n  public image: string | {[key: string]: unknown} | null;\n  public published_at: string | null;\n  public published_scope: string | null;\n  public sort_order: string | null;\n  public template_suffix: string | null;\n  public updated_at: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAkDlH,MAAO,eAAgB,SAAQA,SAAI,CAAA;AAChC,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAC;AACtG,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,8BAA8B,EAAC;AAC/F,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,wBAAwB,EAAC;AACvF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAC;AAChG,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,wBAAwB,EAAC;AACzF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,mCAAmC,EAAC;AACxG,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B;KAChG;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,kBAAkB;AAC9B,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,MAAM,GAAG,IAAI,EACJ,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAkB;AAClD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAC;AAC3B,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,MAAM,CACxB,EACE,OAAO,EACP,EAAE,EACS,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAkB;AACnD,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;IAEO,aAAa,GAAG,CACrB,EACE,OAAO,EACP,KAAK,GAAG,IAAI,EACZ,GAAG,GAAG,IAAI,EACV,QAAQ,GAAG,IAAI,EACf,KAAK,GAAG,IAAI,EACZ,UAAU,GAAG,IAAI,EACjB,MAAM,GAAG,IAAI,EACb,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAkB;AACpD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AAC7U,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,aAAa,KAAK,CACvB,EACE,OAAO,EACP,KAAK,GAAG,IAAI,EACZ,UAAU,GAAG,IAAI,EACjB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAkB;AACnD,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,SAAS,EAAC;AACtP,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,MAAM,KAAK,CAChB,EACE,QAAQ,GAAG,IAAI,EACf,UAAU,GAAG,IAAI,EACjB,IAAI,GAAG,IAAI,EACX,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAkB;AACnD,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;YAClB,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAC;AACvB,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,SAAS,EAAC;AACtE,YAAA,IAAI,EAAE,IAAI;AACV,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,KAAK;AACL,IAAA,KAAK;AACL,IAAA,SAAS;AACT,IAAA,WAAW;AACX,IAAA,MAAM;AACN,IAAA,EAAE;AACF,IAAA,KAAK;AACL,IAAA,YAAY;AACZ,IAAA,eAAe;AACf,IAAA,UAAU;AACV,IAAA,eAAe;AACf,IAAA,UAAU;;;;;"}
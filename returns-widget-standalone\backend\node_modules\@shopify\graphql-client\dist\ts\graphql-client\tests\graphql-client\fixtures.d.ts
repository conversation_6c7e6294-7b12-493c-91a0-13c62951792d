import { LogContentTypes, Headers as TypesHeaders } from '../../types';
export declare const clientConfig: {
    url: string;
    headers: {
        'Content-Type': string;
        'X-Shopify-Storefront-Access-Token': string;
    };
};
export declare const operation = "\nquery {\n  shop {\n    name\n  }\n}\n";
export declare const variables: {
    country: string;
};
export declare const defaultHeaders: {
    "X-SDK-Variant": string;
    "X-SDK-Version": string;
    'Content-Type': string;
    'X-Shopify-Storefront-Access-Token': string;
};
export declare function getValidClient({ retries, logger, headers, }?: {
    retries?: number;
    logger?: (logContent: LogContentTypes) => void;
    headers?: TypesHeaders;
}): import("../../types").GraphQLClient;
export declare function createReaderStreamResponse(responseArray: string[]): any;
export declare function createIterableResponse(responseArray: string[]): Response;
export declare function createIterableBufferResponse(responseArray: string[]): Response;
//# sourceMappingURL=fixtures.d.ts.map
#!/usr/bin/env node

/**
 * MCP Server for Sendcloud API Integration
 * Provides tools for GitHub Copilot to interact with Sendcloud shipping services
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

class SendcloudMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'sendcloud-api-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'sendcloud_get_carriers',
            description: 'Get available shipping carriers and methods',
            inputSchema: {
              type: 'object',
              properties: {
                publicKey: {
                  type: 'string',
                  description: 'Sendcloud public key',
                },
                secretKey: {
                  type: 'string',
                  description: 'Sendcloud secret key',
                },
                country: {
                  type: 'string',
                  description: 'Country code (optional, e.g., GB, US)',
                },
              },
              required: ['publicKey', 'secretKey'],
            },
          },
          {
            name: 'sendcloud_create_label',
            description: 'Create a return shipping label',
            inputSchema: {
              type: 'object',
              properties: {
                publicKey: {
                  type: 'string',
                  description: 'Sendcloud public key',
                },
                secretKey: {
                  type: 'string',
                  description: 'Sendcloud secret key',
                },
                parcelData: {
                  type: 'object',
                  description: 'Parcel information',
                  properties: {
                    name: { type: 'string' },
                    email: { type: 'string' },
                    address: { type: 'string' },
                    city: { type: 'string' },
                    postal_code: { type: 'string' },
                    country: { type: 'string' },
                    from_name: { type: 'string' },
                    from_address_1: { type: 'string' },
                    from_city: { type: 'string' },
                    from_postal_code: { type: 'string' },
                    shipment: {
                      type: 'object',
                      properties: {
                        id: { type: 'number' },
                        name: { type: 'string' },
                      },
                    },
                  },
                  required: ['name', 'email', 'address', 'city', 'postal_code', 'country'],
                },
              },
              required: ['publicKey', 'secretKey', 'parcelData'],
            },
          },
          {
            name: 'sendcloud_get_sender_addresses',
            description: 'Get configured sender addresses',
            inputSchema: {
              type: 'object',
              properties: {
                publicKey: {
                  type: 'string',
                  description: 'Sendcloud public key',
                },
                secretKey: {
                  type: 'string',
                  description: 'Sendcloud secret key',
                },
              },
              required: ['publicKey', 'secretKey'],
            },
          },
          {
            name: 'sendcloud_test_connection',
            description: 'Test Sendcloud API connection',
            inputSchema: {
              type: 'object',
              properties: {
                publicKey: {
                  type: 'string',
                  description: 'Sendcloud public key',
                },
                secretKey: {
                  type: 'string',
                  description: 'Sendcloud secret key',
                },
              },
              required: ['publicKey', 'secretKey'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'sendcloud_get_carriers':
            return await this.getCarriers(args);
          case 'sendcloud_create_label':
            return await this.createLabel(args);
          case 'sendcloud_get_sender_addresses':
            return await this.getSenderAddresses(args);
          case 'sendcloud_test_connection':
            return await this.testConnection(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
        };
      }
    });
  }

  getAuthHeader(publicKey, secretKey) {
    return 'Basic ' + Buffer.from(`${publicKey}:${secretKey}`).toString('base64');
  }

  async getCarriers({ publicKey, secretKey, country }) {
    let url = 'https://panel.sendcloud.sc/api/v2/shipping_methods';
    if (country) {
      url += `?sender_country=${country}`;
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': this.getAuthHeader(publicKey, secretKey),
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2),
        },
      ],
    };
  }

  async createLabel({ publicKey, secretKey, parcelData }) {
    const parcel = {
      ...parcelData,
      is_return: false,
      request_label: true,
    };

    const response = await fetch('https://panel.sendcloud.sc/api/v2/parcels', {
      method: 'POST',
      headers: {
        'Authorization': this.getAuthHeader(publicKey, secretKey),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ parcel }),
    });

    const data = await response.json();
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2),
        },
      ],
    };
  }

  async getSenderAddresses({ publicKey, secretKey }) {
    const response = await fetch('https://panel.sendcloud.sc/api/v2/user/addresses/sender', {
      headers: {
        'Authorization': this.getAuthHeader(publicKey, secretKey),
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2),
        },
      ],
    };
  }

  async testConnection({ publicKey, secretKey }) {
    const response = await fetch('https://panel.sendcloud.sc/api/v2/user', {
      headers: {
        'Authorization': this.getAuthHeader(publicKey, secretKey),
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    return {
      content: [
        {
          type: 'text',
          text: `Connection ${response.ok ? 'successful' : 'failed'}: ${JSON.stringify(data, null, 2)}`,
        },
      ],
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Sendcloud MCP server running on stdio');
  }
}

const server = new SendcloudMCPServer();
server.run().catch(console.error);

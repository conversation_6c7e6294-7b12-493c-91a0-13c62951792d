{"version": 3, "file": "session-utils.js", "sources": ["../../../../../../lib/session/session-utils.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\nimport {SESSION_COOKIE_NAME} from '../auth/oauth/types';\nimport {\n  abstractConvertRequest,\n  Cookies,\n  NormalizedResponse,\n} from '../../runtime/http';\nimport {sanitizeShop} from '../utils/shop-validator';\nimport {logger} from '../logger';\nimport * as ShopifyErrors from '../error';\n\nimport {decodeSessionToken} from './decode-session-token';\nimport type {GetCurrentSessionIdParams} from './types';\nimport {Session} from './session';\n\nexport function getJwtSessionId(config: ConfigInterface) {\n  return (shop: string, userId: string): string => {\n    return `${sanitizeShop(config)(shop, true)}_${userId}`;\n  };\n}\n\nexport function getOfflineId(config: ConfigInterface) {\n  return (shop: string): string => {\n    return `offline_${sanitizeShop(config)(shop, true)}`;\n  };\n}\n\nexport function getCurrentSessionId(config: ConfigInterface) {\n  return async function getCurrentSessionId({\n    isOnline,\n    ...adapterArgs\n  }: GetCurrentSessionIdParams): Promise<string | undefined> {\n    const request = await abstractConvertRequest(adapterArgs);\n\n    const log = logger(config);\n\n    if (config.isEmbeddedApp) {\n      log.debug('App is embedded, looking for session id in JWT payload', {\n        isOnline,\n      });\n\n      const authHeader = request.headers.Authorization;\n      if (authHeader) {\n        const matches = (\n          typeof authHeader === 'string' ? authHeader : authHeader[0]\n        ).match(/^Bearer (.+)$/);\n        if (!matches) {\n          log.error('Missing Bearer token in authorization header', {isOnline});\n\n          throw new ShopifyErrors.MissingJwtTokenError(\n            'Missing Bearer token in authorization header',\n          );\n        }\n\n        const jwtPayload = await decodeSessionToken(config)(matches[1]);\n        const shop = jwtPayload.dest.replace(/^https:\\/\\//, '');\n\n        log.debug('Found valid JWT payload', {shop, isOnline});\n\n        if (isOnline) {\n          return getJwtSessionId(config)(shop, jwtPayload.sub);\n        } else {\n          return getOfflineId(config)(shop);\n        }\n      } else {\n        log.error(\n          'Missing Authorization header, was the request made with authenticatedFetch?',\n          {isOnline},\n        );\n      }\n    } else {\n      log.debug('App is not embedded, looking for session id in cookies', {\n        isOnline,\n      });\n\n      const cookies = new Cookies(request, {} as NormalizedResponse, {\n        keys: [config.apiSecretKey],\n      });\n      return cookies.getAndVerify(SESSION_COOKIE_NAME);\n    }\n\n    return undefined;\n  };\n}\n\nexport function customAppSession(config: ConfigInterface) {\n  return (shop: string): Session => {\n    return new Session({\n      id: '',\n      shop: `${sanitizeShop(config)(shop, true)}`,\n      state: '',\n      isOnline: false,\n    });\n  };\n}\n"], "names": ["sanitizeShop", "abstractConvertRequest", "logger", "ShopifyErrors.MissingJwtTokenError", "decodeSessionToken", "cookies", "Cookies", "SESSION_COOKIE_NAME", "Session"], "mappings": ";;;;;;;;;;;AAeM,SAAU,eAAe,CAAC,MAAuB,EAAA;AACrD,IAAA,OAAO,CAAC,IAAY,EAAE,MAAc,KAAY;AAC9C,QAAA,OAAO,CAAA,EAAGA,0BAAY,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,CAAA,EAAI,MAAM,EAAE;AACxD,IAAA,CAAC;AACH;AAEM,SAAU,YAAY,CAAC,MAAuB,EAAA;IAClD,OAAO,CAAC,IAAY,KAAY;QAC9B,OAAO,CAAA,QAAA,EAAWA,0BAAY,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,CAAE;AACtD,IAAA,CAAC;AACH;AAEM,SAAU,mBAAmB,CAAC,MAAuB,EAAA;IACzD,OAAO,eAAe,mBAAmB,CAAC,EACxC,QAAQ,EACR,GAAG,WAAW,EACY,EAAA;AAC1B,QAAA,MAAM,OAAO,GAAG,MAAMC,4BAAsB,CAAC,WAAW,CAAC;AAEzD,QAAA,MAAM,GAAG,GAAGC,cAAM,CAAC,MAAM,CAAC;AAE1B,QAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,YAAA,GAAG,CAAC,KAAK,CAAC,wDAAwD,EAAE;gBAClE,QAAQ;AACT,aAAA,CAAC;AAEF,YAAA,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa;YAChD,IAAI,UAAU,EAAE;gBACd,MAAM,OAAO,GAAG,CACd,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,EAC3D,KAAK,CAAC,eAAe,CAAC;gBACxB,IAAI,CAAC,OAAO,EAAE;oBACZ,GAAG,CAAC,KAAK,CAAC,8CAA8C,EAAE,EAAC,QAAQ,EAAC,CAAC;AAErE,oBAAA,MAAM,IAAIC,0BAAkC,CAC1C,8CAA8C,CAC/C;gBACH;AAEA,gBAAA,MAAM,UAAU,GAAG,MAAMC,qCAAkB,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/D,gBAAA,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;gBAEvD,GAAG,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC,CAAC;gBAEtD,IAAI,QAAQ,EAAE;oBACZ,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC;gBACtD;qBAAO;AACL,oBAAA,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;gBACnC;YACF;iBAAO;gBACL,GAAG,CAAC,KAAK,CACP,6EAA6E,EAC7E,EAAC,QAAQ,EAAC,CACX;YACH;QACF;aAAO;AACL,YAAA,GAAG,CAAC,KAAK,CAAC,wDAAwD,EAAE;gBAClE,QAAQ;AACT,aAAA,CAAC;YAEF,MAAMC,SAAO,GAAG,IAAIC,eAAO,CAAC,OAAO,EAAE,EAAwB,EAAE;AAC7D,gBAAA,IAAI,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC;AAC5B,aAAA,CAAC;AACF,YAAA,OAAOD,SAAO,CAAC,YAAY,CAACE,yBAAmB,CAAC;QAClD;AAEA,QAAA,OAAO,SAAS;AAClB,IAAA,CAAC;AACH;AAEM,SAAU,gBAAgB,CAAC,MAAuB,EAAA;IACtD,OAAO,CAAC,IAAY,KAAa;QAC/B,OAAO,IAAIC,eAAO,CAAC;AACjB,YAAA,EAAE,EAAE,EAAE;YACN,IAAI,EAAE,CAAA,EAAGR,0BAAY,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,CAAE;AAC3C,YAAA,KAAK,EAAE,EAAE;AACT,YAAA,QAAQ,EAAE,KAAK;AAChB,SAAA,CAAC;AACJ,IAAA,CAAC;AACH;;;;;;;"}
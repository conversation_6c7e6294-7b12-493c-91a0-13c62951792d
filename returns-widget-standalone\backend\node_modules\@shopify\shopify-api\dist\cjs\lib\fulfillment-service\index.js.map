{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/fulfillment-service/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\n\nimport {validateFactory} from './validate';\n\nexport function fulfillmentService(config: ConfigInterface) {\n  return {\n    validate: validateFactory(config),\n  };\n}\n\nexport type FulfillmentService = ReturnType<typeof fulfillmentService>;\n"], "names": ["validateFactory"], "mappings": ";;;;AAIM,SAAU,kBAAkB,CAAC,MAAuB,EAAA;IACxD,OAAO;AACL,QAAA,QAAQ,EAAEA,wBAAe,CAAC,MAAM,CAAC;KAClC;AACH;;;;"}
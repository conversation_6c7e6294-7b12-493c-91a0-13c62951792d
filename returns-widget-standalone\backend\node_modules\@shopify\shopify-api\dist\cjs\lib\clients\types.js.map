{"version": 3, "file": "types.js", "sources": ["../../../../../../lib/clients/types.ts"], "sourcesContent": ["import {\n  AllOperations,\n  ApiClientRequestOptions,\n  SearchParams,\n} from '@shopify/admin-api-client';\nimport {Method} from '@shopify/network';\n\nimport {Session} from '../session/session';\nimport type {ApiVersion} from '../types';\nimport {Headers} from '../../runtime/http';\n\nimport {GraphqlClient} from './admin/graphql/client';\nimport {StorefrontClient} from './storefront/client';\nimport type {GraphqlProxy} from './graphql_proxy/types';\nimport {RestClient} from './admin/rest/client';\n\nexport * from './admin/types';\nexport * from './graphql_proxy/types';\n\nexport interface ClientArgs {\n  session: Session;\n  apiVersion?: ApiVersion;\n  retries?: number;\n}\n\n/**\n * Headers to be sent with the request.\n */\nexport type HeaderParams = Record<string, string | number | string[]>;\n\n/* eslint-disable @shopify/typescript/prefer-pascal-case-enums */\nexport enum DataType {\n  JSON = 'application/json',\n  GraphQL = 'application/graphql',\n  URLEncoded = 'application/x-www-form-urlencoded',\n}\n\n/* eslint-enable @shopify/typescript/prefer-pascal-case-enums */\n\nexport interface GetRequestParams {\n  /**\n   * The path to the resource, relative to the API version root.\n   */\n  path: string;\n  /**\n   * The type of data expected in the response.\n   */\n  type?: DataType;\n  /**\n   * The request body.\n   */\n  data?: Record<string, any> | string;\n  /**\n   * Query parameters to be sent with the request.\n   */\n  query?: SearchParams;\n  /**\n   * Additional headers to be sent with the request.\n   */\n  extraHeaders?: HeaderParams;\n  /**\n   * The maximum number of times the request can be made if it fails with a throttling or server error.\n   */\n  tries?: number;\n}\n\nexport type PostRequestParams = GetRequestParams & {\n  data: Record<string, any> | string;\n};\n\nexport type PutRequestParams = PostRequestParams;\n\nexport type DeleteRequestParams = GetRequestParams;\n\nexport type RequestParams = (GetRequestParams | PostRequestParams) & {\n  method: Method;\n};\n\nexport interface RequestReturn<T = unknown> {\n  /**\n   * The response body.\n   */\n  body: T;\n  /**\n   * The response headers.\n   */\n  headers: Headers;\n}\n\nexport type GraphqlParams = Omit<PostRequestParams, 'path' | 'type'>;\n\nexport interface GraphqlClientParams {\n  session: Session;\n  apiVersion?: ApiVersion;\n}\n\nexport interface GraphqlQueryOptions<\n  Operation extends keyof Operations,\n  Operations extends AllOperations,\n> {\n  /**\n   * The variables to include in the operation.\n   */\n  variables?: ApiClientRequestOptions<Operation, Operations>['variables'];\n  /**\n   * Additional headers to be sent with the request.\n   */\n  headers?: Record<string, string | number>;\n  /**\n   * The maximum number of times to retry the request if it fails with a throttling or server error.\n   */\n  retries?: number;\n  /**\n   * An optional AbortSignal to cancel the request.\n   */\n  signal?: AbortSignal;\n}\n\nexport {GraphqlClient} from './admin/graphql/client';\nexport {RestClient} from './admin/rest/client';\n\nexport interface ShopifyClients {\n  Rest: typeof RestClient;\n  Graphql: typeof GraphqlClient;\n  Storefront: typeof StorefrontClient;\n  graphqlProxy: GraphqlProxy;\n}\n"], "names": ["DataType"], "mappings": ";;;;;;;;;AA8BA;AACYA;AAAZ,CAAA,UAAY,QAAQ,EAAA;AAClB,IAAA,QAAA,CAAA,MAAA,CAAA,GAAA,kBAAyB;AACzB,IAAA,QAAA,CAAA,SAAA,CAAA,GAAA,qBAA+B;AAC/B,IAAA,QAAA,CAAA,YAAA,CAAA,GAAA,mCAAgD;AAClD,CAAC,EAJWA,gBAAQ,KAARA,gBAAQ,GAAA,EAAA,CAAA,CAAA;;"}
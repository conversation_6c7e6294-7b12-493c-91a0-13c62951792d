'use strict';

require('../../runtime/crypto/crypto.js');
require('../../runtime/crypto/types.js');
var headers = require('../../runtime/http/headers.js');

async function nodeConvertRequest(adapterArgs) {
    const req = adapterArgs.rawRequest;
    return {
        headers: headers.canonicalizeHeaders({ ...req.headers }),
        method: req.method ?? 'GET',
        // Express.js overrides the url property, so we want to use originalUrl for it
        url: req.originalUrl || req.url,
    };
}
async function nodeConvertIncomingResponse(adapterArgs) {
    return {
        statusCode: adapterArgs.rawResponse.statusCode,
        statusText: adapterArgs.rawResponse.statusMessage,
        headers: headers.canonicalizeHeaders(adapterArgs.rawResponse.getHeaders()),
    };
}
async function nodeConvertAndSendResponse(response, adapterArgs) {
    const res = adapterArgs.rawResponse;
    if (response.headers) {
        await nodeConvertAndSetHeaders(response.headers, adapterArgs);
    }
    if (response.body) {
        res.write(response.body);
    }
    res.statusCode = response.statusCode;
    res.statusMessage = response.statusText;
    res.end();
}
async function nodeConvertAndSetHeaders(headers, adapterArgs) {
    const res = adapterArgs.rawResponse;
    Object.entries(headers).forEach(([header, value]) => res.setHeader(header, value));
}
function nodeRuntimeString() {
    return `Node ${process.version}`;
}

exports.nodeConvertAndSendResponse = nodeConvertAndSendResponse;
exports.nodeConvertAndSetHeaders = nodeConvertAndSetHeaders;
exports.nodeConvertIncomingResponse = nodeConvertIncomingResponse;
exports.nodeConvertRequest = nodeConvertRequest;
exports.nodeRuntimeString = nodeRuntimeString;
//# sourceMappingURL=adapter.js.map

'use strict';

var oauth = require('./oauth/oauth.js');
var nonce = require('./oauth/nonce.js');
var safeCompare = require('./oauth/safe-compare.js');
var getEmbeddedAppUrl = require('./get-embedded-app-url.js');
var tokenExchange = require('./oauth/token-exchange.js');
var clientCredentials = require('./oauth/client-credentials.js');

function shopifyAuth(config) {
    const shopify = {
        begin: oauth.begin(config),
        callback: oauth.callback(config),
        nonce: nonce.nonce,
        safeCompare: safeCompare.safeCompare,
        getEmbeddedAppUrl: getEmbeddedAppUrl.getEmbeddedAppUrl(config),
        buildEmbeddedAppUrl: getEmbeddedAppUrl.buildEmbeddedAppUrl(config),
        tokenExchange: tokenExchange.tokenExchange(config),
        clientCredentials: clientCredentials.clientCredentials(config),
    };
    return shopify;
}

exports.shopifyAuth = shopifyAuth;
//# sourceMappingURL=index.js.map

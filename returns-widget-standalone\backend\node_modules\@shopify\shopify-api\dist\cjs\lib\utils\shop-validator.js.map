{"version": 3, "file": "shop-validator.js", "sources": ["../../../../../../lib/utils/shop-validator.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\nimport {InvalidHostError, InvalidShopError} from '../error';\nimport {decodeHost} from '../auth/decode-host';\n\nimport {shopAdminUrlToLegacyUrl} from './shop-admin-url-helper';\n\nexport function sanitizeShop(config: ConfigInterface) {\n  return (shop: string, throwOnInvalid = false): string | null => {\n    let shopUrl = shop;\n    const domainsRegex = [\n      'myshopify\\\\.com',\n      'shopify\\\\.com',\n      'myshopify\\\\.io',\n      'shop\\\\.dev',\n    ];\n    if (config.customShopDomains) {\n      domainsRegex.push(\n        ...config.customShopDomains.map((regex) =>\n          typeof regex === 'string' ? regex : regex.source,\n        ),\n      );\n    }\n\n    const shopUrlRegex = new RegExp(\n      `^[a-zA-Z0-9][a-zA-Z0-9-_]*\\\\.(${domainsRegex.join('|')})[/]*$`,\n    );\n\n    const shopAdminRegex = new RegExp(\n      `^admin\\\\.(${domainsRegex.join('|')})/store/([a-zA-Z0-9][a-zA-Z0-9-_]*)$`,\n    );\n\n    const isShopAdminUrl = shopAdminRegex.test(shopUrl);\n    if (isShopAdminUrl) {\n      shopUrl = shopAdminUrlToLegacyUrl(shopUrl) || '';\n    }\n\n    const sanitizedShop = shopUrlRegex.test(shopUrl) ? shopUrl : null;\n    if (!sanitizedShop && throwOnInvalid) {\n      throw new InvalidShopError('Received invalid shop argument');\n    }\n\n    return sanitizedShop;\n  };\n}\n\nexport function sanitizeHost() {\n  return (host: string, throwOnInvalid = false): string | null => {\n    const base64regex = /^[0-9a-zA-Z+/]+={0,2}$/;\n\n    let sanitizedHost = base64regex.test(host) ? host : null;\n    if (sanitizedHost) {\n      const {hostname} = new URL(`https://${decodeHost(sanitizedHost)}`);\n\n      const originsRegex = [\n        'myshopify\\\\.com',\n        'shopify\\\\.com',\n        'myshopify\\\\.io',\n        'spin\\\\.dev',\n        'shop\\\\.dev',\n      ];\n\n      const hostRegex = new RegExp(`\\\\.(${originsRegex.join('|')})$`);\n      if (!hostRegex.test(hostname)) {\n        sanitizedHost = null;\n      }\n    }\n    if (!sanitizedHost && throwOnInvalid) {\n      throw new InvalidHostError('Received invalid host argument');\n    }\n\n    return sanitizedHost;\n  };\n}\n"], "names": ["shopAdminUrlToLegacyUrl", "InvalidShopError", "decodeHost", "InvalidHostError"], "mappings": ";;;;;;AAMM,SAAU,YAAY,CAAC,MAAuB,EAAA;AAClD,IAAA,OAAO,CAAC,IAAY,EAAE,cAAc,GAAG,KAAK,KAAmB;QAC7D,IAAI,OAAO,GAAG,IAAI;AAClB,QAAA,MAAM,YAAY,GAAG;YACnB,iBAAiB;YACjB,eAAe;YACf,gBAAgB;YAChB,YAAY;SACb;AACD,QAAA,IAAI,MAAM,CAAC,iBAAiB,EAAE;AAC5B,YAAA,YAAY,CAAC,IAAI,CACf,GAAG,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,KAAK,KACpC,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,CACjD,CACF;QACH;AAEA,QAAA,MAAM,YAAY,GAAG,IAAI,MAAM,CAC7B,CAAA,8BAAA,EAAiC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,MAAA,CAAQ,CAChE;AAED,QAAA,MAAM,cAAc,GAAG,IAAI,MAAM,CAC/B,CAAA,UAAA,EAAa,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,oCAAA,CAAsC,CAC1E;QAED,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;QACnD,IAAI,cAAc,EAAE;AAClB,YAAA,OAAO,GAAGA,0CAAuB,CAAC,OAAO,CAAC,IAAI,EAAE;QAClD;AAEA,QAAA,MAAM,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,IAAI;AACjE,QAAA,IAAI,CAAC,aAAa,IAAI,cAAc,EAAE;AACpC,YAAA,MAAM,IAAIC,sBAAgB,CAAC,gCAAgC,CAAC;QAC9D;AAEA,QAAA,OAAO,aAAa;AACtB,IAAA,CAAC;AACH;SAEgB,YAAY,GAAA;AAC1B,IAAA,OAAO,CAAC,IAAY,EAAE,cAAc,GAAG,KAAK,KAAmB;QAC7D,MAAM,WAAW,GAAG,wBAAwB;AAE5C,QAAA,IAAI,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;QACxD,IAAI,aAAa,EAAE;AACjB,YAAA,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,GAAG,CAAC,CAAA,QAAA,EAAWC,qBAAU,CAAC,aAAa,CAAC,CAAA,CAAE,CAAC;AAElE,YAAA,MAAM,YAAY,GAAG;gBACnB,iBAAiB;gBACjB,eAAe;gBACf,gBAAgB;gBAChB,YAAY;gBACZ,YAAY;aACb;AAED,YAAA,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,CAAA,IAAA,EAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,EAAA,CAAI,CAAC;YAC/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC7B,aAAa,GAAG,IAAI;YACtB;QACF;AACA,QAAA,IAAI,CAAC,aAAa,IAAI,cAAc,EAAE;AACpC,YAAA,MAAM,IAAIC,sBAAgB,CAAC,gCAAgC,CAAC;QAC9D;AAEA,QAAA,OAAO,aAAa;AACtB,IAAA,CAAC;AACH;;;;;"}
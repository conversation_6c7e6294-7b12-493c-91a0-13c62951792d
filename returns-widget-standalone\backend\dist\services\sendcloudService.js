"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendcloudService = void 0;
const axios_1 = __importDefault(require("axios"));
class SendcloudService {
    constructor(publicKey, secretKey) {
        this.baseUrl = 'https://panel.sendcloud.sc/api/v2';
        this.publicKey = publicKey;
        this.secretKey = secretKey;
        if (!this.publicKey || !this.secretKey) {
            throw new Error('Sendcloud API keys are required.');
        }
    }
    getAuthHeader() {
        const credentials = Buffer.from(`${this.publicKey}:${this.secretKey}`).toString('base64');
        return `Basic ${credentials}`;
    }
    async getSenderAddresses() {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/user/addresses/sender`, {
                headers: {
                    'Authorization': this.getAuthHeader(),
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        }
        catch (error) {
            console.error('Error fetching sender addresses:', error);
            throw error;
        }
    }
    async getShippingMethods() {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/shipping_methods`, {
                headers: {
                    'Authorization': this.getAuthHeader(),
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        }
        catch (error) {
            console.error('Error fetching shipping methods:', error);
            throw error;
        }
    }
    async getCarrierOptions() {
        try {
            const shippingMethods = await this.getShippingMethods();
            if (!shippingMethods?.shipping_methods) {
                return { carriers: [], services: {} };
            }
            const carrierGroups = {};
            shippingMethods.shipping_methods.forEach((method) => {
                const carrierName = method.carrier || 'Unknown Carrier';
                const serviceName = method.name || 'Standard Service';
                const price = parseFloat(method.price || '0');
                if (!carrierGroups[carrierName]) {
                    carrierGroups[carrierName] = [];
                }
                const cleanServiceName = serviceName.replace(new RegExp(`^${carrierName}\\s*-?\\s*`, 'i'), '').trim() || serviceName;
                carrierGroups[carrierName].push({
                    id: method.id,
                    serviceName: cleanServiceName,
                    originalName: serviceName,
                    price: price,
                    currency: method.currency || 'EUR',
                    minWeight: parseFloat(method.min_weight || '0'),
                    maxWeight: parseFloat(method.max_weight || '1000'),
                    deliveryTime: method.properties?.delivery_time ||
                        method.service_point_input ||
                        'Standard delivery',
                    countries: method.countries || [],
                    isReturnsSupported: method.service_point_input !== 'required',
                    description: method.description || '',
                    isFree: price === 0
                });
            });
            // Filter to known working carriers
            const knownWorkingIds = [8, 2912, 3639, 28996];
            const validatedServices = {};
            Object.entries(carrierGroups).forEach(([carrierName, carrierServices]) => {
                const validServices = carrierServices.filter(service => knownWorkingIds.includes(service.id));
                if (validServices.length > 0) {
                    validatedServices[carrierName] = validServices.sort((a, b) => {
                        if (a.isFree && !b.isFree)
                            return -1;
                        if (!a.isFree && b.isFree)
                            return 1;
                        return a.price - b.price;
                    });
                }
            });
            const sortedCarriers = Object.keys(validatedServices).sort();
            return {
                carriers: sortedCarriers,
                services: validatedServices
            };
        }
        catch (error) {
            console.error('Error fetching carrier options:', error);
            throw error;
        }
    }
    async createReturnParcel(customerAddress, returnItems, orderName, selectedCarrierId) {
        try {
            const senderAddresses = await this.getSenderAddresses();
            if (!senderAddresses?.sender_addresses?.length) {
                throw new Error('No sender addresses found in Sendcloud account');
            }
            const businessSender = senderAddresses.sender_addresses[0];
            const shippingMethods = await this.getShippingMethods();
            let shipmentId, shipmentName;
            if (selectedCarrierId) {
                const selectedMethod = shippingMethods?.shipping_methods?.find((method) => method.id === selectedCarrierId);
                if (selectedMethod) {
                    shipmentId = selectedMethod.id;
                    shipmentName = selectedMethod.name;
                }
                else {
                    selectedCarrierId = undefined;
                }
            }
            if (!selectedCarrierId || !shipmentId) {
                const suitableMethods = shippingMethods?.shipping_methods?.filter((method) => {
                    const minWeight = parseFloat(method.min_weight || '0');
                    const maxWeight = parseFloat(method.max_weight || '1000');
                    const packageWeight = 0.5;
                    return packageWeight >= minWeight &&
                        packageWeight <= maxWeight &&
                        method.price === 0;
                }) || [];
                if (suitableMethods.length > 0) {
                    shipmentId = suitableMethods[0].id;
                    shipmentName = suitableMethods[0].name;
                }
                else if (shippingMethods?.shipping_methods?.length > 0) {
                    shipmentId = shippingMethods.shipping_methods[0].id;
                    shipmentName = shippingMethods.shipping_methods[0].name;
                }
                else {
                    throw new Error('No shipping methods available in Sendcloud account');
                }
            }
            const totalWeight = returnItems.reduce((sum, item) => sum + (parseFloat(item.weight) * item.quantity), 0);
            const parcelData = {
                name: businessSender.contact_name,
                email: businessSender.email,
                telephone: businessSender.telephone,
                address: businessSender.street + (businessSender.house_number ? ' ' + businessSender.house_number : ''),
                city: businessSender.city,
                postal_code: businessSender.postal_code,
                country: businessSender.country,
                weight: totalWeight.toString(),
                from_name: customerAddress.name,
                from_address_1: customerAddress.address,
                from_city: customerAddress.city,
                from_postal_code: customerAddress.postal_code,
                from_country: customerAddress.country,
                from_email: customerAddress.email,
                from_telephone: customerAddress.telephone || '',
                shipment: {
                    id: shipmentId,
                    name: shipmentName
                },
                is_return: false,
                request_label: true
            };
            if (customerAddress.country !== 'GB') {
                parcelData.customs_shipment_type = 2;
                parcelData.parcel_items = returnItems;
            }
            const response = await axios_1.default.post(`${this.baseUrl}/parcels`, { parcel: parcelData }, {
                headers: {
                    'Authorization': this.getAuthHeader(),
                    'Content-Type': 'application/json'
                }
            });
            const parcel = response.data.parcel;
            if (!parcel?.label?.normal_printer?.[0]) {
                throw new Error('No label URL found in Sendcloud response');
            }
            return parcel.label.normal_printer[0];
        }
        catch (error) {
            console.error('Error creating Sendcloud return parcel:', error);
            if (axios_1.default.isAxiosError(error) && error.response) {
                if ((error.response.status === 412 || error.response.status === 400) && selectedCarrierId && selectedCarrierId !== 8) {
                    return this.createReturnParcel(customerAddress, returnItems, orderName, 8);
                }
            }
            throw new Error(`Failed to create return label: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.SendcloudService = SendcloudService;

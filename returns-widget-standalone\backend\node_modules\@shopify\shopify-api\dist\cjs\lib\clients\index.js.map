{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/clients/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\n\nimport {restClientClass, graphqlClientClass} from './admin';\nimport {storefrontClientClass} from './storefront';\nimport {graphqlProxy} from './graphql_proxy/graphql_proxy';\nimport {ShopifyClients} from './types';\n\nexport type {ShopifyClients} from './types';\n\nexport function clientClasses(config: ConfigInterface): ShopifyClients {\n  return {\n    // We don't pass in the HttpClient because the RestClient inherits from it, and goes through the same setup process\n    Rest: restClientClass({config}),\n    Graphql: graphqlClientClass({config}),\n    Storefront: storefrontClientClass({config}),\n    graphqlProxy: graphqlProxy(config),\n  };\n}\n"], "names": ["restClientClass", "graphqlClientClass", "storefrontClientClass", "graphqlProxy"], "mappings": ";;;;;;;AASM,SAAU,aAAa,CAAC,MAAuB,EAAA;IACnD,OAAO;;AAEL,QAAA,IAAI,EAAEA,wBAAe,CAAC,EAAC,MAAM,EAAC,CAAC;AAC/B,QAAA,OAAO,EAAEC,2BAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;AACrC,QAAA,UAAU,EAAEC,4BAAqB,CAAC,EAAC,MAAM,EAAC,CAAC;AAC3C,QAAA,YAAY,EAAEC,0BAAY,CAAC,MAAM,CAAC;KACnC;AACH;;;;"}
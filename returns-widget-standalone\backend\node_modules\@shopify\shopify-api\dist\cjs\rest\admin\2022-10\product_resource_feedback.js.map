{"version": 3, "file": "product_resource_feedback.js", "sources": ["../../../../../../../rest/admin/2022-10/product_resource_feedback.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  product_id?: number | string | null;\n}\n\nexport class ProductResourceFeedback extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"product_id\"], \"path\": \"products/<product_id>/resource_feedback.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [\"product_id\"], \"path\": \"products/<product_id>/resource_feedback.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"product_resource_feedback\",\n      \"plural\": \"product_resource_feedbacks\"\n    }\n  ];\n\n  protected static getJsonBodyName(): string\n  {\n    return \"resource_feedback\";\n  }\n\n  public static async all(\n    {\n      session,\n      product_id = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<ProductResourceFeedback>> {\n    const response = await this.baseFind<ProductResourceFeedback>({\n      session: session,\n      urlIds: {\"product_id\": product_id},\n      params: {...otherArgs},\n    });\n\n    return response;\n  }\n\n  public created_at: string | null;\n  public feedback_generated_at: string | null;\n  public messages: string[] | null;\n  public product_id: number | null;\n  public resource_id: number | null;\n  public resource_type: string | null;\n  public resource_updated_at: string | null;\n  public state: string | null;\n  public updated_at: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAalH,MAAO,uBAAwB,SAAQA,SAAI,CAAA;AACxC,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,8CAA8C,EAAC;AACzH,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,8CAA8C;KAC3H;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,2BAA2B;AACvC,YAAA,QAAQ,EAAE;AACX;KACF;AAES,IAAA,OAAO,eAAe,GAAA;AAE9B,QAAA,OAAO,mBAAmB;IAC5B;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,UAAU,GAAG,IAAI,EACjB,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAA0B;AAC5D,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,YAAY,EAAE,UAAU,EAAC;AAClC,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACvB,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,UAAU;AACV,IAAA,qBAAqB;AACrB,IAAA,QAAQ;AACR,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,aAAa;AACb,IAAA,mBAAmB;AACnB,IAAA,KAAK;AACL,IAAA,UAAU;;;;;"}
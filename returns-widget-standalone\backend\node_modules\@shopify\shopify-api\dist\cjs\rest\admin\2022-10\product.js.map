{"version": 3, "file": "product.js", "sources": ["../../../../../../../rest/admin/2022-10/product.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\nimport {Image} from './image';\nimport {Variant} from './variant';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  fields?: unknown;\n}\ninterface DeleteArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  ids?: unknown;\n  limit?: unknown;\n  since_id?: unknown;\n  title?: unknown;\n  vendor?: unknown;\n  handle?: unknown;\n  product_type?: unknown;\n  status?: unknown;\n  collection_id?: unknown;\n  created_at_min?: unknown;\n  created_at_max?: unknown;\n  updated_at_min?: unknown;\n  updated_at_max?: unknown;\n  published_at_min?: unknown;\n  published_at_max?: unknown;\n  published_status?: unknown;\n  fields?: unknown;\n  presentment_currencies?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n  vendor?: unknown;\n  product_type?: unknown;\n  collection_id?: unknown;\n  created_at_min?: unknown;\n  created_at_max?: unknown;\n  updated_at_min?: unknown;\n  updated_at_max?: unknown;\n  published_at_min?: unknown;\n  published_at_max?: unknown;\n  published_status?: unknown;\n}\n\nexport class Product extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {\n    \"images\": Image,\n    \"variants\": Variant\n  };\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"id\"], \"path\": \"products/<id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [], \"path\": \"products/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"products.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"products/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [], \"path\": \"products.json\"},\n    {\"http_method\": \"put\", \"operation\": \"put\", \"ids\": [\"id\"], \"path\": \"products/<id>.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"product\",\n      \"plural\": \"products\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      fields = null\n    }: FindArgs\n  ): Promise<Product | null> {\n    const result = await this.baseFind<Product>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {\"fields\": fields},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async delete(\n    {\n      session,\n      id\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<Product>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      ids = null,\n      limit = null,\n      since_id = null,\n      title = null,\n      vendor = null,\n      handle = null,\n      product_type = null,\n      status = null,\n      collection_id = null,\n      created_at_min = null,\n      created_at_max = null,\n      updated_at_min = null,\n      updated_at_max = null,\n      published_at_min = null,\n      published_at_max = null,\n      published_status = null,\n      fields = null,\n      presentment_currencies = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Product>> {\n    const response = await this.baseFind<Product>({\n      session: session,\n      urlIds: {},\n      params: {\"ids\": ids, \"limit\": limit, \"since_id\": since_id, \"title\": title, \"vendor\": vendor, \"handle\": handle, \"product_type\": product_type, \"status\": status, \"collection_id\": collection_id, \"created_at_min\": created_at_min, \"created_at_max\": created_at_max, \"updated_at_min\": updated_at_min, \"updated_at_max\": updated_at_max, \"published_at_min\": published_at_min, \"published_at_max\": published_at_max, \"published_status\": published_status, \"fields\": fields, \"presentment_currencies\": presentment_currencies, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      vendor = null,\n      product_type = null,\n      collection_id = null,\n      created_at_min = null,\n      created_at_max = null,\n      updated_at_min = null,\n      updated_at_max = null,\n      published_at_min = null,\n      published_at_max = null,\n      published_status = null,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<Product>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {},\n      params: {\"vendor\": vendor, \"product_type\": product_type, \"collection_id\": collection_id, \"created_at_min\": created_at_min, \"created_at_max\": created_at_max, \"updated_at_min\": updated_at_min, \"updated_at_max\": updated_at_max, \"published_at_min\": published_at_min, \"published_at_max\": published_at_max, \"published_status\": published_status, ...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public title: string | null;\n  public body_html: string | null;\n  public created_at: string | null;\n  public handle: string | null;\n  public id: number | null;\n  public images: Image[] | null | {[key: string]: any};\n  public options: {[key: string]: unknown} | {[key: string]: unknown}[] | null;\n  public product_type: string | null;\n  public published_at: string | null;\n  public published_scope: string | null;\n  public status: string | null;\n  public tags: string | string[] | null;\n  public template_suffix: string | null;\n  public updated_at: string | null;\n  public variants: Variant[] | null | {[key: string]: any};\n  public vendor: string | null;\n}\n"], "names": ["Base", "ApiVersion", "Image", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;AAAA;;AAEwH;AAwDlH,MAAO,OAAQ,SAAQA,SAAI,CAAA;AACxB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;IAChD,OAAO,OAAO,GAAiC;AACvD,QAAA,QAAQ,EAAEC,WAAK;AACf,QAAA,UAAU,EAAEC;KACb;IACS,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB,EAAC;AAC7F,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,qBAAqB,EAAC;AACtF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,eAAe,EAAC;AAC9E,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB,EAAC;AACvF,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,eAAe,EAAC;AAChF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB;KACvF;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,SAAS;AACrB,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,MAAM,GAAG,IAAI,EACJ,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAU;AAC1C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAC;AAC3B,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,MAAM,CACxB,EACE,OAAO,EACP,EAAE,EACS,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAU;AAC3C,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,GAAG,GAAG,IAAI,EACV,KAAK,GAAG,IAAI,EACZ,QAAQ,GAAG,IAAI,EACf,KAAK,GAAG,IAAI,EACZ,MAAM,GAAG,IAAI,EACb,MAAM,GAAG,IAAI,EACb,YAAY,GAAG,IAAI,EACnB,MAAM,GAAG,IAAI,EACb,aAAa,GAAG,IAAI,EACpB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,MAAM,GAAG,IAAI,EACb,sBAAsB,GAAG,IAAI,EAC7B,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAU;AAC5C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,wBAAwB,EAAE,sBAAsB,EAAE,GAAG,SAAS,EAAC;AAC3gB,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;IAEO,aAAa,KAAK,CACvB,EACE,OAAO,EACP,MAAM,GAAG,IAAI,EACb,YAAY,GAAG,IAAI,EACnB,aAAa,GAAG,IAAI,EACpB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAU;AAC3C,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,SAAS,EAAC;AAChW,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,KAAK;AACL,IAAA,SAAS;AACT,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,EAAE;AACF,IAAA,MAAM;AACN,IAAA,OAAO;AACP,IAAA,YAAY;AACZ,IAAA,YAAY;AACZ,IAAA,eAAe;AACf,IAAA,MAAM;AACN,IAAA,IAAI;AACJ,IAAA,eAAe;AACf,IAAA,UAAU;AACV,IAAA,QAAQ;AACR,IAAA,MAAM;;;;;"}
{"version": 3, "file": "tender_transaction.js", "sources": ["../../../../../../../rest/admin/2022-10/tender_transaction.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  limit?: unknown;\n  since_id?: unknown;\n  processed_at_min?: unknown;\n  processed_at_max?: unknown;\n  processed_at?: unknown;\n  order?: unknown;\n}\n\nexport class TenderTransaction extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"tender_transactions.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"tender_transaction\",\n      \"plural\": \"tender_transactions\"\n    }\n  ];\n\n  public static async all(\n    {\n      session,\n      limit = null,\n      since_id = null,\n      processed_at_min = null,\n      processed_at_max = null,\n      processed_at = null,\n      order = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<TenderTransaction>> {\n    const response = await this.baseFind<TenderTransaction>({\n      session: session,\n      urlIds: {},\n      params: {\"limit\": limit, \"since_id\": since_id, \"processed_at_min\": processed_at_min, \"processed_at_max\": processed_at_max, \"processed_at\": processed_at, \"order\": order, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public amount: string | null;\n  public currency: string | null;\n  public id: number | null;\n  public order_id: number | null;\n  public payment_details: {[key: string]: unknown} | null;\n  public payment_method: string | null;\n  public processed_at: string | null;\n  public remote_reference: string | null;\n  public test: boolean | null;\n  public user_id: number | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAkBlH,MAAO,iBAAkB,SAAQA,SAAI,CAAA;AAClC,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,0BAA0B;KACzF;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,oBAAoB;AAChC,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,KAAK,GAAG,IAAI,EACZ,QAAQ,GAAG,IAAI,EACf,gBAAgB,GAAG,IAAI,EACvB,gBAAgB,GAAG,IAAI,EACvB,YAAY,GAAG,IAAI,EACnB,KAAK,GAAG,IAAI,EACZ,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAoB;AACtD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS,EAAC;AACvL,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,MAAM;AACN,IAAA,QAAQ;AACR,IAAA,EAAE;AACF,IAAA,QAAQ;AACR,IAAA,eAAe;AACf,IAAA,cAAc;AACd,IAAA,YAAY;AACZ,IAAA,gBAAgB;AAChB,IAAA,IAAI;AACJ,IAAA,OAAO;;;;;"}
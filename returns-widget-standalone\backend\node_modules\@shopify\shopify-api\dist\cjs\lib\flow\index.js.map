{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/flow/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\n\nimport {validateFactory} from './validate';\n\nexport function shopifyFlow(config: ConfigInterface) {\n  return {\n    validate: validateFactory(config),\n  };\n}\n\nexport type ShopifyFlow = ReturnType<typeof shopifyFlow>;\n"], "names": ["validateFactory"], "mappings": ";;;;AAIM,SAAU,WAAW,CAAC,MAAuB,EAAA;IACjD,OAAO;AACL,QAAA,QAAQ,EAAEA,wBAAe,CAAC,MAAM,CAAC;KAClC;AACH;;;;"}
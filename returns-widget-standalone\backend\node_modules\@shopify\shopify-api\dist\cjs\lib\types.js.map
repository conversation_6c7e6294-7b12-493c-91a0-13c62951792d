{"version": 3, "file": "types.js", "sources": ["../../../../../lib/types.ts"], "sourcesContent": ["export enum LogSeverity {\n  Error,\n  Warning,\n  Info,\n  Debug,\n}\n\nexport enum ApiVersion {\n  October22 = '2022-10',\n  January23 = '2023-01',\n  April23 = '2023-04',\n  July23 = '2023-07',\n  October23 = '2023-10',\n  January24 = '2024-01',\n  April24 = '2024-04',\n  July24 = '2024-07',\n  October24 = '2024-10',\n  January25 = '2025-01',\n  April25 = '2025-04',\n  July25 = '2025-07',\n  October25 = '2025-10',\n  Unstable = 'unstable',\n}\n\nexport const LIBRARY_NAME = 'Shopify API Library';\nexport const LATEST_API_VERSION = ApiVersion.July25;\nexport const RELEASE_CANDIDATE_API_VERSION = ApiVersion.October25;\n\n/* eslint-disable @shopify/typescript/prefer-pascal-case-enums */\nexport enum ShopifyHeader {\n  AccessToken = 'X-Shopify-Access-Token',\n  ApiVersion = 'X-Shopify-API-Version',\n  Domain = 'X-Shopify-Shop-Domain',\n  Hmac = 'X-Shopify-Hmac-Sha256',\n  Topic = 'X-Shopify-Topic',\n  SubTopic = 'X-Shopify-Sub-Topic',\n  WebhookId = 'X-Shopify-Webhook-Id',\n  StorefrontPrivateToken = 'Shopify-Storefront-Private-Token',\n  StorefrontSDKVariant = 'X-SDK-Variant',\n  StorefrontSDKVersion = 'X-SDK-Version',\n}\n/* eslint-enable @shopify/typescript/prefer-pascal-case-enums */\n\nexport enum ClientType {\n  Rest = 'rest',\n  Graphql = 'graphql',\n}\n\nexport const privacyTopics: string[] = [\n  'CUSTOMERS_DATA_REQUEST',\n  'CUSTOMERS_REDACT',\n  'SHOP_REDACT',\n];\n\nexport enum BillingInterval {\n  OneTime = 'ONE_TIME',\n  Every30Days = 'EVERY_30_DAYS',\n  Annual = 'ANNUAL',\n  Usage = 'USAGE',\n}\n\nexport type RecurringBillingIntervals = Exclude<\n  BillingInterval,\n  BillingInterval.OneTime\n>;\n\nexport enum BillingReplacementBehavior {\n  ApplyImmediately = 'APPLY_IMMEDIATELY',\n  ApplyOnNextBillingCycle = 'APPLY_ON_NEXT_BILLING_CYCLE',\n  Standard = 'STANDARD',\n}\n"], "names": ["LogSeverity", "ApiVersion", "ShopifyHeader", "ClientType", "BillingInterval", "BillingReplacementBehavior"], "mappings": ";;AAAYA;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,WAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACP,CAAC,EALWA,mBAAW,KAAXA,mBAAW,GAAA,EAAA,CAAA,CAAA;AAOXC;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAqB;AACrB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAqB;AACrB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,SAAkB;AAClB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAqB;AACrB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAqB;AACrB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,SAAkB;AAClB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAqB;AACrB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAqB;AACrB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,SAAkB;AAClB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAqB;AACrB,IAAA,UAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACvB,CAAC,EAfWA,kBAAU,KAAVA,kBAAU,GAAA,EAAA,CAAA,CAAA;AAiBf,MAAM,YAAY,GAAG;AACrB,MAAM,kBAAkB,GAAGA,kBAAU,CAAC;AACtC,MAAM,6BAA6B,GAAGA,kBAAU,CAAC;AAExD;AACYC;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,GAAA,wBAAsC;AACtC,IAAA,aAAA,CAAA,YAAA,CAAA,GAAA,uBAAoC;AACpC,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,uBAAgC;AAChC,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,uBAA8B;AAC9B,IAAA,aAAA,CAAA,OAAA,CAAA,GAAA,iBAAyB;AACzB,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,qBAAgC;AAChC,IAAA,aAAA,CAAA,WAAA,CAAA,GAAA,sBAAkC;AAClC,IAAA,aAAA,CAAA,wBAAA,CAAA,GAAA,kCAA2D;AAC3D,IAAA,aAAA,CAAA,sBAAA,CAAA,GAAA,eAAsC;AACtC,IAAA,aAAA,CAAA,sBAAA,CAAA,GAAA,eAAsC;AACxC,CAAC,EAXWA,qBAAa,KAAbA,qBAAa,GAAA,EAAA,CAAA,CAAA;AAYzB;AAEYC;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACrB,CAAC,EAHWA,kBAAU,KAAVA,kBAAU,GAAA,EAAA,CAAA,CAAA;AAKf,MAAM,aAAa,GAAa;IACrC,wBAAwB;IACxB,kBAAkB;IAClB,aAAa;;AAGHC;AAAZ,CAAA,UAAY,eAAe,EAAA;AACzB,IAAA,eAAA,CAAA,SAAA,CAAA,GAAA,UAAoB;AACpB,IAAA,eAAA,CAAA,aAAA,CAAA,GAAA,eAA6B;AAC7B,IAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACjB,CAAC,EALWA,uBAAe,KAAfA,uBAAe,GAAA,EAAA,CAAA,CAAA;AAYfC;AAAZ,CAAA,UAAY,0BAA0B,EAAA;AACpC,IAAA,0BAAA,CAAA,kBAAA,CAAA,GAAA,mBAAsC;AACtC,IAAA,0BAAA,CAAA,yBAAA,CAAA,GAAA,6BAAuD;AACvD,IAAA,0BAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACvB,CAAC,EAJWA,kCAA0B,KAA1BA,kCAA0B,GAAA,EAAA,CAAA,CAAA;;;;;;;"}
{"version": 3, "file": "utils.js", "sources": ["../../../../../../lib/billing/utils.ts"], "sourcesContent": ["import {ActiveSubscriptionLineItem} from './types';\n\n/**\n * Converts string amounts to numbers in Money type objects\n */\nexport function convertMoneyAmount(data: any) {\n  if (!data) return data;\n\n  convertAppUsagePricingMoney(data);\n  convertAppRecurringPricingMoney(data);\n  convertAppDiscountMoney(data);\n\n  return data;\n}\n\nexport function convertAppRecurringPricingMoney(data: any): void {\n  if (!data) return;\n\n  if (data.price?.amount && typeof data.price.amount === 'string') {\n    data.price.amount = parseFloat(data.price.amount);\n  }\n}\n\nexport function convertAppDiscountMoney(data: any): void {\n  if (!data) return;\n\n  if (\n    data.discount?.priceAfterDiscount?.amount &&\n    typeof data.discount.priceAfterDiscount.amount === 'string'\n  ) {\n    data.discount.priceAfterDiscount.amount = parseFloat(\n      data.discount.priceAfterDiscount.amount,\n    );\n  }\n\n  if (\n    data.discount?.value?.amount?.amount &&\n    typeof data.discount.value.amount.amount === 'string'\n  ) {\n    data.discount.value.amount.amount = parseFloat(\n      data.discount.value.amount.amount,\n    );\n  }\n}\n\nexport function convertAppUsagePricingMoney(data: any): void {\n  if (!data) return;\n\n  if (data.balanceUsed?.amount && typeof data.balanceUsed.amount === 'string') {\n    data.balanceUsed.amount = parseFloat(data.balanceUsed.amount);\n  }\n\n  if (\n    data.cappedAmount?.amount &&\n    typeof data.cappedAmount.amount === 'string'\n  ) {\n    data.cappedAmount.amount = parseFloat(data.cappedAmount.amount);\n  }\n}\n\n/**\n * Converts Money amounts in line items\n */\nexport function convertLineItems(lineItems: ActiveSubscriptionLineItem[]) {\n  return lineItems.map((item) => {\n    if (item.plan?.pricingDetails) {\n      item.plan.pricingDetails = convertMoneyAmount(item.plan.pricingDetails);\n    }\n    return item;\n  });\n}\n"], "names": [], "mappings": ";;AAEA;;AAEG;AACG,SAAU,kBAAkB,CAAC,IAAS,EAAA;AAC1C,IAAA,IAAI,CAAC,IAAI;AAAE,QAAA,OAAO,IAAI;IAEtB,2BAA2B,CAAC,IAAI,CAAC;IACjC,+BAA+B,CAAC,IAAI,CAAC;IACrC,uBAAuB,CAAC,IAAI,CAAC;AAE7B,IAAA,OAAO,IAAI;AACb;AAEM,SAAU,+BAA+B,CAAC,IAAS,EAAA;AACvD,IAAA,IAAI,CAAC,IAAI;QAAE;AAEX,IAAA,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE;AAC/D,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACnD;AACF;AAEM,SAAU,uBAAuB,CAAC,IAAS,EAAA;AAC/C,IAAA,IAAI,CAAC,IAAI;QAAE;AAEX,IAAA,IACE,IAAI,CAAC,QAAQ,EAAE,kBAAkB,EAAE,MAAM;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,KAAK,QAAQ,EAC3D;AACA,QAAA,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,GAAG,UAAU,CAClD,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,CACxC;IACH;IAEA,IACE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;AACpC,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,QAAQ,EACrD;QACA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAC5C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAClC;IACH;AACF;AAEM,SAAU,2BAA2B,CAAC,IAAS,EAAA;AACnD,IAAA,IAAI,CAAC,IAAI;QAAE;AAEX,IAAA,IAAI,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,QAAQ,EAAE;AAC3E,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAC/D;AAEA,IAAA,IACE,IAAI,CAAC,YAAY,EAAE,MAAM;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,QAAQ,EAC5C;AACA,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IACjE;AACF;AAEA;;AAEG;AACG,SAAU,gBAAgB,CAAC,SAAuC,EAAA;AACtE,IAAA,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;AAC5B,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE;AAC7B,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;QACzE;AACA,QAAA,OAAO,IAAI;AACb,IAAA,CAAC,CAAC;AACJ;;;;;;;;"}
{"version": 3, "file": "registry.js", "sources": ["../../../../../../lib/webhooks/registry.ts"], "sourcesContent": ["import {InvalidDeliveryMethodError} from '../error';\nimport {ConfigInterface} from '../base-types';\nimport {logger} from '../logger';\n\nimport {\n  AddHandlersParams,\n  DeliveryMethod,\n  WebhookHandler,\n  WebhookRegistry,\n} from './types';\n\nexport function registry(): WebhookRegistry {\n  return {};\n}\n\nexport function topicForStorage(topic: string): string {\n  return topic.toUpperCase().replace(/\\/|\\./g, '_');\n}\n\nexport function addHandlers(\n  config: ConfigInterface,\n  webhookRegistry: WebhookRegistry,\n) {\n  return function addHandlers(handlersToAdd: AddHandlersParams) {\n    for (const [topic, handlers] of Object.entries(handlersToAdd)) {\n      const topicKey = topicForStorage(topic);\n\n      if (Array.isArray(handlers)) {\n        for (const handler of handlers) {\n          mergeOrAddHandler(config, webhookRegistry, topicKey, handler);\n        }\n      } else {\n        mergeOrAddHandler(config, webhookRegistry, topicKey, handlers);\n      }\n    }\n  };\n}\n\nexport function getTopicsAdded(webhookRegistry: WebhookRegistry) {\n  return function getTopicsAdded(): string[] {\n    return Object.keys(webhookRegistry);\n  };\n}\n\nexport function getHandlers(webhookRegistry: WebhookRegistry) {\n  return function getHandlers(topic: string): WebhookHandler[] {\n    return webhookRegistry[topicForStorage(topic)] || [];\n  };\n}\n\nexport function handlerIdentifier(\n  config: ConfigInterface,\n  handler: WebhookHandler,\n): string {\n  const prefix = handler.deliveryMethod;\n\n  switch (handler.deliveryMethod) {\n    case DeliveryMethod.Http:\n      return `${prefix}_${addHostToCallbackUrl(config, handler.callbackUrl)}`;\n    case DeliveryMethod.EventBridge:\n      return `${prefix}_${handler.arn}`;\n    case DeliveryMethod.PubSub:\n      return `${prefix}_${handler.pubSubProject}:${handler.pubSubTopic}`;\n    default:\n      throw new InvalidDeliveryMethodError(\n        `Unrecognized delivery method '${(handler as any).deliveryMethod}'`,\n      );\n  }\n}\n\nexport function addHostToCallbackUrl(\n  config: ConfigInterface,\n  callbackUrl: string,\n): string {\n  if (callbackUrl.startsWith('/')) {\n    return `${config.hostScheme}://${config.hostName}${callbackUrl}`;\n  } else {\n    return callbackUrl;\n  }\n}\n\nfunction mergeOrAddHandler(\n  config: ConfigInterface,\n  webhookRegistry: WebhookRegistry,\n  topic: string,\n  handler: WebhookHandler,\n) {\n  const log = logger(config);\n\n  handler.includeFields?.sort();\n  handler.metafieldNamespaces?.sort();\n\n  if (!(topic in webhookRegistry)) {\n    webhookRegistry[topic] = [handler];\n    return;\n  }\n\n  const identifier = handlerIdentifier(config, handler);\n\n  for (const index in webhookRegistry[topic]) {\n    if (!Object.prototype.hasOwnProperty.call(webhookRegistry[topic], index)) {\n      continue;\n    }\n\n    const existingHandler = webhookRegistry[topic][index];\n    const existingIdentifier = handlerIdentifier(config, existingHandler);\n\n    if (identifier !== existingIdentifier) {\n      continue;\n    }\n\n    if (handler.deliveryMethod === DeliveryMethod.Http) {\n      log.info(\n        `Detected multiple handlers for '${topic}', webhooks.process will call them sequentially`,\n      );\n      break;\n    } else {\n      throw new InvalidDeliveryMethodError(\n        `Can only add multiple handlers for a topic when deliveryMethod is Http. Please be sure that you used addHandler method once after creating ShopifyApi instance in your app.  Invalid handler: ${JSON.stringify(\n          handler,\n        )}`,\n      );\n    }\n  }\n\n  webhookRegistry[topic].push(handler);\n}\n"], "names": ["DeliveryMethod", "InvalidDeliveryMethodError", "logger"], "mappings": ";;;;;;SAWgB,QAAQ,GAAA;AACtB,IAAA,OAAO,EAAE;AACX;AAEM,SAAU,eAAe,CAAC,KAAa,EAAA;IAC3C,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;AACnD;AAEM,SAAU,WAAW,CACzB,MAAuB,EACvB,eAAgC,EAAA;IAEhC,OAAO,SAAS,WAAW,CAAC,aAAgC,EAAA;AAC1D,QAAA,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAC7D,YAAA,MAAM,QAAQ,GAAG,eAAe,CAAC,KAAK,CAAC;AAEvC,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC3B,gBAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;oBAC9B,iBAAiB,CAAC,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,CAAC;gBAC/D;YACF;iBAAO;gBACL,iBAAiB,CAAC,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAChE;QACF;AACF,IAAA,CAAC;AACH;AAEM,SAAU,cAAc,CAAC,eAAgC,EAAA;AAC7D,IAAA,OAAO,SAAS,cAAc,GAAA;AAC5B,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;AACrC,IAAA,CAAC;AACH;AAEM,SAAU,WAAW,CAAC,eAAgC,EAAA;IAC1D,OAAO,SAAS,WAAW,CAAC,KAAa,EAAA;QACvC,OAAO,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;AACtD,IAAA,CAAC;AACH;AAEM,SAAU,iBAAiB,CAC/B,MAAuB,EACvB,OAAuB,EAAA;AAEvB,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc;AAErC,IAAA,QAAQ,OAAO,CAAC,cAAc;QAC5B,KAAKA,oBAAc,CAAC,IAAI;AACtB,YAAA,OAAO,CAAA,EAAG,MAAM,CAAA,CAAA,EAAI,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,CAAA,CAAE;QACzE,KAAKA,oBAAc,CAAC,WAAW;AAC7B,YAAA,OAAO,GAAG,MAAM,CAAA,CAAA,EAAI,OAAO,CAAC,GAAG,EAAE;QACnC,KAAKA,oBAAc,CAAC,MAAM;YACxB,OAAO,CAAA,EAAG,MAAM,CAAA,CAAA,EAAI,OAAO,CAAC,aAAa,CAAA,CAAA,EAAI,OAAO,CAAC,WAAW,CAAA,CAAE;AACpE,QAAA;YACE,MAAM,IAAIC,gCAA0B,CAClC,CAAA,8BAAA,EAAkC,OAAe,CAAC,cAAc,CAAA,CAAA,CAAG,CACpE;;AAEP;AAEM,SAAU,oBAAoB,CAClC,MAAuB,EACvB,WAAmB,EAAA;AAEnB,IAAA,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC/B,OAAO,CAAA,EAAG,MAAM,CAAC,UAAU,CAAA,GAAA,EAAM,MAAM,CAAC,QAAQ,CAAA,EAAG,WAAW,CAAA,CAAE;IAClE;SAAO;AACL,QAAA,OAAO,WAAW;IACpB;AACF;AAEA,SAAS,iBAAiB,CACxB,MAAuB,EACvB,eAAgC,EAChC,KAAa,EACb,OAAuB,EAAA;AAEvB,IAAA,MAAM,GAAG,GAAGC,YAAM,CAAC,MAAM,CAAC;AAE1B,IAAA,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE;AAC7B,IAAA,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;AAEnC,IAAA,IAAI,EAAE,KAAK,IAAI,eAAe,CAAC,EAAE;AAC/B,QAAA,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;QAClC;IACF;IAEA,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC;IAErD,KAAK,MAAM,KAAK,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;AAC1C,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE;YACxE;QACF;QAEA,MAAM,eAAe,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QACrD,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,MAAM,EAAE,eAAe,CAAC;AAErE,QAAA,IAAI,UAAU,KAAK,kBAAkB,EAAE;YACrC;QACF;QAEA,IAAI,OAAO,CAAC,cAAc,KAAKF,oBAAc,CAAC,IAAI,EAAE;AAClD,YAAA,GAAG,CAAC,IAAI,CACN,mCAAmC,KAAK,CAAA,+CAAA,CAAiD,CAC1F;YACD;QACF;aAAO;AACL,YAAA,MAAM,IAAIC,gCAA0B,CAClC,CAAA,8LAAA,EAAiM,IAAI,CAAC,SAAS,CAC7M,OAAO,CACR,CAAA,CAAE,CACJ;QACH;IACF;IAEA,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AACtC;;;;;;;;;;"}
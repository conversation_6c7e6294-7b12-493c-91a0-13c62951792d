{"version": 3, "file": "price_rule.js", "sources": ["../../../../../../../rest/admin/2022-10/price_rule.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n}\ninterface DeleteArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  limit?: unknown;\n  since_id?: unknown;\n  created_at_min?: unknown;\n  created_at_max?: unknown;\n  updated_at_min?: unknown;\n  updated_at_max?: unknown;\n  starts_at_min?: unknown;\n  starts_at_max?: unknown;\n  ends_at_min?: unknown;\n  ends_at_max?: unknown;\n  times_used?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n}\n\nexport class PriceRule extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"id\"], \"path\": \"price_rules/<id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [], \"path\": \"price_rules/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"price_rules.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"price_rules/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [], \"path\": \"price_rules.json\"},\n    {\"http_method\": \"put\", \"operation\": \"put\", \"ids\": [\"id\"], \"path\": \"price_rules/<id>.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"price_rule\",\n      \"plural\": \"price_rules\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id\n    }: FindArgs\n  ): Promise<PriceRule | null> {\n    const result = await this.baseFind<PriceRule>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async delete(\n    {\n      session,\n      id\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<PriceRule>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      limit = null,\n      since_id = null,\n      created_at_min = null,\n      created_at_max = null,\n      updated_at_min = null,\n      updated_at_max = null,\n      starts_at_min = null,\n      starts_at_max = null,\n      ends_at_min = null,\n      ends_at_max = null,\n      times_used = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<PriceRule>> {\n    const response = await this.baseFind<PriceRule>({\n      session: session,\n      urlIds: {},\n      params: {\"limit\": limit, \"since_id\": since_id, \"created_at_min\": created_at_min, \"created_at_max\": created_at_max, \"updated_at_min\": updated_at_min, \"updated_at_max\": updated_at_max, \"starts_at_min\": starts_at_min, \"starts_at_max\": starts_at_max, \"ends_at_min\": ends_at_min, \"ends_at_max\": ends_at_max, \"times_used\": times_used, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<PriceRule>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {},\n      params: {...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public allocation_limit: number | null;\n  public allocation_method: string | null;\n  public created_at: string | null;\n  public customer_segment_prerequisite_ids: number[] | null;\n  public customer_selection: string | null;\n  public ends_at: string | null;\n  public entitled_collection_ids: number[] | null;\n  public entitled_country_ids: number[] | null;\n  public entitled_product_ids: number[] | null;\n  public entitled_variant_ids: number[] | null;\n  public id: number | null;\n  public once_per_customer: boolean | null;\n  public prerequisite_collection_ids: number[] | null;\n  public prerequisite_customer_ids: number[] | null;\n  public prerequisite_product_ids: number[] | null;\n  public prerequisite_quantity_range: {[key: string]: unknown} | null;\n  public prerequisite_shipping_price_range: {[key: string]: unknown} | null;\n  public prerequisite_subtotal_range: {[key: string]: unknown} | null;\n  public prerequisite_to_entitlement_purchase: {[key: string]: unknown} | null;\n  public prerequisite_to_entitlement_quantity_ratio: {[key: string]: unknown} | null;\n  public prerequisite_variant_ids: number[] | null;\n  public starts_at: string | null;\n  public target_selection: string | null;\n  public target_type: string | null;\n  public title: string | null;\n  public updated_at: string | null;\n  public usage_limit: number | null;\n  public value: string | null;\n  public value_type: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAmClH,MAAO,SAAU,SAAQA,SAAI,CAAA;AAC1B,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,uBAAuB,EAAC;AAChG,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,wBAAwB,EAAC;AACzF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAC;AACjF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,uBAAuB,EAAC;AAC1F,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAC;AACnF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,uBAAuB;KAC1F;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,YAAY;AACxB,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACO,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAY;AAC5C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,MAAM,CACxB,EACE,OAAO,EACP,EAAE,EACS,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAY;AAC7C,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;IAEO,aAAa,GAAG,CACrB,EACE,OAAO,EACP,KAAK,GAAG,IAAI,EACZ,QAAQ,GAAG,IAAI,EACf,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,aAAa,GAAG,IAAI,EACpB,aAAa,GAAG,IAAI,EACpB,WAAW,GAAG,IAAI,EAClB,WAAW,GAAG,IAAI,EAClB,UAAU,GAAG,IAAI,EACjB,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAY;AAC9C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,SAAS,EAAC;AACvV,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;IAEO,aAAa,KAAK,CACvB,EACE,OAAO,EACP,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAY;AAC7C,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACtB,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,gBAAgB;AAChB,IAAA,iBAAiB;AACjB,IAAA,UAAU;AACV,IAAA,iCAAiC;AACjC,IAAA,kBAAkB;AAClB,IAAA,OAAO;AACP,IAAA,uBAAuB;AACvB,IAAA,oBAAoB;AACpB,IAAA,oBAAoB;AACpB,IAAA,oBAAoB;AACpB,IAAA,EAAE;AACF,IAAA,iBAAiB;AACjB,IAAA,2BAA2B;AAC3B,IAAA,yBAAyB;AACzB,IAAA,wBAAwB;AACxB,IAAA,2BAA2B;AAC3B,IAAA,iCAAiC;AACjC,IAAA,2BAA2B;AAC3B,IAAA,oCAAoC;AACpC,IAAA,0CAA0C;AAC1C,IAAA,wBAAwB;AACxB,IAAA,SAAS;AACT,IAAA,gBAAgB;AAChB,IAAA,WAAW;AACX,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,KAAK;AACL,IAAA,UAAU;;;;;"}
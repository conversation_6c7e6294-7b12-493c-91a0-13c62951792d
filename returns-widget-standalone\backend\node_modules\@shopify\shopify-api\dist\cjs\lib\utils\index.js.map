{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/utils/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\n\nimport {sanitizeShop, sanitizeHost} from './shop-validator';\nimport {validateHmac} from './hmac-validator';\nimport {versionCompatible, versionPriorTo} from './version-compatible';\nimport {\n  shopAdminUrlToLegacyUrl,\n  legacyUrlToShopAdminUrl,\n} from './shop-admin-url-helper';\n\nexport function shopifyUtils(config: ConfigInterface) {\n  return {\n    sanitizeShop: sanitizeShop(config),\n    sanitizeHost: sanitizeHost(),\n    validateHmac: validateHmac(config),\n    versionCompatible: versionCompatible(config),\n    versionPriorTo: versionPriorTo(config),\n    shopAdminUrlToLegacyUrl,\n    legacyUrlToShopAdminUrl,\n  };\n}\n\nexport type ShopifyUtils = ReturnType<typeof shopifyUtils>;\n"], "names": ["sanitizeShop", "sanitizeHost", "validateHmac", "versionCompatible", "versionPriorTo", "shopAdminUrlToLegacyUrl", "legacyUrlToShopAdminUrl"], "mappings": ";;;;;;;AAUM,SAAU,YAAY,CAAC,MAAuB,EAAA;IAClD,OAAO;AACL,QAAA,YAAY,EAAEA,0BAAY,CAAC,MAAM,CAAC;QAClC,YAAY,EAAEC,0BAAY,EAAE;AAC5B,QAAA,YAAY,EAAEC,0BAAY,CAAC,MAAM,CAAC;AAClC,QAAA,iBAAiB,EAAEC,mCAAiB,CAAC,MAAM,CAAC;AAC5C,QAAA,cAAc,EAAEC,gCAAc,CAAC,MAAM,CAAC;iCACtCC,0CAAuB;iCACvBC,0CAAuB;KACxB;AACH;;;;"}
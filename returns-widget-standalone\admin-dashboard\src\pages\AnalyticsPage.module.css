/* Analytics Page Styles */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.header p {
  color: #6b7280;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 0.75rem;
}

.exportButton,
.refreshButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.exportButton:hover,
.refreshButton:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.refreshButton {
  padding: 0.75rem;
}

/* Date Controls */
.dateControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.quickFilters {
  display: flex;
  gap: 0.5rem;
}

.quickFilter {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.quickFilter:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.dateInputs {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dateInput {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.dateInputs span {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Metrics Grid */
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.metricIcon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 1rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metricContent {
  flex: 1;
}

.metricContent h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metricValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.metricChange {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.metricSubtext {
  font-size: 0.875rem;
  color: #6b7280;
}

.positive {
  color: #10b981;
}

.negative {
  color: #ef4444;
}

/* Chart Section */
.chartSection {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chartHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chartHeader h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chartControls {
  display: flex;
  gap: 0.5rem;
}

.chartButton {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.chartButton:hover {
  background: #f9fafb;
}

.chartButton.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.chartContainer {
  width: 100%;
  height: 300px;
}

/* Charts Grid */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.chartCard,
.tableCard {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chartCard h3,
.tableCard h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

/* Table Styles */
.table {
  width: 100%;
}

.tableHeader {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 2px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tableRow {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
  font-size: 0.875rem;
  align-items: center;
}

.tableRow:last-child {
  border-bottom: none;
}

.itemTitle,
.customerEmail {
  font-weight: 500;
  color: #1f2937;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.customerEmail {
  color: #3b82f6;
}

/* Loading and Error States */
.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;
}

.error {
  color: #dc2626;
}

.error h3 {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.error p {
  margin: 0 0 1.5rem 0;
}

.retryButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s;
}

.retryButton:hover {
  background: #2563eb;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .dateControls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .quickFilters {
    flex-wrap: wrap;
  }

  .metricsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metricCard {
    padding: 1rem;
  }

  .metricValue {
    font-size: 1.5rem;
  }

  .chartSection {
    padding: 1rem;
  }

  .chartHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .tableHeader,
  .tableRow {
    grid-template-columns: 1.5fr 0.75fr 0.75fr;
    gap: 0.5rem;
  }
}

{"version": 3, "file": "graphql_proxy.js", "sources": ["../../../../../../../lib/clients/graphql_proxy/graphql_proxy.ts"], "sourcesContent": ["import {ConfigInterface} from '../../base-types';\nimport * as ShopifyErrors from '../../error';\nimport {graphqlClientClass} from '../admin';\n\nimport {GraphqlProxy} from './types';\n\nexport function graphqlProxy(config: ConfigInterface): GraphqlProxy {\n  return async ({session, rawBody}) => {\n    if (!session.accessToken) {\n      throw new ShopifyErrors.InvalidSession(\n        'Cannot proxy query. Session not authenticated.',\n      );\n    }\n\n    const GraphqlClient = graphqlClientClass({config});\n    const client = new GraphqlClient({session});\n\n    let query: string;\n    let variables: Record<string, any> | undefined;\n    if (typeof rawBody === 'string') {\n      query = rawBody;\n    } else {\n      query = rawBody.query;\n      variables = rawBody.variables;\n    }\n\n    if (!query) {\n      throw new ShopifyErrors.MissingRequiredArgument('Query missing.');\n    }\n\n    const response = await client.request(query, {variables});\n\n    return {body: response, headers: {}};\n  };\n}\n"], "names": ["ShopifyErrors.InvalidSession", "graphqlClientClass", "client", "ShopifyErrors.MissingRequiredArgument"], "mappings": ";;;;;;;;;;;AAMM,SAAU,YAAY,CAAC,MAAuB,EAAA;IAClD,OAAO,OAAO,EAAC,OAAO,EAAE,OAAO,EAAC,KAAI;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AACxB,YAAA,MAAM,IAAIA,oBAA4B,CACpC,gDAAgD,CACjD;QACH;QAEA,MAAM,aAAa,GAAGC,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;QAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;AAE3C,QAAA,IAAI,KAAa;AACjB,QAAA,IAAI,SAA0C;AAC9C,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,KAAK,GAAG,OAAO;QACjB;aAAO;AACL,YAAA,KAAK,GAAG,OAAO,CAAC,KAAK;AACrB,YAAA,SAAS,GAAG,OAAO,CAAC,SAAS;QAC/B;QAEA,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,MAAM,IAAIC,6BAAqC,CAAC,gBAAgB,CAAC;QACnE;AAEA,QAAA,MAAM,QAAQ,GAAG,MAAMD,QAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAC,SAAS,EAAC,CAAC;QAEzD,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAC;AACtC,IAAA,CAAC;AACH;;;;"}
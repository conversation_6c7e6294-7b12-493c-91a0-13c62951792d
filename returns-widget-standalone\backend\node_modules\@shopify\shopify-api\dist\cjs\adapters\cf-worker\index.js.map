{"version": 3, "file": "index.js", "sources": ["../../../../../../adapters/cf-worker/index.ts"], "sourcesContent": ["import {\n  setAbstractFetchFunc,\n  setAbstractConvertRequestFunc,\n  setAbstractConvertResponseFunc,\n  setAbstractConvertHeadersFunc,\n  setAbstractRuntimeString,\n} from '../../runtime';\nimport {\n  webApiConvertHeaders,\n  webApiConvertRequest,\n  webApiConvertResponse,\n} from '../web-api/adapter';\n\nimport {workerRuntimeString} from './adapter';\n\nsetAbstractFetchFunc(fetch);\nsetAbstractConvertRequestFunc(webApiConvertRequest);\nsetAbstractConvertResponseFunc(webApiConvertResponse);\nsetAbstractConvertHeadersFunc(webApiConvertHeaders);\nsetAbstractRuntimeString(workerRuntimeString);\n"], "names": ["setAbstractFetchFunc", "setAbstractConvertRequestFunc", "webApiConvertRequest", "setAbstractConvertResponseFunc", "webApiConvertResponse", "setAbstractConvertHeadersFunc", "webApiConvertHeaders", "setAbstractRuntimeString", "workerRuntimeString"], "mappings": ";;;;;;;;;AAeAA,0BAAoB,CAAC,KAAK,CAAC;AAC3BC,mCAA6B,CAACC,4BAAoB,CAAC;AACnDC,oCAA8B,CAACC,6BAAqB,CAAC;AACrDC,mCAA6B,CAACC,4BAAoB,CAAC;AACnDC,sCAAwB,CAACC,6BAAmB,CAAC;;"}
{"version": 3, "file": "adapter.js", "sources": ["../../../../../../adapters/mock/adapter.ts"], "sourcesContent": ["import {\n  Headers as FetchHeaders,\n  Request,\n  RequestInit,\n  Response,\n} from 'node-fetch';\n\nimport {\n  AbstractFetchFunc,\n  AdapterArgs,\n  AdapterHeaders,\n  canonicalizeHeaders,\n  Headers,\n  NormalizedRequest,\n  NormalizedResponse,\n} from '../../runtime/http';\n\nimport {mockTestRequests} from './mock_test_requests';\nimport {mockRequestCapture} from './mock_request_capture';\n\ninterface MockAdapterArgs extends AdapterArgs {\n  rawRequest: NormalizedRequest;\n}\n\nexport async function mockConvertRequest(\n  adapterArgs: MockAdapterArgs,\n): Promise<NormalizedRequest> {\n  return Promise.resolve(adapterArgs.rawRequest);\n}\n\nexport async function mockConvertResponse(\n  response: NormalizedResponse,\n  _adapterArgs: MockAdapterArgs,\n): Promise<NormalizedResponse> {\n  return Promise.resolve(response);\n}\n\nexport async function mockConvertHeaders(\n  headers: Headers,\n  _adapterArgs: MockAdapterArgs,\n): Promise<AdapterHeaders> {\n  return Promise.resolve(headers);\n}\n\nexport const mockFetch: AbstractFetchFunc = async (url, init) => {\n  const mockInit = init as RequestInit;\n\n  // Capture the init object for testing\n  mockRequestCapture.lastRequestInit = mockInit;\n\n  const request = new Request(url as string, mockInit);\n  const headers = Object.fromEntries(\n    new FetchHeaders(mockInit?.headers).entries(),\n  );\n\n  mockTestRequests.requestList.push({\n    url: request.url,\n    method: request.method,\n    headers: canonicalizeHeaders(headers),\n    body: await request.text(),\n  });\n\n  const next = mockTestRequests.responseList.shift()!;\n  if (!next) {\n    throw new Error(\n      `Missing mock for ${request.method} to ${url}, have you queued all required responses?`,\n    );\n  }\n  if (next instanceof Error) {\n    throw next;\n  }\n\n  const responseHeaders = new FetchHeaders();\n  Object.entries(next.headers ?? {}).forEach(([key, value]) => {\n    responseHeaders.set(\n      key,\n      typeof value === 'string' ? value : value.join(', '),\n    );\n  });\n\n  return new Response(next.body, {\n    status: next.statusCode,\n    statusText: next.statusText,\n    headers: responseHeaders as any,\n  }) as any;\n};\n\nexport function mockRuntimeString() {\n  return 'Mock adapter';\n}\n"], "names": ["Request", "headers", "FetchHeaders", "mockTestRequests", "canonicalizeHeaders", "Response"], "mappings": ";;;;;;;;AAwBO,eAAe,kBAAkB,CACtC,WAA4B,EAAA;IAE5B,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC;AAChD;AAEO,eAAe,mBAAmB,CACvC,QAA4B,EAC5B,YAA6B,EAAA;AAE7B,IAAA,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;AAClC;AAEO,eAAe,kBAAkB,CACtC,OAAgB,EAChB,YAA6B,EAAA;AAE7B,IAAA,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AACjC;AAEO,MAAM,SAAS,GAAsB,OAAO,GAAG,EAAE,IAAI,KAAI;IAC9D,MAAM,QAAQ,GAAG,IAAmB;IAKpC,MAAM,OAAO,GAAG,IAAIA,aAAO,CAAC,GAAa,EAAE,QAAQ,CAAC;AACpD,IAAA,MAAMC,SAAO,GAAG,MAAM,CAAC,WAAW,CAChC,IAAIC,aAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,CAC9C;AAED,IAAAC,mCAAgB,CAAC,WAAW,CAAC,IAAI,CAAC;QAChC,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,MAAM,EAAE,OAAO,CAAC,MAAM;AACtB,QAAA,OAAO,EAAEC,2BAAmB,CAACH,SAAO,CAAC;AACrC,QAAA,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3B,KAAA,CAAC;IAEF,MAAM,IAAI,GAAGE,mCAAgB,CAAC,YAAY,CAAC,KAAK,EAAG;IACnD,IAAI,CAAC,IAAI,EAAE;QACT,MAAM,IAAI,KAAK,CACb,CAAA,iBAAA,EAAoB,OAAO,CAAC,MAAM,CAAA,IAAA,EAAO,GAAG,CAAA,yCAAA,CAA2C,CACxF;IACH;AACA,IAAA,IAAI,IAAI,YAAY,KAAK,EAAE;AACzB,QAAA,MAAM,IAAI;IACZ;AAEA,IAAA,MAAM,eAAe,GAAG,IAAID,aAAY,EAAE;AAC1C,IAAA,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;QAC1D,eAAe,CAAC,GAAG,CACjB,GAAG,EACH,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CACrD;AACH,IAAA,CAAC,CAAC;AAEF,IAAA,OAAO,IAAIG,cAAQ,CAAC,IAAI,CAAC,IAAI,EAAE;QAC7B,MAAM,EAAE,IAAI,CAAC,UAAU;QACvB,UAAU,EAAE,IAAI,CAAC,UAAU;AAC3B,QAAA,OAAO,EAAE,eAAsB;AAChC,KAAA,CAAQ;AACX;SAEgB,iBAAiB,GAAA;AAC/B,IAAA,OAAO,cAAc;AACvB;;;;;;;;"}
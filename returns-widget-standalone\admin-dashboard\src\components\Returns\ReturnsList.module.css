.container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.header h2 {
  margin: 0;
  color: #111827;
  font-size: 24px;
  font-weight: 600;
}

.filterTabs {
  display: flex;
  gap: 8px;
  background: #f3f4f6;
  padding: 4px;
  border-radius: 8px;
}

.filterTab {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s;
}

.filterTab:hover {
  color: #374151;
  background: rgba(255, 255, 255, 0.5);
}

.filterTab.active {
  background: white;
  color: #2563eb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  color: #6b7280;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  color: #ef4444;
  text-align: center;
}

.error p {
  margin: 16px 0;
}

.retryButton {
  padding: 8px 16px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background: #1d4ed8;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  color: #6b7280;
  text-align: center;
}

.empty svg {
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty h3 {
  margin: 0 0 8px 0;
  color: #374151;
}

.empty p {
  margin: 0;
}

.returnsList {
  display: grid;
  gap: 16px;
}

.returnCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.returnCard:hover {
  border-color: #2563eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.returnHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.returnInfo h4 {
  margin: 0 0 4px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
}

.orderInfo {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusPending {
  background: #fef3c7;
  color: #d97706;
}

.statusApproved {
  background: #d1fae5;
  color: #059669;
}

.statusRejected {
  background: #fee2e2;
  color: #dc2626;
}

.statusCompleted {
  background: #dbeafe;
  color: #2563eb;
}

.statusDefault {
  background: #f3f4f6;
  color: #6b7280;
}

.statusIconPending,
.statusIconApproved,
.statusIconRejected,
.statusIconCompleted,
.statusIconDefault {
  width: 14px;
  height: 14px;
}

.returnDetails {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 14px;
}

.customer,
.items {
  color: #374151;
}

.date {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
}

.tracking {
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
  font-size: 14px;
  color: #374151;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filterTabs {
    width: 100%;
    overflow-x: auto;
  }

  .returnDetails {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .returnHeader {
    flex-direction: column;
    gap: 12px;
  }
}

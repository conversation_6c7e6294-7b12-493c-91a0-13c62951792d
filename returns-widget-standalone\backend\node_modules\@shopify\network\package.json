{"name": "@shopify/network", "version": "3.3.0", "license": "MIT", "description": "Common values related to dealing with the network", "main": "index.js", "types": "./build/ts/index.d.ts", "sideEffects": false, "publishConfig": {"access": "public", "@shopify:registry": "https://registry.npmjs.org"}, "author": "Shopify Inc.", "repository": {"type": "git", "url": "git+https://github.com/Shopify/quilt.git", "directory": "packages/network"}, "bugs": {"url": "https://github.com/Shopify/quilt/issues"}, "homepage": "https://github.com/Shopify/quilt/blob/main/packages/network/README.md", "engines": {"node": ">=18.12.0"}, "files": ["build/", "!build/*.tsbuildinfo", "!build/ts/**/tests/", "index.js", "index.mjs", "index.esnext"], "module": "index.mjs", "esnext": "index.esnext", "exports": {".": {"types": "./build/ts/index.d.ts", "esnext": "./index.esnext", "import": "./index.mjs", "require": "./index.js"}}}
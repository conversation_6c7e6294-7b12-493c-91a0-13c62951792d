'use strict';

var network = require('@shopify/network');
var index$1 = require('../../runtime/http/index.js');
var error = require('../error.js');
var index = require('../logger/index.js');
var types = require('./types.js');
var validate = require('./validate.js');

const STATUS_TEXT_LOOKUP = {
    [network.StatusCode.Ok]: 'OK',
    [network.StatusCode.BadRequest]: 'Bad Request',
    [network.StatusCode.Unauthorized]: 'Unauthorized',
    [network.StatusCode.NotFound]: 'Not Found',
    [network.StatusCode.InternalServerError]: 'Internal Server Error',
};
function process(config, webhookRegistry) {
    return async function process({ context, rawBody, ...adapterArgs }) {
        const response = {
            statusCode: network.StatusCode.Ok,
            statusText: STATUS_TEXT_LOOKUP[network.StatusCode.Ok],
            headers: {},
        };
        await index.logger(config).info('Receiving webhook request');
        const webhookCheck = await validate.validateFactory(config)({
            rawBody,
            ...adapterArgs,
        });
        let errorMessage = 'Unknown error while handling webhook';
        if (webhookCheck.valid) {
            const handlerResult = await callWebhookHandlers(config, webhookRegistry, webhookCheck, rawBody, context);
            response.statusCode = handlerResult.statusCode;
            if (!index$1.isOK(response)) {
                errorMessage = handlerResult.errorMessage || errorMessage;
            }
        }
        else {
            const errorResult = await handleInvalidWebhook(config, webhookCheck);
            response.statusCode = errorResult.statusCode;
            response.statusText = STATUS_TEXT_LOOKUP[response.statusCode];
            errorMessage = errorResult.errorMessage;
        }
        const returnResponse = await index$1.abstractConvertResponse(response, adapterArgs);
        if (!index$1.isOK(response)) {
            throw new error.InvalidWebhookError({
                message: errorMessage,
                response: returnResponse,
            });
        }
        return Promise.resolve(returnResponse);
    };
}
async function callWebhookHandlers(config, webhookRegistry, webhookCheck, rawBody, context) {
    const log = index.logger(config);
    const { hmac: _hmac, valid: _valid, ...loggingContext } = webhookCheck;
    await log.debug('Webhook request is valid, looking for HTTP handlers to call', loggingContext);
    const handlers = webhookRegistry[webhookCheck.topic] || [];
    const response = { statusCode: network.StatusCode.Ok };
    let found = false;
    for (const handler of handlers) {
        if (handler.deliveryMethod !== types.DeliveryMethod.Http) {
            continue;
        }
        if (!handler.callback) {
            response.statusCode = network.StatusCode.InternalServerError;
            response.errorMessage =
                "Cannot call webhooks.process with a webhook handler that doesn't have a callback";
            throw new error.MissingWebhookCallbackError({
                message: response.errorMessage,
                response,
            });
        }
        found = true;
        await log.debug('Found HTTP handler, triggering it', loggingContext);
        try {
            await handler.callback(webhookCheck.topic, webhookCheck.domain, rawBody, webhookCheck.webhookId, webhookCheck.apiVersion, ...(webhookCheck?.subTopic ? webhookCheck.subTopic : ''), context);
        }
        catch (error) {
            response.statusCode = network.StatusCode.InternalServerError;
            response.errorMessage = error.message;
        }
    }
    if (!found) {
        await log.debug('No HTTP handlers found', loggingContext);
        response.statusCode = network.StatusCode.NotFound;
        response.errorMessage = `No HTTP webhooks registered for topic ${webhookCheck.topic}`;
    }
    return response;
}
async function handleInvalidWebhook(config, webhookCheck) {
    const response = {
        statusCode: network.StatusCode.InternalServerError,
        errorMessage: 'Unknown error while handling webhook',
    };
    switch (webhookCheck.reason) {
        case types.WebhookValidationErrorReason.MissingHeaders:
            response.statusCode = network.StatusCode.BadRequest;
            response.errorMessage = `Missing one or more of the required HTTP headers to process webhooks: [${webhookCheck.missingHeaders.join(', ')}]`;
            break;
        case types.WebhookValidationErrorReason.MissingBody:
            response.statusCode = network.StatusCode.BadRequest;
            response.errorMessage = 'No body was received when processing webhook';
            break;
        case types.WebhookValidationErrorReason.MissingHmac:
            response.statusCode = network.StatusCode.BadRequest;
            response.errorMessage = `Missing HMAC header in request`;
            break;
        case types.WebhookValidationErrorReason.InvalidHmac:
            response.statusCode = network.StatusCode.Unauthorized;
            response.errorMessage = `Could not validate request HMAC`;
            break;
    }
    await index.logger(config).debug(`Webhook request is invalid, returning ${response.statusCode}: ${response.errorMessage}`);
    return response;
}

exports.process = process;
//# sourceMappingURL=process.js.map

{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/rest/types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,cAAc,EAAC,MAAM,yBAAyB,CAAC;AAEvD,OAAO,EAAC,qBAAqB,EAAC,MAAM,UAAU,CAAC;AAE/C,oBAAY,MAAM;IAChB,GAAG,QAAQ;IACX,IAAI,SAAS;IACb,GAAG,QAAQ;IACX,MAAM,WAAW;CAClB;AAED,KAAK,gBAAgB,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,MAAM,MAAM,iBAAiB,GACzB,gBAAgB,GAChB,gBAAgB,EAAE,GAClB,MAAM,CAAC,MAAM,EAAE,gBAAgB,GAAG,gBAAgB,EAAE,CAAC,CAAC;AAC1D,MAAM,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAE7D,MAAM,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,CAAC,CAAC;AAEvE,MAAM,WAAW,iBAAiB;IAChC,OAAO,CAAC,EAAE,aAAa,CAAC;IACxB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC;IACpC,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,kBAAmB,SAAQ,iBAAiB;IAC3D,IAAI,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC;CAC3C;AAED,MAAM,WAAW,iBAAkB,SAAQ,kBAAkB;CAAG;AAEhE,MAAM,WAAW,oBAAqB,SAAQ,iBAAiB;CAAG;AAElE,MAAM,WAAW,yBAA0B,SAAQ,qBAAqB;IACtE,MAAM,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC;IAC1B,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB;AAED,MAAM,MAAM,cAAc,GAAG,CAAC,iBAAiB,GAAG,kBAAkB,CAAC,GAAG;IACtE,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,WAAW,kBAAkB;IACjC,GAAG,EAAE,CACH,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,iBAAiB,KACxB,UAAU,CAAC,cAAc,CAAC,CAAC;IAChC,GAAG,EAAE,CACH,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,iBAAiB,KACxB,UAAU,CAAC,cAAc,CAAC,CAAC;IAChC,IAAI,EAAE,CACJ,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,kBAAkB,KACzB,UAAU,CAAC,cAAc,CAAC,CAAC;IAChC,MAAM,EAAE,CACN,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,oBAAoB,KAC3B,UAAU,CAAC,cAAc,CAAC,CAAC;CACjC"}
'use strict';

var graphqlClient = require('@shopify/graphql-client');
var constants = require('../constants.js');
var validations = require('../validations.js');

function createAdminApiClient({ storeDomain, apiVersion, accessToken, userAgentPrefix, retries = 0, customFetchApi, logger, isTesting, }) {
    const currentSupportedApiVersions = graphqlClient.getCurrentSupportedApiVersions();
    const storeUrl = graphqlClient.validateDomainAndGetStoreUrl({
        client: constants.CLIENT,
        storeDomain,
    });
    const baseApiVersionValidationParams = {
        client: constants.CLIENT,
        currentSupportedApiVersions,
        logger,
    };
    validations.validateServerSideUsage(isTesting);
    graphqlClient.validateApiVersion({
        client: constants.CLIENT,
        currentSupportedApiVersions,
        apiVersion,
        logger,
    });
    validations.validateRequiredAccessToken(accessToken);
    const apiUrlFormatter = generateApiUrlFormatter(storeUrl, apiVersion, baseApiVersionValidationParams);
    const config = {
        storeDomain: storeUrl,
        apiVersion,
        accessToken,
        headers: {
            'Content-Type': constants.DEFAULT_CONTENT_TYPE,
            Accept: constants.DEFAULT_CONTENT_TYPE,
            [constants.ACCESS_TOKEN_HEADER]: accessToken,
            'User-Agent': `${userAgentPrefix ? `${userAgentPrefix} | ` : ''}${constants.CLIENT} v${constants.DEFAULT_CLIENT_VERSION}`,
        },
        apiUrl: apiUrlFormatter(),
        userAgentPrefix,
    };
    const graphqlClient$1 = graphqlClient.createGraphQLClient({
        headers: config.headers,
        url: config.apiUrl,
        retries,
        customFetchApi,
        logger,
    });
    const getHeaders = graphqlClient.generateGetHeaders(config);
    const getApiUrl = generateGetApiUrl(config, apiUrlFormatter);
    const getGQLClientParams = graphqlClient.generateGetGQLClientParams({
        getHeaders,
        getApiUrl,
    });
    const client = {
        config,
        getHeaders,
        getApiUrl,
        fetch: (...props) => {
            return graphqlClient$1.fetch(...getGQLClientParams(...props));
        },
        request: (...props) => {
            return graphqlClient$1.request(...getGQLClientParams(...props));
        },
    };
    return Object.freeze(client);
}
function generateApiUrlFormatter(storeUrl, defaultApiVersion, baseApiVersionValidationParams) {
    return (apiVersion) => {
        if (apiVersion) {
            graphqlClient.validateApiVersion({
                ...baseApiVersionValidationParams,
                apiVersion,
            });
        }
        const urlApiVersion = (apiVersion ?? defaultApiVersion).trim();
        return `${storeUrl}/admin/api/${urlApiVersion}/graphql.json`;
    };
}
function generateGetApiUrl(config, apiUrlFormatter) {
    return (propApiVersion) => {
        return propApiVersion ? apiUrlFormatter(propApiVersion) : config.apiUrl;
    };
}

exports.createAdminApiClient = createAdminApiClient;
//# sourceMappingURL=client.js.map

'use strict';

var storefrontApiClient = require('@shopify/storefront-api-client');
var index = require('../../logger/index.js');
var error = require('../../error.js');
var index$1 = require('../../../runtime/http/index.js');
require('../../../runtime/crypto/types.js');
require('../../../runtime/crypto/crypto.js');
var common = require('../common.js');

class StorefrontClient {
    static config;
    session;
    client;
    apiVersion;
    constructor(params) {
        const config = this.storefrontClass().config;
        if (!config.isCustomStoreApp && !params.session.accessToken) {
            throw new error.MissingRequiredArgument('Missing access token when creating GraphQL client');
        }
        if (params.apiVersion) {
            const message = params.apiVersion === config.apiVersion
                ? `Storefront client has a redundant API version override to the default ${params.apiVersion}`
                : `Storefront client overriding default API version ${config.apiVersion} with ${params.apiVersion}`;
            index.logger(config).debug(message);
        }
        let accessToken;
        if (config.isCustomStoreApp) {
            accessToken = config.privateAppStorefrontAccessToken;
            if (!accessToken) {
                throw new error.MissingRequiredArgument('Custom store apps must set the privateAppStorefrontAccessToken property to call the Storefront API.');
            }
        }
        else {
            accessToken = params.session.accessToken;
            if (!accessToken) {
                throw new error.MissingRequiredArgument('Session missing access token.');
            }
        }
        this.session = params.session;
        this.apiVersion = params.apiVersion;
        this.client = storefrontApiClient.createStorefrontApiClient({
            privateAccessToken: accessToken,
            apiVersion: this.apiVersion ?? config.apiVersion,
            storeDomain: this.session.shop,
            customFetchApi: index$1.abstractFetch,
            logger: common.clientLoggerFactory(config),
            clientName: common.getUserAgent(config),
        });
    }
    async query(params) {
        index.logger(this.storefrontClass().config).deprecated('12.0.0', 'The query method is deprecated, and was replaced with the request method.\n' +
            'See the migration guide: https://github.com/Shopify/shopify-app-js/blob/main/packages/apps/shopify-api/docs/migrating-to-v9.md#using-the-new-clients.');
        if ((typeof params.data === 'string' && params.data.length === 0) ||
            Object.entries(params.data).length === 0) {
            throw new error.MissingRequiredArgument('Query missing.');
        }
        let operation;
        let variables;
        if (typeof params.data === 'string') {
            operation = params.data;
        }
        else {
            operation = params.data.query;
            variables = params.data.variables;
        }
        const headers = Object.fromEntries(Object.entries(params?.extraHeaders ?? {}).map(([key, value]) => [
            key,
            Array.isArray(value) ? value.join(', ') : value.toString(),
        ]));
        const response = await this.request(operation, {
            headers,
            retries: params.tries ? params.tries - 1 : undefined,
            variables,
        });
        return { body: response, headers: {} };
    }
    async request(operation, options) {
        const response = await this.client.request(operation, {
            apiVersion: this.apiVersion || this.storefrontClass().config.apiVersion,
            ...options,
        });
        if (response.errors) {
            const fetchResponse = response.errors.response;
            common.throwFailedRequest(response, (options?.retries ?? 0) > 0, fetchResponse);
        }
        return response;
    }
    storefrontClass() {
        return this.constructor;
    }
}
function storefrontClientClass(params) {
    const { config } = params;
    class NewStorefrontClient extends StorefrontClient {
        static config = config;
    }
    Reflect.defineProperty(NewStorefrontClient, 'name', {
        value: 'StorefrontClient',
    });
    return NewStorefrontClient;
}

exports.StorefrontClient = StorefrontClient;
exports.storefrontClientClass = storefrontClientClass;
//# sourceMappingURL=client.js.map

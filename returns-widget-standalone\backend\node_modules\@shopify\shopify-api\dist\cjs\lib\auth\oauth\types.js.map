{"version": 3, "file": "types.js", "sources": ["../../../../../../../lib/auth/oauth/types.ts"], "sourcesContent": ["import {AdapterArgs} from '../../../runtime/http/types';\n\nexport const SESSION_COOKIE_NAME = 'shopify_app_session';\nexport const STATE_COOKIE_NAME = 'shopify_app_state';\n\nexport interface AuthQuery {\n  [key: string]: string | undefined;\n  hmac?: string;\n  signature?: string;\n}\n\nexport interface BeginParams extends AdapterArgs {\n  /**\n   * The shop domain. For example: `{exampleshop}.myshopify.com`.\n   */\n  shop: string;\n  /**\n   * The path to the callback endpoint, with a leading `/`.\n   * This URL must be allowed in the Partners dashboard, or using the CLI to run your app.\n   */\n  callbackPath: string;\n  /**\n   * Defines if the session is online or offline.\n   * Learn more about [OAuth access modes](https://shopify.dev/docs/apps/auth/oauth/access-modes).\n   */\n  isOnline: boolean;\n}\n\nexport interface CallbackParams extends AdapterArgs {}\n\nexport interface AccessTokenResponse {\n  access_token: string;\n  scope: string;\n}\n\nexport interface OnlineAccessInfo {\n  /**\n   * How long the access token is valid for, in seconds.\n   */\n  expires_in: number;\n  /**\n   * The effective set of scopes for the session.\n   */\n  associated_user_scope: string;\n  /**\n   * The user associated with the access token.\n   */\n  associated_user: OnlineAccessUser;\n}\n\nexport interface OfflineAccessInfo {\n  /**\n   * How long the access token is valid for, in seconds.\n   */\n  expires_in?: number;\n}\n\nexport interface OnlineAccessUser {\n  /**\n   * The user's ID.\n   */\n  id: number;\n  /**\n   * The user's first name.\n   */\n  first_name: string;\n  /**\n   * The user's last name.\n   */\n  last_name: string;\n  /**\n   * The user's email address.\n   */\n  email: string;\n  /**\n   * Whether the user has verified their email address.\n   */\n  email_verified: boolean;\n  /**\n   * Whether the user is the account owner.\n   */\n  account_owner: boolean;\n  /**\n   * The user's locale.\n   */\n  locale: string;\n  /**\n   * Whether the user is a collaborator.\n   */\n  collaborator: boolean;\n}\n\nexport interface OnlineAccessResponse\n  extends AccessTokenResponse,\n    OnlineAccessInfo {}\n\nexport interface OfflineAccessResponse\n  extends AccessTokenResponse,\n    OfflineAccessInfo {}\n"], "names": [], "mappings": ";;AAEO,MAAM,mBAAmB,GAAG;AAC5B,MAAM,iBAAiB,GAAG;;;;;"}
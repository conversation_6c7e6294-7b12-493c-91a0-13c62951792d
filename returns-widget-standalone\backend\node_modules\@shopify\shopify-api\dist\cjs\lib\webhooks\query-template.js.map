{"version": 3, "file": "query-template.js", "sources": ["../../../../../../lib/webhooks/query-template.ts"], "sourcesContent": ["export function queryTemplate(template: string, params: Record<string, any>) {\n  let query = template;\n\n  Object.entries(params).forEach(([key, value]) => {\n    query = query.replace(`{{${key}}}`, value);\n  });\n\n  return query;\n}\n"], "names": [], "mappings": ";;AAAM,SAAU,aAAa,CAAC,QAAgB,EAAE,MAA2B,EAAA;IACzE,IAAI,KAAK,GAAG,QAAQ;AAEpB,IAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;QAC9C,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAA,EAAA,EAAK,GAAG,CAAA,EAAA,CAAI,EAAE,KAAK,CAAC;AAC5C,IAAA,CAAC,CAAC;AAEF,IAAA,OAAO,KAAK;AACd;;;;"}
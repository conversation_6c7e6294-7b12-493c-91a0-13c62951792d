{"version": 3, "file": "update-usage-subscription-capped-amount.js", "sources": ["../../../../../../lib/billing/update-usage-subscription-capped-amount.ts"], "sourcesContent": ["import {BillingError, GraphqlQueryError} from '../error';\nimport {ConfigInterface} from '../base-types';\nimport {graphqlClientClass} from '../clients/admin';\n\nimport {\n  BillingUpdateUsageCappedAmount,\n  BillingUpdateUsageCappedAmountParams,\n  BillingUpdateUsageCappedAmountResponse,\n  UpdateCappedAmountConfirmation,\n  APP_SUBSCRIPTION_FRAGMENT,\n} from './types';\nimport {convertLineItems} from './utils';\n\nconst UPDATE_USAGE_CAPPED_AMOUNT_MUTATION = `\n${APP_SUBSCRIPTION_FRAGMENT}\nmutation appSubscriptionLineItemUpdate($cappedAmount: MoneyInput!, $id: ID!) {\n  appSubscriptionLineItemUpdate(cappedAmount: $cappedAmount, id: $id) {\n    userErrors {\n      field\n      message\n    }\n    confirmationUrl\n    appSubscription {\n      ...AppSubscriptionFragment\n    }\n  }\n}\n`;\n\nexport function updateUsageCappedAmount(\n  config: ConfigInterface,\n): BillingUpdateUsageCappedAmount {\n  return async function updateUsageCappedAmount(\n    params: BillingUpdateUsageCappedAmountParams,\n  ): Promise<UpdateCappedAmountConfirmation> {\n    if (!config.billing) {\n      throw new BillingError({\n        message: 'Attempted to update line item without billing configs',\n        errorData: [],\n      });\n    }\n\n    const {\n      session,\n      subscriptionLineItemId,\n      cappedAmount: {amount, currencyCode},\n    } = params;\n\n    const GraphqlClient = graphqlClientClass({config});\n    const client = new GraphqlClient({session});\n\n    try {\n      const response =\n        await client.request<BillingUpdateUsageCappedAmountResponse>(\n          UPDATE_USAGE_CAPPED_AMOUNT_MUTATION,\n          {\n            variables: {\n              id: subscriptionLineItemId,\n              cappedAmount: {\n                amount,\n                currencyCode,\n              },\n            },\n          },\n        );\n\n      if (response.data?.appSubscriptionLineItemUpdate?.userErrors.length) {\n        throw new BillingError({\n          message: 'Error while updating usage subscription capped amount',\n          errorData: response.data?.appSubscriptionLineItemUpdate?.userErrors,\n        });\n      }\n\n      const appSubscription =\n        response.data?.appSubscriptionLineItemUpdate?.appSubscription!;\n      if (appSubscription && appSubscription.lineItems) {\n        appSubscription.lineItems = convertLineItems(appSubscription.lineItems);\n      }\n\n      return {\n        confirmationUrl:\n          response.data?.appSubscriptionLineItemUpdate?.confirmationUrl!,\n        appSubscription,\n      };\n    } catch (error) {\n      if (error instanceof GraphqlQueryError) {\n        throw new BillingError({\n          message: error.message,\n          errorData: error.response?.errors,\n        });\n      }\n\n      throw error;\n    }\n  };\n}\n"], "names": ["APP_SUBSCRIPTION_FRAGMENT", "BillingError", "graphqlClientClass", "client", "convertLineItems", "error", "GraphqlQueryError"], "mappings": ";;;;;;;;;;;;;AAaA,MAAM,mCAAmC,GAAG;EAC1CA,+BAAyB;;;;;;;;;;;;;CAa1B;AAEK,SAAU,uBAAuB,CACrC,MAAuB,EAAA;AAEvB,IAAA,OAAO,eAAe,uBAAuB,CAC3C,MAA4C,EAAA;AAE5C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACnB,MAAM,IAAIC,kBAAY,CAAC;AACrB,gBAAA,OAAO,EAAE,uDAAuD;AAChE,gBAAA,SAAS,EAAE,EAAE;AACd,aAAA,CAAC;QACJ;AAEA,QAAA,MAAM,EACJ,OAAO,EACP,sBAAsB,EACtB,YAAY,EAAE,EAAC,MAAM,EAAE,YAAY,EAAC,GACrC,GAAG,MAAM;QAEV,MAAM,aAAa,GAAGC,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;QAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;AAE3C,QAAA,IAAI;YACF,MAAM,QAAQ,GACZ,MAAMA,QAAM,CAAC,OAAO,CAClB,mCAAmC,EACnC;AACE,gBAAA,SAAS,EAAE;AACT,oBAAA,EAAE,EAAE,sBAAsB;AAC1B,oBAAA,YAAY,EAAE;wBACZ,MAAM;wBACN,YAAY;AACb,qBAAA;AACF,iBAAA;AACF,aAAA,CACF;YAEH,IAAI,QAAQ,CAAC,IAAI,EAAE,6BAA6B,EAAE,UAAU,CAAC,MAAM,EAAE;gBACnE,MAAM,IAAIF,kBAAY,CAAC;AACrB,oBAAA,OAAO,EAAE,uDAAuD;AAChE,oBAAA,SAAS,EAAE,QAAQ,CAAC,IAAI,EAAE,6BAA6B,EAAE,UAAU;AACpE,iBAAA,CAAC;YACJ;YAEA,MAAM,eAAe,GACnB,QAAQ,CAAC,IAAI,EAAE,6BAA6B,EAAE,eAAgB;AAChE,YAAA,IAAI,eAAe,IAAI,eAAe,CAAC,SAAS,EAAE;gBAChD,eAAe,CAAC,SAAS,GAAGG,sBAAgB,CAAC,eAAe,CAAC,SAAS,CAAC;YACzE;YAEA,OAAO;AACL,gBAAA,eAAe,EACb,QAAQ,CAAC,IAAI,EAAE,6BAA6B,EAAE,eAAgB;gBAChE,eAAe;aAChB;QACH;QAAE,OAAOC,OAAK,EAAE;AACd,YAAA,IAAIA,OAAK,YAAYC,uBAAiB,EAAE;gBACtC,MAAM,IAAIL,kBAAY,CAAC;oBACrB,OAAO,EAAEI,OAAK,CAAC,OAAO;AACtB,oBAAA,SAAS,EAAEA,OAAK,CAAC,QAAQ,EAAE,MAAM;AAClC,iBAAA,CAAC;YACJ;AAEA,YAAA,MAAMA,OAAK;QACb;AACF,IAAA,CAAC;AACH;;;;"}
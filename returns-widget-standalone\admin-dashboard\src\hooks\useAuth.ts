import { useState, useEffect, createContext, useContext, createElement } from 'react'
import type { ReactNode } from 'react'
import { authService } from '../services/auth'
import { api } from '../services/api'
import type { AuthUser, LoginData, RegisterData } from '../services/types'

interface AuthContextType {
  user: AuthUser | null
  login: (data: LoginData) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => void
  loading: boolean
  error: string | null
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check for existing session on mount
    const token = authService.getToken()
    if (token) {
      // Validate token and get user data
      api.getMerchant()
        .then(setUser)
        .catch(() => {
          // Token is invalid, clear it
          authService.logout()
        })
        .finally(() => setLoading(false))
    } else {
      setLoading(false)
    }
  }, [])

  const login = async (data: LoginData) => {
    setError(null)
    setLoading(true)
    
    try {
      const user = await authService.login(data)
      setUser(user)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const register = async (data: RegisterData) => {
    setError(null)
    setLoading(true)
    
    try {
      const user = await authService.register(data)
      setUser(user)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    authService.logout()
    setUser(null)
    setError(null)
  }

  const clearError = () => {
    setError(null)
  }

  const contextValue = {
    user, 
    login, 
    register, 
    logout, 
    loading, 
    error, 
    clearError 
  }

  return createElement(AuthContext.Provider, { value: contextValue }, children)
}

#!/usr/bin/env node

/**
 * MCP Server for Database Operations
 * Provides tools for GitHub Copilot to interact with SQLite database
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import Database from 'better-sqlite3';

class DatabaseMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'database-tools-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'db_query',
            description: 'Execute a SELECT query on the database',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'SQL SELECT query to execute',
                },
                params: {
                  type: 'array',
                  description: 'Query parameters',
                  items: { type: 'string' },
                },
                dbPath: {
                  type: 'string',
                  description: 'Path to SQLite database file',
                },
              },
              required: ['query'],
            },
          },
          {
            name: 'db_schema',
            description: 'Get database schema information',
            inputSchema: {
              type: 'object',
              properties: {
                dbPath: {
                  type: 'string',
                  description: 'Path to SQLite database file',
                },
                tableName: {
                  type: 'string',
                  description: 'Specific table name (optional)',
                },
              },
            },
          },
          {
            name: 'db_stats',
            description: 'Get database statistics',
            inputSchema: {
              type: 'object',
              properties: {
                dbPath: {
                  type: 'string',
                  description: 'Path to SQLite database file',
                },
              },
            },
          },
          {
            name: 'db_backup',
            description: 'Create a backup of the database',
            inputSchema: {
              type: 'object',
              properties: {
                dbPath: {
                  type: 'string',
                  description: 'Path to SQLite database file',
                },
                backupPath: {
                  type: 'string',
                  description: 'Path for backup file',
                },
              },
              required: ['dbPath', 'backupPath'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'db_query':
            return await this.executeQuery(args);
          case 'db_schema':
            return await this.getSchema(args);
          case 'db_stats':
            return await this.getStats(args);
          case 'db_backup':
            return await this.createBackup(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
        };
      }
    });
  }

  getDatabase(dbPath) {
    const path = dbPath || process.env.DATABASE_PATH || './backend/database.sqlite';
    return new Database(path);
  }

  async executeQuery({ query, params = [], dbPath }) {
    const db = this.getDatabase(dbPath);
    
    try {
      const stmt = db.prepare(query);
      const results = stmt.all(...params);
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(results, null, 2),
          },
        ],
      };
    } finally {
      db.close();
    }
  }

  async getSchema({ dbPath, tableName }) {
    const db = this.getDatabase(dbPath);
    
    try {
      let results;
      
      if (tableName) {
        // Get schema for specific table
        const tableInfo = db.prepare("PRAGMA table_info(?)").all(tableName);
        const indexes = db.prepare("PRAGMA index_list(?)").all(tableName);
        results = { table: tableName, columns: tableInfo, indexes };
      } else {
        // Get all tables
        const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
        results = { tables: tables.map(t => t.name) };
      }
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(results, null, 2),
          },
        ],
      };
    } finally {
      db.close();
    }
  }

  async getStats({ dbPath }) {
    const db = this.getDatabase(dbPath);
    
    try {
      const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
      const stats = {};
      
      for (const table of tables) {
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
        stats[table.name] = count.count;
      }
      
      const dbSize = db.prepare("PRAGMA page_count").get();
      const pageSize = db.prepare("PRAGMA page_size").get();
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              tableCounts: stats,
              databaseSize: `${(dbSize.page_count * pageSize.page_size / 1024).toFixed(2)} KB`,
              totalTables: tables.length,
            }, null, 2),
          },
        ],
      };
    } finally {
      db.close();
    }
  }

  async createBackup({ dbPath, backupPath }) {
    const db = this.getDatabase(dbPath);
    
    try {
      db.backup(backupPath);
      
      return {
        content: [
          {
            type: 'text',
            text: `Database backup created successfully at: ${backupPath}`,
          },
        ],
      };
    } finally {
      db.close();
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Database MCP server running on stdio');
  }
}

const server = new DatabaseMCPServer();
server.run().catch(console.error);

{"version": 3, "file": "fetch-request.js", "sources": ["../../../../../../lib/utils/fetch-request.ts"], "sourcesContent": ["import {logger} from '../logger';\nimport {LogSeverity} from '../types';\nimport {abstractFetch} from '../../runtime';\nimport {ConfigInterface} from '../base-types';\n\nexport function fetchRequestFactory(config: ConfigInterface) {\n  return async function fetchRequest(\n    url: string,\n    options?: RequestInit,\n  ): Promise<Response> {\n    const log = logger(config);\n    const doLog =\n      config.logger.httpRequests && config.logger.level === LogSeverity.Debug;\n\n    if (doLog) {\n      log.debug('Making HTTP request', {\n        method: options?.method || 'GET',\n        url,\n        ...(options?.body && {body: options?.body}),\n      });\n    }\n\n    const response = await abstractFetch(url, options);\n\n    if (doLog) {\n      log.debug('HTTP request completed', {\n        method: options?.method || 'GET',\n        url,\n        status: response.status,\n      });\n    }\n\n    return response;\n  };\n}\n"], "names": ["logger", "LogSeverity", "abstractFetch"], "mappings": ";;;;;;;;AAKM,SAAU,mBAAmB,CAAC,MAAuB,EAAA;AACzD,IAAA,OAAO,eAAe,YAAY,CAChC,GAAW,EACX,OAAqB,EAAA;AAErB,QAAA,MAAM,GAAG,GAAGA,YAAM,CAAC,MAAM,CAAC;AAC1B,QAAA,MAAM,KAAK,GACT,MAAM,CAAC,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,KAAKC,iBAAW,CAAC,KAAK;QAEzE,IAAI,KAAK,EAAE;AACT,YAAA,GAAG,CAAC,KAAK,CAAC,qBAAqB,EAAE;AAC/B,gBAAA,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,KAAK;gBAChC,GAAG;AACH,gBAAA,IAAI,OAAO,EAAE,IAAI,IAAI,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AAC5C,aAAA,CAAC;QACJ;QAEA,MAAM,QAAQ,GAAG,MAAMC,qBAAa,CAAC,GAAG,EAAE,OAAO,CAAC;QAElD,IAAI,KAAK,EAAE;AACT,YAAA,GAAG,CAAC,KAAK,CAAC,wBAAwB,EAAE;AAClC,gBAAA,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,KAAK;gBAChC,GAAG;gBACH,MAAM,EAAE,QAAQ,CAAC,MAAM;AACxB,aAAA,CAAC;QACJ;AAEA,QAAA,OAAO,QAAQ;AACjB,IAAA,CAAC;AACH;;;;"}
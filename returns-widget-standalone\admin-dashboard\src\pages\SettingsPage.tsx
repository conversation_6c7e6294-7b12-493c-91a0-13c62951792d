import { useState } from 'react'
import { ShopifySettings } from '../components/Settings/ShopifySettings'
import { SendcloudSettings } from '../components/Settings/SendcloudSettings'
import { ReturnPolicySettings } from '../components/Settings/ReturnPolicySettings'
import { Settings as SettingsIcon, ShoppingBag, Truck, FileText } from 'lucide-react'
import styles from './SettingsPage.module.css'

type SettingsTab = 'shopify' | 'sendcloud' | 'policy'

export const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('shopify')

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <SettingsIcon size={24} />
          <h1>Settings</h1>
        </div>
        <p>Configure your integrations and return policies</p>
      </div>

      <div className={styles.tabs}>
        <button
          className={`${styles.tab} ${activeTab === 'shopify' ? styles.activeTab : ''}`}
          onClick={() => setActiveTab('shopify')}
        >
          <ShoppingBag size={20} />
          <span>Shopify Integration</span>
        </button>
        
        <button
          className={`${styles.tab} ${activeTab === 'sendcloud' ? styles.activeTab : ''}`}
          onClick={() => setActiveTab('sendcloud')}
        >
          <Truck size={20} />
          <span>Sendcloud Integration</span>
        </button>

        <button
          className={`${styles.tab} ${activeTab === 'policy' ? styles.activeTab : ''}`}
          onClick={() => setActiveTab('policy')}
        >
          <FileText size={20} />
          <span>Return Policy</span>
        </button>
      </div>

      <div className={styles.content}>
        {activeTab === 'shopify' && <ShopifySettings />}
        {activeTab === 'sendcloud' && <SendcloudSettings />}
        {activeTab === 'policy' && <ReturnPolicySettings />}
      </div>
    </div>
  )
}

{"version": 3, "file": "client.js", "sources": ["../../src/rest/client.ts"], "sourcesContent": ["import {\n  CustomFetchApi,\n  LogContentTypes,\n  Logger,\n  generateHttpFetch,\n  getCurrentSupportedApiVersions,\n  validateApiVersion,\n  validateDomainAndGetStoreUrl,\n  validateRetries,\n} from '@shopify/graphql-client';\n\nimport {\n  validateRequiredAccessToken,\n  validateServerSideUsage,\n} from '../validations';\nimport {\n  ACCESS_TOKEN_HEADER,\n  CLIENT,\n  DEFAULT_CLIENT_VERSION,\n  DEFAULT_CONTENT_TYPE,\n  DEFAULT_RETRY_WAIT_TIME,\n  RETRIABLE_STATUS_CODES,\n} from '../constants';\n\nimport {\n  AdminRestApiClient,\n  AdminRestApiClientOptions,\n  DeleteRequestOptions,\n  GetRequestOptions,\n  HeaderOptions,\n  Method,\n  PostRequestOptions,\n  PutRequestOptions,\n  RequestOptions,\n  SearchParamFields,\n  SearchParams,\n} from './types';\n\nexport function createAdminRestApiClient({\n  storeDomain,\n  apiVersion,\n  accessToken,\n  userAgentPrefix,\n  logger,\n  customFetchApi = fetch,\n  retries: clientRetries = 0,\n  scheme = 'https',\n  defaultRetryTime = DEFAULT_RETRY_WAIT_TIME,\n  formatPaths = true,\n  isTesting,\n}: AdminRestApiClientOptions): AdminRestApiClient {\n  const currentSupportedApiVersions = getCurrentSupportedApiVersions();\n\n  const storeUrl = validateDomainAndGetStoreUrl({\n    client: CLIENT,\n    storeDomain,\n  }).replace('https://', `${scheme}://`);\n\n  const baseApiVersionValidationParams = {\n    client: CLIENT,\n    currentSupportedApiVersions,\n    logger,\n  };\n\n  validateServerSideUsage(isTesting);\n  validateApiVersion({\n    client: CLIENT,\n    currentSupportedApiVersions,\n    apiVersion,\n    logger,\n  });\n  validateRequiredAccessToken(accessToken);\n  validateRetries({client: CLIENT, retries: clientRetries});\n\n  const apiUrlFormatter = generateApiUrlFormatter(\n    storeUrl,\n    apiVersion,\n    baseApiVersionValidationParams,\n    formatPaths,\n  );\n  const clientLogger = generateClientLogger(logger);\n  const httpFetch = generateHttpFetch({\n    customFetchApi,\n    clientLogger,\n    defaultRetryWaitTime: defaultRetryTime,\n    client: CLIENT,\n    retriableCodes: RETRIABLE_STATUS_CODES,\n  });\n\n  const request = async (\n    path: string,\n    {\n      method,\n      data,\n      headers: requestHeadersObj,\n      searchParams,\n      retries = 0,\n      apiVersion,\n    }: RequestOptions,\n  ): ReturnType<CustomFetchApi> => {\n    validateRetries({client: CLIENT, retries});\n\n    const url = apiUrlFormatter(path, searchParams ?? {}, apiVersion);\n\n    const requestHeaders = normalizedHeaders(requestHeadersObj ?? {});\n    const userAgent = [\n      ...(requestHeaders['user-agent'] ? [requestHeaders['user-agent']] : []),\n      ...(userAgentPrefix ? [userAgentPrefix] : []),\n      `${CLIENT} v${DEFAULT_CLIENT_VERSION}`,\n    ].join(' | ');\n\n    const headers = normalizedHeaders({\n      'Content-Type': DEFAULT_CONTENT_TYPE,\n      ...requestHeaders,\n      Accept: DEFAULT_CONTENT_TYPE,\n      [ACCESS_TOKEN_HEADER]: accessToken,\n      'User-Agent': userAgent,\n    });\n\n    const body = data && typeof data !== 'string' ? JSON.stringify(data) : data;\n\n    return httpFetch(\n      [url, {method, headers, ...(body ? {body} : undefined)}],\n      1,\n      retries ?? clientRetries,\n    );\n  };\n\n  return {\n    get: (path: string, options?: GetRequestOptions) =>\n      request(path, {method: Method.Get, ...options}),\n    put: (path: string, options?: PutRequestOptions) =>\n      request(path, {method: Method.Put, ...options}),\n    post: (path: string, options?: PostRequestOptions) =>\n      request(path, {method: Method.Post, ...options}),\n    delete: (path: string, options?: DeleteRequestOptions) =>\n      request(path, {method: Method.Delete, ...options}),\n  };\n}\n\nfunction generateApiUrlFormatter(\n  storeUrl: string,\n  defaultApiVersion: string,\n  baseApiVersionValidationParams: Omit<\n    Parameters<typeof validateApiVersion>[0],\n    'apiVersion'\n  >,\n  formatPaths = true,\n) {\n  return (path: string, searchParams: SearchParams, apiVersion?: string) => {\n    if (apiVersion) {\n      validateApiVersion({\n        ...baseApiVersionValidationParams,\n        apiVersion,\n      });\n    }\n\n    function convertValue(\n      params: URLSearchParams,\n      key: string,\n      value: SearchParamFields,\n    ) {\n      if (Array.isArray(value)) {\n        value.forEach((arrayValue) =>\n          convertValue(params, `${key}[]`, arrayValue),\n        );\n        return;\n      } else if (typeof value === 'object') {\n        Object.entries(value).forEach(([objKey, objValue]) =>\n          convertValue(params, `${key}[${objKey}]`, objValue),\n        );\n        return;\n      }\n\n      params.append(key, String(value));\n    }\n\n    const urlApiVersion = (apiVersion ?? defaultApiVersion).trim();\n    let cleanPath = path.replace(/^\\//, '');\n    if (formatPaths) {\n      if (!cleanPath.startsWith('admin')) {\n        cleanPath = `admin/api/${urlApiVersion}/${cleanPath}`;\n      }\n      if (!cleanPath.endsWith('.json')) {\n        cleanPath = `${cleanPath}.json`;\n      }\n    }\n\n    const params = new URLSearchParams();\n    if (searchParams) {\n      for (const [key, value] of Object.entries(searchParams)) {\n        convertValue(params, key, value);\n      }\n    }\n    const queryString = params.toString() ? `?${params.toString()}` : '';\n\n    return `${storeUrl}/${cleanPath}${queryString}`;\n  };\n}\n\nfunction generateClientLogger(logger?: Logger): Logger {\n  return (logContent: LogContentTypes) => {\n    if (logger) {\n      logger(logContent);\n    }\n  };\n}\n\nfunction normalizedHeaders(headersObj: HeaderOptions): Record<string, string> {\n  const normalizedHeaders: Record<string, string> = {};\n  for (const [key, value] of Object.entries(headersObj)) {\n    normalizedHeaders[key.toLowerCase()] = Array.isArray(value)\n      ? value.join(', ')\n      : String(value);\n  }\n  return normalizedHeaders;\n}\n"], "names": ["DEFAULT_RETRY_WAIT_TIME", "getCurrentSupportedApiVersions", "validateDomainAndGetStoreUrl", "CLIENT", "validateServerSideUsage", "validateApiVersion", "validateRequiredAccessToken", "validateRetries", "generateHttpFetch", "RETRIABLE_STATUS_CODES", "DEFAULT_CLIENT_VERSION", "DEFAULT_CONTENT_TYPE", "ACCESS_TOKEN_HEADER", "Method"], "mappings": ";;;;;;;SAsCgB,wBAAwB,CAAC,EACvC,WAAW,EACX,UAAU,EACV,WAAW,EACX,eAAe,EACf,MAAM,EACN,cAAc,GAAG,KAAK,EACtB,OAAO,EAAE,aAAa,GAAG,CAAC,EAC1B,MAAM,GAAG,OAAO,EAChB,gBAAgB,GAAGA,iCAAuB,EAC1C,WAAW,GAAG,IAAI,EAClB,SAAS,GACiB,EAAA;AAC1B,IAAA,MAAM,2BAA2B,GAAGC,4CAA8B,EAAE;IAEpE,MAAM,QAAQ,GAAGC,0CAA4B,CAAC;AAC5C,QAAA,MAAM,EAAEC,gBAAM;QACd,WAAW;KACZ,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,CAAA,EAAG,MAAM,CAAA,GAAA,CAAK,CAAC;AAEtC,IAAA,MAAM,8BAA8B,GAAG;AACrC,QAAA,MAAM,EAAEA,gBAAM;QACd,2BAA2B;QAC3B,MAAM;KACP;IAEDC,mCAAuB,CAAC,SAAS,CAAC;AAClC,IAAAC,gCAAkB,CAAC;AACjB,QAAA,MAAM,EAAEF,gBAAM;QACd,2BAA2B;QAC3B,UAAU;QACV,MAAM;AACP,KAAA,CAAC;IACFG,uCAA2B,CAAC,WAAW,CAAC;IACxCC,6BAAe,CAAC,EAAC,MAAM,EAAEJ,gBAAM,EAAE,OAAO,EAAE,aAAa,EAAC,CAAC;AAEzD,IAAA,MAAM,eAAe,GAAG,uBAAuB,CAC7C,QAAQ,EACR,UAAU,EACV,8BAA8B,EAC9B,WAAW,CACZ;AACD,IAAA,MAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,CAAC;IACjD,MAAM,SAAS,GAAGK,+BAAiB,CAAC;QAClC,cAAc;QACd,YAAY;AACZ,QAAA,oBAAoB,EAAE,gBAAgB;AACtC,QAAA,MAAM,EAAEL,gBAAM;AACd,QAAA,cAAc,EAAEM,gCAAsB;AACvC,KAAA,CAAC;IAEF,MAAM,OAAO,GAAG,OACd,IAAY,EACZ,EACE,MAAM,EACN,IAAI,EACJ,OAAO,EAAE,iBAAiB,EAC1B,YAAY,EACZ,OAAO,GAAG,CAAC,EACX,UAAU,GACK,KACa;QAC9BF,6BAAe,CAAC,EAAC,MAAM,EAAEJ,gBAAM,EAAE,OAAO,EAAC,CAAC;AAE1C,QAAA,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,EAAE,YAAY,IAAI,EAAE,EAAE,UAAU,CAAC;QAEjE,MAAM,cAAc,GAAG,iBAAiB,CAAC,iBAAiB,IAAI,EAAE,CAAC;AACjE,QAAA,MAAM,SAAS,GAAG;AAChB,YAAA,IAAI,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,CAAC;AACvE,YAAA,IAAI,eAAe,GAAG,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;YAC7C,CAAA,EAAGA,gBAAM,CAAA,EAAA,EAAKO,gCAAsB,CAAA,CAAE;AACvC,SAAA,CAAC,IAAI,CAAC,KAAK,CAAC;QAEb,MAAM,OAAO,GAAG,iBAAiB,CAAC;AAChC,YAAA,cAAc,EAAEC,8BAAoB;AACpC,YAAA,GAAG,cAAc;AACjB,YAAA,MAAM,EAAEA,8BAAoB;YAC5B,CAACC,6BAAmB,GAAG,WAAW;AAClC,YAAA,YAAY,EAAE,SAAS;AACxB,SAAA,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;AAE3E,QAAA,OAAO,SAAS,CACd,CAAC,GAAG,EAAE,EAAC,MAAM,EAAE,OAAO,EAAE,IAAI,IAAI,GAAG,EAAC,IAAI,EAAC,GAAG,SAAS,CAAC,EAAC,CAAC,EACxD,CAAC,EACD,OAAO,IAAI,aAAa,CACzB;AACH,IAAA,CAAC;IAED,OAAO;QACL,GAAG,EAAE,CAAC,IAAY,EAAE,OAA2B,KAC7C,OAAO,CAAC,IAAI,EAAE,EAAC,MAAM,EAAEC,YAAM,CAAC,GAAG,EAAE,GAAG,OAAO,EAAC,CAAC;QACjD,GAAG,EAAE,CAAC,IAAY,EAAE,OAA2B,KAC7C,OAAO,CAAC,IAAI,EAAE,EAAC,MAAM,EAAEA,YAAM,CAAC,GAAG,EAAE,GAAG,OAAO,EAAC,CAAC;QACjD,IAAI,EAAE,CAAC,IAAY,EAAE,OAA4B,KAC/C,OAAO,CAAC,IAAI,EAAE,EAAC,MAAM,EAAEA,YAAM,CAAC,IAAI,EAAE,GAAG,OAAO,EAAC,CAAC;QAClD,MAAM,EAAE,CAAC,IAAY,EAAE,OAA8B,KACnD,OAAO,CAAC,IAAI,EAAE,EAAC,MAAM,EAAEA,YAAM,CAAC,MAAM,EAAE,GAAG,OAAO,EAAC,CAAC;KACrD;AACH;AAEA,SAAS,uBAAuB,CAC9B,QAAgB,EAChB,iBAAyB,EACzB,8BAGC,EACD,WAAW,GAAG,IAAI,EAAA;AAElB,IAAA,OAAO,CAAC,IAAY,EAAE,YAA0B,EAAE,UAAmB,KAAI;QACvE,IAAI,UAAU,EAAE;AACd,YAAAR,gCAAkB,CAAC;AACjB,gBAAA,GAAG,8BAA8B;gBACjC,UAAU;AACX,aAAA,CAAC;QACJ;AAEA,QAAA,SAAS,YAAY,CACnB,MAAuB,EACvB,GAAW,EACX,KAAwB,EAAA;AAExB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,KACvB,YAAY,CAAC,MAAM,EAAE,GAAG,GAAG,CAAA,EAAA,CAAI,EAAE,UAAU,CAAC,CAC7C;gBACD;YACF;AAAO,iBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACpC,gBAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,KAC/C,YAAY,CAAC,MAAM,EAAE,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,MAAM,CAAA,CAAA,CAAG,EAAE,QAAQ,CAAC,CACpD;gBACD;YACF;YAEA,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QACnC;QAEA,MAAM,aAAa,GAAG,CAAC,UAAU,IAAI,iBAAiB,EAAE,IAAI,EAAE;QAC9D,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;QACvC,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAClC,gBAAA,SAAS,GAAG,CAAA,UAAA,EAAa,aAAa,CAAA,CAAA,EAAI,SAAS,EAAE;YACvD;YACA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAChC,gBAAA,SAAS,GAAG,CAAA,EAAG,SAAS,CAAA,KAAA,CAAO;YACjC;QACF;AAEA,QAAA,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE;QACpC,IAAI,YAAY,EAAE;AAChB,YAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AACvD,gBAAA,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC;YAClC;QACF;AACA,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAA,CAAE,GAAG,EAAE;AAEpE,QAAA,OAAO,GAAG,QAAQ,CAAA,CAAA,EAAI,SAAS,CAAA,EAAG,WAAW,EAAE;AACjD,IAAA,CAAC;AACH;AAEA,SAAS,oBAAoB,CAAC,MAAe,EAAA;IAC3C,OAAO,CAAC,UAA2B,KAAI;QACrC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,UAAU,CAAC;QACpB;AACF,IAAA,CAAC;AACH;AAEA,SAAS,iBAAiB,CAAC,UAAyB,EAAA;IAClD,MAAM,iBAAiB,GAA2B,EAAE;AACpD,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AACrD,QAAA,iBAAiB,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK;AACxD,cAAE,KAAK,CAAC,IAAI,CAAC,IAAI;AACjB,cAAE,MAAM,CAAC,KAAK,CAAC;IACnB;AACA,IAAA,OAAO,iBAAiB;AAC1B;;;;"}
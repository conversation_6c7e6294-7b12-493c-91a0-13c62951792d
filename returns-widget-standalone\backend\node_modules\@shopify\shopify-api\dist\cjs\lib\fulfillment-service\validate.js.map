{"version": 3, "file": "validate.js", "sources": ["../../../../../../lib/fulfillment-service/validate.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\nimport {validateHmacFromRequestFactory} from '../utils/hmac-validator';\nimport {\n  HmacValidationType,\n  ValidateParams,\n  ValidationInvalid,\n  ValidationValid,\n} from '../utils/types';\n\nexport function validateFactory(config: ConfigInterface) {\n  return async function validate({\n    rawBody,\n    ...adapterArgs\n  }: ValidateParams): Promise<ValidationInvalid | ValidationValid> {\n    return validateHmacFromRequestFactory(config)({\n      type: HmacValidationType.FulfillmentService,\n      rawBody,\n      ...adapterArgs,\n    });\n  };\n}\n"], "names": ["validateHmacFromRequestFactory", "HmacValidationType"], "mappings": ";;;;;AASM,SAAU,eAAe,CAAC,MAAuB,EAAA;IACrD,OAAO,eAAe,QAAQ,CAAC,EAC7B,OAAO,EACP,GAAG,WAAW,EACC,EAAA;AACf,QAAA,OAAOA,4CAA8B,CAAC,MAAM,CAAC,CAAC;YAC5C,IAAI,EAAEC,wBAAkB,CAAC,kBAAkB;YAC3C,OAAO;AACP,YAAA,GAAG,WAAW;AACf,SAAA,CAAC;AACJ,IAAA,CAAC;AACH;;;;"}
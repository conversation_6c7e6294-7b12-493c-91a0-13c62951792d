{"version": 3, "file": "client.js", "sources": ["../../../../../../../lib/clients/storefront/client.ts"], "sourcesContent": ["import {\n  StorefrontApiClient,\n  StorefrontOperations,\n  ClientResponse,\n  createStorefrontApiClient,\n  ReturnData,\n  ApiClientRequestOptions,\n} from '@shopify/storefront-api-client';\n\nimport {ApiVersion} from '../../types';\nimport {logger} from '../../logger';\nimport * as ShopifyErrors from '../../error';\nimport {MissingRequiredArgument} from '../../error';\nimport type {\n  GraphqlClientParams,\n  GraphqlParams,\n  GraphqlQueryOptions,\n  RequestReturn,\n} from '../types';\nimport {ConfigInterface} from '../../base-types';\nimport {Session} from '../../session/session';\nimport {abstractFetch} from '../../../runtime';\nimport {clientLoggerFactory, getUserAgent, throwFailedRequest} from '../common';\n\ninterface GraphqlClientClassParams {\n  config: ConfigInterface;\n}\n\nexport class StorefrontClient {\n  public static config: ConfigInterface;\n\n  readonly session: Session;\n  readonly client: StorefrontApiClient;\n  readonly apiVersion?: ApiVersion;\n\n  constructor(params: GraphqlClientParams) {\n    const config = this.storefrontClass().config;\n\n    if (!config.isCustomStoreApp && !params.session.accessToken) {\n      throw new ShopifyErrors.MissingRequiredArgument(\n        'Missing access token when creating GraphQL client',\n      );\n    }\n\n    if (params.apiVersion) {\n      const message =\n        params.apiVersion === config.apiVersion\n          ? `Storefront client has a redundant API version override to the default ${params.apiVersion}`\n          : `Storefront client overriding default API version ${config.apiVersion} with ${params.apiVersion}`;\n\n      logger(config).debug(message);\n    }\n\n    let accessToken: string | undefined;\n    if (config.isCustomStoreApp) {\n      accessToken = config.privateAppStorefrontAccessToken;\n\n      if (!accessToken) {\n        throw new MissingRequiredArgument(\n          'Custom store apps must set the privateAppStorefrontAccessToken property to call the Storefront API.',\n        );\n      }\n    } else {\n      accessToken = params.session.accessToken;\n\n      if (!accessToken) {\n        throw new MissingRequiredArgument('Session missing access token.');\n      }\n    }\n\n    this.session = params.session;\n    this.apiVersion = params.apiVersion;\n    this.client = createStorefrontApiClient({\n      privateAccessToken: accessToken,\n      apiVersion: this.apiVersion ?? config.apiVersion,\n      storeDomain: this.session.shop,\n      customFetchApi: abstractFetch,\n      logger: clientLoggerFactory(config),\n      clientName: getUserAgent(config),\n    });\n  }\n\n  public async query<T = undefined>(\n    params: GraphqlParams,\n  ): Promise<RequestReturn<T>> {\n    logger(this.storefrontClass().config).deprecated(\n      '12.0.0',\n      'The query method is deprecated, and was replaced with the request method.\\n' +\n        'See the migration guide: https://github.com/Shopify/shopify-app-js/blob/main/packages/apps/shopify-api/docs/migrating-to-v9.md#using-the-new-clients.',\n    );\n\n    if (\n      (typeof params.data === 'string' && params.data.length === 0) ||\n      Object.entries(params.data).length === 0\n    ) {\n      throw new ShopifyErrors.MissingRequiredArgument('Query missing.');\n    }\n\n    let operation: string;\n    let variables: Record<string, any> | undefined;\n    if (typeof params.data === 'string') {\n      operation = params.data;\n    } else {\n      operation = params.data.query;\n      variables = params.data.variables;\n    }\n\n    const headers = Object.fromEntries(\n      Object.entries(params?.extraHeaders ?? {}).map(([key, value]) => [\n        key,\n        Array.isArray(value) ? value.join(', ') : value.toString(),\n      ]),\n    );\n\n    const response = await this.request<T>(operation, {\n      headers,\n      retries: params.tries ? params.tries - 1 : undefined,\n      variables,\n    });\n\n    return {body: response as T, headers: {}};\n  }\n\n  public async request<\n    T = undefined,\n    Operation extends keyof Operations = string,\n    Operations extends StorefrontOperations = StorefrontOperations,\n  >(\n    operation: Operation,\n    options?: GraphqlQueryOptions<Operation, Operations>,\n  ): Promise<\n    ClientResponse<T extends undefined ? ReturnData<Operation, Operations> : T>\n  > {\n    const response = await this.client.request<T, Operation>(operation, {\n      apiVersion: this.apiVersion || this.storefrontClass().config.apiVersion,\n      ...(options as ApiClientRequestOptions<Operation, StorefrontOperations>),\n    });\n\n    if (response.errors) {\n      const fetchResponse = response.errors.response;\n\n      throwFailedRequest(response, (options?.retries ?? 0) > 0, fetchResponse);\n    }\n\n    return response;\n  }\n\n  private storefrontClass() {\n    return this.constructor as typeof StorefrontClient;\n  }\n}\n\nexport function storefrontClientClass(params: GraphqlClientClassParams) {\n  const {config} = params;\n  class NewStorefrontClient extends StorefrontClient {\n    public static config = config;\n  }\n\n  Reflect.defineProperty(NewStorefrontClient, 'name', {\n    value: 'StorefrontClient',\n  });\n\n  return NewStorefrontClient as typeof StorefrontClient;\n}\n"], "names": ["ShopifyErrors.MissingRequiredArgument", "logger", "MissingRequiredArgument", "createStorefrontApiClient", "abstractFetch", "clientLoggerFactory", "getUserAgent", "throwFailedRequest"], "mappings": ";;;;;;;;;;MA4Ba,gBAAgB,CAAA;IACpB,OAAO,MAAM;AAEX,IAAA,OAAO;AACP,IAAA,MAAM;AACN,IAAA,UAAU;AAEnB,IAAA,WAAA,CAAY,MAA2B,EAAA;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM;AAE5C,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE;AAC3D,YAAA,MAAM,IAAIA,6BAAqC,CAC7C,mDAAmD,CACpD;QACH;AAEA,QAAA,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,MAAM,OAAO,GACX,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC;AAC3B,kBAAE,CAAA,sEAAA,EAAyE,MAAM,CAAC,UAAU,CAAA;kBAC1F,CAAA,iDAAA,EAAoD,MAAM,CAAC,UAAU,SAAS,MAAM,CAAC,UAAU,CAAA,CAAE;YAEvGC,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QAC/B;AAEA,QAAA,IAAI,WAA+B;AACnC,QAAA,IAAI,MAAM,CAAC,gBAAgB,EAAE;AAC3B,YAAA,WAAW,GAAG,MAAM,CAAC,+BAA+B;YAEpD,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,MAAM,IAAIC,6BAAuB,CAC/B,qGAAqG,CACtG;YACH;QACF;aAAO;AACL,YAAA,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW;YAExC,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,MAAM,IAAIA,6BAAuB,CAAC,+BAA+B,CAAC;YACpE;QACF;AAEA,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;AAC7B,QAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU;AACnC,QAAA,IAAI,CAAC,MAAM,GAAGC,6CAAyB,CAAC;AACtC,YAAA,kBAAkB,EAAE,WAAW;AAC/B,YAAA,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU;AAChD,YAAA,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;AAC9B,YAAA,cAAc,EAAEC,qBAAa;AAC7B,YAAA,MAAM,EAAEC,0BAAmB,CAAC,MAAM,CAAC;AACnC,YAAA,UAAU,EAAEC,mBAAY,CAAC,MAAM,CAAC;AACjC,SAAA,CAAC;IACJ;IAEO,MAAM,KAAK,CAChB,MAAqB,EAAA;AAErB,QAAAL,YAAM,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC,UAAU,CAC9C,QAAQ,EACR,6EAA6E;AAC3E,YAAA,uJAAuJ,CAC1J;AAED,QAAA,IACE,CAAC,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAC5D,YAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EACxC;AACA,YAAA,MAAM,IAAID,6BAAqC,CAAC,gBAAgB,CAAC;QACnE;AAEA,QAAA,IAAI,SAAiB;AACrB,QAAA,IAAI,SAA0C;AAC9C,QAAA,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AACnC,YAAA,SAAS,GAAG,MAAM,CAAC,IAAI;QACzB;aAAO;AACL,YAAA,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;AAC7B,YAAA,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS;QACnC;QAEA,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAChC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;YAC/D,GAAG;YACH,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE;AAC3D,SAAA,CAAC,CACH;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAI,SAAS,EAAE;YAChD,OAAO;AACP,YAAA,OAAO,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS;YACpD,SAAS;AACV,SAAA,CAAC;QAEF,OAAO,EAAC,IAAI,EAAE,QAAa,EAAE,OAAO,EAAE,EAAE,EAAC;IAC3C;AAEO,IAAA,MAAM,OAAO,CAKlB,SAAoB,EACpB,OAAoD,EAAA;QAIpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAe,SAAS,EAAE;AAClE,YAAA,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,UAAU;AACvE,YAAA,GAAI,OAAoE;AACzE,SAAA,CAAC;AAEF,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ;AAE9C,YAAAO,yBAAkB,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC;QAC1E;AAEA,QAAA,OAAO,QAAQ;IACjB;IAEQ,eAAe,GAAA;QACrB,OAAO,IAAI,CAAC,WAAsC;IACpD;AACD;AAEK,SAAU,qBAAqB,CAAC,MAAgC,EAAA;AACpE,IAAA,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM;IACvB,MAAM,mBAAoB,SAAQ,gBAAgB,CAAA;AACzC,QAAA,OAAO,MAAM,GAAG,MAAM;;AAG/B,IAAA,OAAO,CAAC,cAAc,CAAC,mBAAmB,EAAE,MAAM,EAAE;AAClD,QAAA,KAAK,EAAE,kBAAkB;AAC1B,KAAA,CAAC;AAEF,IAAA,OAAO,mBAA8C;AACvD;;;;;"}
{"version": 3, "file": "client.mjs", "sources": ["../../src/graphql/client.ts"], "sourcesContent": ["import {\n  createGraphQLClient,\n  getCurrentSupportedApiVersions,\n  validateApiVersion,\n  validateDomainAndGetStoreUrl,\n  generateGetGQLClientParams,\n  generateGetHeaders,\n} from '@shopify/graphql-client';\n\nimport {\n  DEFAULT_CONTENT_TYPE,\n  ACCESS_TOKEN_HEADER,\n  CLIENT,\n  DEFAULT_CLIENT_VERSION,\n} from '../constants';\nimport {\n  validateRequiredAccessToken,\n  validateServerSideUsage,\n} from '../validations';\nimport {AdminApiClientConfig, AdminApiClientOptions} from '../types';\n\nimport {AdminApiClient, AdminOperations} from './types';\n\nexport function createAdminApiClient({\n  storeDomain,\n  apiVersion,\n  accessToken,\n  userAgentPrefix,\n  retries = 0,\n  customFetchApi,\n  logger,\n  isTesting,\n}: AdminApiClientOptions): AdminApiClient {\n  const currentSupportedApiVersions = getCurrentSupportedApiVersions();\n\n  const storeUrl = validateDomainAndGetStoreUrl({\n    client: CLIENT,\n    storeDomain,\n  });\n\n  const baseApiVersionValidationParams = {\n    client: CLIENT,\n    currentSupportedApiVersions,\n    logger,\n  };\n\n  validateServerSideUsage(isTesting);\n  validateApiVersion({\n    client: CLIENT,\n    currentSupportedApiVersions,\n    apiVersion,\n    logger,\n  });\n  validateRequiredAccessToken(accessToken);\n\n  const apiUrlFormatter = generateApiUrlFormatter(\n    storeUrl,\n    apiVersion,\n    baseApiVersionValidationParams,\n  );\n\n  const config: AdminApiClientConfig = {\n    storeDomain: storeUrl,\n    apiVersion,\n    accessToken,\n    headers: {\n      'Content-Type': DEFAULT_CONTENT_TYPE,\n      Accept: DEFAULT_CONTENT_TYPE,\n      [ACCESS_TOKEN_HEADER]: accessToken,\n      'User-Agent': `${\n        userAgentPrefix ? `${userAgentPrefix} | ` : ''\n      }${CLIENT} v${DEFAULT_CLIENT_VERSION}`,\n    },\n    apiUrl: apiUrlFormatter(),\n    userAgentPrefix,\n  };\n\n  const graphqlClient = createGraphQLClient({\n    headers: config.headers,\n    url: config.apiUrl,\n    retries,\n    customFetchApi,\n    logger,\n  });\n\n  const getHeaders = generateGetHeaders(config);\n  const getApiUrl = generateGetApiUrl(config, apiUrlFormatter);\n\n  const getGQLClientParams = generateGetGQLClientParams<AdminOperations>({\n    getHeaders,\n    getApiUrl,\n  });\n\n  const client: AdminApiClient = {\n    config,\n    getHeaders,\n    getApiUrl,\n    fetch: (...props) => {\n      return graphqlClient.fetch(...getGQLClientParams(...props));\n    },\n    request: (...props) => {\n      return graphqlClient.request(...getGQLClientParams(...props));\n    },\n  };\n\n  return Object.freeze(client);\n}\n\nfunction generateApiUrlFormatter(\n  storeUrl: string,\n  defaultApiVersion: string,\n  baseApiVersionValidationParams: Omit<\n    Parameters<typeof validateApiVersion>[0],\n    'apiVersion'\n  >,\n) {\n  return (apiVersion?: string) => {\n    if (apiVersion) {\n      validateApiVersion({\n        ...baseApiVersionValidationParams,\n        apiVersion,\n      });\n    }\n\n    const urlApiVersion = (apiVersion ?? defaultApiVersion).trim();\n\n    return `${storeUrl}/admin/api/${urlApiVersion}/graphql.json`;\n  };\n}\n\nfunction generateGetApiUrl(\n  config: AdminApiClientConfig,\n  apiUrlFormatter: (version?: string) => string,\n): AdminApiClient['getApiUrl'] {\n  return (propApiVersion?: string) => {\n    return propApiVersion ? apiUrlFormatter(propApiVersion) : config.apiUrl;\n  };\n}\n"], "names": [], "mappings": ";;;;AAuBM,SAAU,oBAAoB,CAAC,EACnC,WAAW,EACX,UAAU,EACV,WAAW,EACX,eAAe,EACf,OAAO,GAAG,CAAC,EACX,cAAc,EACd,MAAM,EACN,SAAS,GACa,EAAA;AACtB,IAAA,MAAM,2BAA2B,GAAG,8BAA8B,EAAE;IAEpE,MAAM,QAAQ,GAAG,4BAA4B,CAAC;AAC5C,QAAA,MAAM,EAAE,MAAM;QACd,WAAW;AACZ,KAAA,CAAC;AAEF,IAAA,MAAM,8BAA8B,GAAG;AACrC,QAAA,MAAM,EAAE,MAAM;QACd,2BAA2B;QAC3B,MAAM;KACP;IAED,uBAAuB,CAAC,SAAS,CAAC;AAClC,IAAA,kBAAkB,CAAC;AACjB,QAAA,MAAM,EAAE,MAAM;QACd,2BAA2B;QAC3B,UAAU;QACV,MAAM;AACP,KAAA,CAAC;IACF,2BAA2B,CAAC,WAAW,CAAC;IAExC,MAAM,eAAe,GAAG,uBAAuB,CAC7C,QAAQ,EACR,UAAU,EACV,8BAA8B,CAC/B;AAED,IAAA,MAAM,MAAM,GAAyB;AACnC,QAAA,WAAW,EAAE,QAAQ;QACrB,UAAU;QACV,WAAW;AACX,QAAA,OAAO,EAAE;AACP,YAAA,cAAc,EAAE,oBAAoB;AACpC,YAAA,MAAM,EAAE,oBAAoB;YAC5B,CAAC,mBAAmB,GAAG,WAAW;AAClC,YAAA,YAAY,EAAE,CAAA,EACZ,eAAe,GAAG,CAAA,EAAG,eAAe,CAAA,GAAA,CAAK,GAAG,EAC9C,GAAG,MAAM,CAAA,EAAA,EAAK,sBAAsB,CAAA,CAAE;AACvC,SAAA;QACD,MAAM,EAAE,eAAe,EAAE;QACzB,eAAe;KAChB;IAED,MAAM,aAAa,GAAG,mBAAmB,CAAC;QACxC,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,GAAG,EAAE,MAAM,CAAC,MAAM;QAClB,OAAO;QACP,cAAc;QACd,MAAM;AACP,KAAA,CAAC;AAEF,IAAA,MAAM,UAAU,GAAG,kBAAkB,CAAC,MAAM,CAAC;IAC7C,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,EAAE,eAAe,CAAC;IAE5D,MAAM,kBAAkB,GAAG,0BAA0B,CAAkB;QACrE,UAAU;QACV,SAAS;AACV,KAAA,CAAC;AAEF,IAAA,MAAM,MAAM,GAAmB;QAC7B,MAAM;QACN,UAAU;QACV,SAAS;AACT,QAAA,KAAK,EAAE,CAAC,GAAG,KAAK,KAAI;YAClB,OAAO,aAAa,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7D,CAAC;AACD,QAAA,OAAO,EAAE,CAAC,GAAG,KAAK,KAAI;YACpB,OAAO,aAAa,CAAC,OAAO,CAAC,GAAG,kBAAkB,CAAC,GAAG,KAAK,CAAC,CAAC;QAC/D,CAAC;KACF;AAED,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9B;AAEA,SAAS,uBAAuB,CAC9B,QAAgB,EAChB,iBAAyB,EACzB,8BAGC,EAAA;IAED,OAAO,CAAC,UAAmB,KAAI;QAC7B,IAAI,UAAU,EAAE;AACd,YAAA,kBAAkB,CAAC;AACjB,gBAAA,GAAG,8BAA8B;gBACjC,UAAU;AACX,aAAA,CAAC;QACJ;QAEA,MAAM,aAAa,GAAG,CAAC,UAAU,IAAI,iBAAiB,EAAE,IAAI,EAAE;AAE9D,QAAA,OAAO,CAAA,EAAG,QAAQ,CAAA,WAAA,EAAc,aAAa,eAAe;AAC9D,IAAA,CAAC;AACH;AAEA,SAAS,iBAAiB,CACxB,MAA4B,EAC5B,eAA6C,EAAA;IAE7C,OAAO,CAAC,cAAuB,KAAI;AACjC,QAAA,OAAO,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,MAAM;AACzE,IAAA,CAAC;AACH;;;;"}
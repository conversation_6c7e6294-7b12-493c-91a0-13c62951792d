import React, { useState, useEffect } from 'react'
import { Package, Calendar, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { api } from '../../services/api'
import type { ReturnRecord } from '../../services/types'
import styles from './ReturnsList.module.css'

interface ReturnsListProps {
  onReturnSelect?: (returnId: string) => void
}

export const ReturnsList: React.FC<ReturnsListProps> = ({ onReturnSelect }) => {
  const [returns, setReturns] = useState<ReturnRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'completed'>('all')

  useEffect(() => {
    loadReturns()
  }, [])

  const loadReturns = async () => {
    try {
      setLoading(true)
      const data = await api.getReturns()
      setReturns(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load returns')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: ReturnRecord['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className={styles.statusIconPending} />
      case 'approved':
        return <CheckCircle className={styles.statusIconApproved} />
      case 'rejected':
        return <XCircle className={styles.statusIconRejected} />
      case 'completed':
        return <CheckCircle className={styles.statusIconCompleted} />
      default:
        return <AlertCircle className={styles.statusIconDefault} />
    }
  }

  const getStatusClass = (status: ReturnRecord['status']) => {
    return styles[`status${status.charAt(0).toUpperCase() + status.slice(1)}`] || styles.statusDefault
  }

  const filteredReturns = returns.filter(returnRecord => 
    filter === 'all' || returnRecord.status === filter
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading returns...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <AlertCircle size={24} />
          <p>{error}</p>
          <button onClick={loadReturns} className={styles.retryButton}>
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Returns Management</h2>
        <div className={styles.filterTabs}>
          <button
            className={`${styles.filterTab} ${filter === 'all' ? styles.active : ''}`}
            onClick={() => setFilter('all')}
          >
            All ({returns.length})
          </button>
          <button
            className={`${styles.filterTab} ${filter === 'pending' ? styles.active : ''}`}
            onClick={() => setFilter('pending')}
          >
            Pending ({returns.filter(r => r.status === 'pending').length})
          </button>
          <button
            className={`${styles.filterTab} ${filter === 'approved' ? styles.active : ''}`}
            onClick={() => setFilter('approved')}
          >
            Approved ({returns.filter(r => r.status === 'approved').length})
          </button>
          <button
            className={`${styles.filterTab} ${filter === 'completed' ? styles.active : ''}`}
            onClick={() => setFilter('completed')}
          >
            Completed ({returns.filter(r => r.status === 'completed').length})
          </button>
        </div>
      </div>

      {filteredReturns.length === 0 ? (
        <div className={styles.empty}>
          <Package size={48} />
          <h3>No returns found</h3>
          <p>
            {filter === 'all' 
              ? 'No returns have been submitted yet.'
              : `No ${filter} returns found.`
            }
          </p>
        </div>
      ) : (
        <div className={styles.returnsList}>
          {filteredReturns.map(returnRecord => (
            <div
              key={returnRecord.id}
              className={styles.returnCard}
              onClick={() => onReturnSelect?.(returnRecord.id)}
            >
              <div className={styles.returnHeader}>
                <div className={styles.returnInfo}>
                  <h4>Return #{returnRecord.id.slice(-8)}</h4>
                  <p className={styles.orderInfo}>
                    Order: {returnRecord.shopify_order_id}
                  </p>
                </div>
                <div className={`${styles.status} ${getStatusClass(returnRecord.status)}`}>
                  {getStatusIcon(returnRecord.status)}
                  <span>{returnRecord.status.charAt(0).toUpperCase() + returnRecord.status.slice(1)}</span>
                </div>
              </div>

              <div className={styles.returnDetails}>
                <div className={styles.customer}>
                  <strong>Customer:</strong> {returnRecord.customer_email}
                </div>
                <div className={styles.items}>
                  <strong>Items:</strong> {returnRecord.return_items.length} item{returnRecord.return_items.length !== 1 ? 's' : ''}
                </div>
                <div className={styles.date}>
                  <Calendar size={16} />
                  <span>Created: {formatDate(returnRecord.created_at)}</span>
                </div>
              </div>

              {returnRecord.tracking_number && (
                <div className={styles.tracking}>
                  <strong>Tracking:</strong> {returnRecord.tracking_number}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

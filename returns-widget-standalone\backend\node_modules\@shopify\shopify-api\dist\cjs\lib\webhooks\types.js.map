{"version": 3, "file": "types.js", "sources": ["../../../../../../lib/webhooks/types.ts"], "sourcesContent": ["import {ValidationErrorReason, ValidationInvalid} from '../utils/types';\nimport {AdapterArgs} from '../../runtime/types';\nimport {Session} from '../session/session';\n\nexport enum DeliveryMethod {\n  Http = 'http',\n  EventBridge = 'eventbridge',\n  PubSub = 'pubsub',\n}\n\nexport type WebhookHandlerFunction = (\n  topic: string,\n  shop_domain: string,\n  body: string,\n  webhookId: string,\n  apiVersion?: string,\n  subTopic?: string,\n  context?: any,\n) => Promise<void>;\n\ninterface BaseWebhookHandler {\n  id?: string;\n  includeFields?: string[];\n  metafieldNamespaces?: string[];\n  subTopic?: string;\n  context?: any;\n}\n\nexport interface HttpWebhookHandler extends BaseWebhookHandler {\n  deliveryMethod: DeliveryMethod.Http;\n  callbackUrl: string;\n}\n\nexport interface HttpWebhookHandlerWithCallback extends HttpWebhookHandler {\n  callback: WebhookHandlerFunction;\n}\n\nexport interface EventBridgeWebhookHandler extends BaseWebhookHandler {\n  deliveryMethod: DeliveryMethod.EventBridge;\n  arn: string;\n}\n\nexport interface PubSubWebhookHandler extends BaseWebhookHandler {\n  deliveryMethod: DeliveryMethod.PubSub;\n  pubSubProject: string;\n  pubSubTopic: string;\n}\n\nexport type WebhookHandler =\n  | HttpWebhookHandler\n  | HttpWebhookHandlerWithCallback\n  | EventBridgeWebhookHandler\n  | PubSubWebhookHandler;\n\n// See https://shopify.dev/docs/api/admin-graphql/latest/enums/webhooksubscriptiontopic for available topics\nexport type WebhookRegistry<Handler extends WebhookHandler = WebhookHandler> =\n  Record<string, Handler[]>;\n\n// eslint-disable-next-line no-warning-comments\n// TODO Rethink the wording for this enum - the operations we're doing are actually \"subscribing\" and \"unsubscribing\"\n// Consider changing the values when releasing v12.0.0 when it can be safely deprecated\nexport enum WebhookOperation {\n  Create = 'create',\n  Update = 'update',\n  Delete = 'delete',\n}\n\nexport interface RegisterParams {\n  session: Session;\n}\n\nexport interface RegisterResult {\n  success: boolean;\n  deliveryMethod: DeliveryMethod;\n  result: unknown;\n  operation: WebhookOperation;\n}\n\nexport type RegisterReturn = Record<string, RegisterResult[]>;\n\ninterface WebhookHttpEndpoint {\n  __typename: 'WebhookHttpEndpoint';\n  callbackUrl: string;\n}\ninterface WebhookEventBridgeEndpoint {\n  __typename: 'WebhookEventBridgeEndpoint';\n  arn: string;\n}\ninterface WebhookPubSubEndpoint {\n  __typename: 'WebhookPubSubEndpoint';\n  pubSubProject: string;\n  pubSubTopic: string;\n}\n\ntype WebhookEndpoint =\n  | WebhookHttpEndpoint\n  | WebhookEventBridgeEndpoint\n  | WebhookPubSubEndpoint;\n\nexport interface WebhookCheckResponseNode<\n  T = {\n    endpoint: WebhookEndpoint;\n  },\n> {\n  node: {\n    id: string;\n    topic: string;\n    includeFields: string[];\n    metafieldNamespaces: string[];\n  } & T;\n}\n\nexport interface WebhookCheckResponse<T = WebhookCheckResponseNode> {\n  webhookSubscriptions: {\n    edges: T[];\n    pageInfo: {\n      endCursor: string;\n      hasNextPage: boolean;\n    };\n  };\n}\n\nexport type AddHandlersParams = Record<\n  string,\n  WebhookHandler | WebhookHandler[]\n>;\n\nexport interface WebhookProcessParams extends AdapterArgs {\n  rawBody: string;\n  context?: any;\n}\n\nexport interface WebhookValidateParams extends WebhookProcessParams {}\n\nexport const WebhookValidationErrorReason = {\n  ...ValidationErrorReason,\n  MissingHeaders: 'missing_headers',\n} as const;\n\nexport type WebhookValidationErrorReasonType =\n  (typeof WebhookValidationErrorReason)[keyof typeof WebhookValidationErrorReason];\n\nexport interface WebhookFields {\n  webhookId: string;\n  apiVersion: string;\n  domain: string;\n  hmac: string;\n  topic: string;\n  subTopic?: string;\n}\n\nexport interface WebhookValidationInvalid\n  extends Omit<ValidationInvalid, 'reason'> {\n  valid: false;\n  reason: WebhookValidationErrorReasonType;\n}\n\nexport interface WebhookValidationMissingHeaders\n  extends WebhookValidationInvalid {\n  reason: typeof WebhookValidationErrorReason.MissingHeaders;\n  missingHeaders: string[];\n}\n\nexport interface WebhookValidationValid extends WebhookFields {\n  valid: true;\n}\n\nexport type WebhookValidation =\n  | WebhookValidationValid\n  | WebhookValidationInvalid\n  | WebhookValidationMissingHeaders;\n"], "names": ["DeliveryMethod", "WebhookOperation", "ValidationErrorReason"], "mappings": ";;;;AAIYA;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,aAA2B;AAC3B,IAAA,cAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EAJWA,sBAAc,KAAdA,sBAAc,GAAA,EAAA,CAAA,CAAA;AAsD1B;AACA;AACA;AACYC;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EAJWA,wBAAgB,KAAhBA,wBAAgB,GAAA,EAAA,CAAA,CAAA;AAyErB,MAAM,4BAA4B,GAAG;AAC1C,IAAA,GAAGC,2BAAqB;AACxB,IAAA,cAAc,EAAE,iBAAiB;;;;;"}
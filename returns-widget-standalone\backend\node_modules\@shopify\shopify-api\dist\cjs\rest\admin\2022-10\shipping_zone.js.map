{"version": 3, "file": "shipping_zone.js", "sources": ["../../../../../../../rest/admin/2022-10/shipping_zone.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\nimport {Country} from './country';\nimport {Province} from './province';\n\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  fields?: unknown;\n}\n\nexport class ShippingZone extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {\n    \"countries\": Country,\n    \"provinces\": Province\n  };\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"shipping_zones.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"shipping_zone\",\n      \"plural\": \"shipping_zones\"\n    }\n  ];\n\n  public static async all(\n    {\n      session,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<ShippingZone>> {\n    const response = await this.baseFind<ShippingZone>({\n      session: session,\n      urlIds: {},\n      params: {\"fields\": fields, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public carrier_shipping_rate_providers: unknown | null;\n  public countries: Country[] | null | {[key: string]: any};\n  public id: number | null;\n  public location_group_id: string | null;\n  public name: string | null;\n  public price_based_shipping_rates: {[key: string]: unknown}[] | null;\n  public profile_id: string | null;\n  public provinces: Province[] | null | {[key: string]: any};\n  public weight_based_shipping_rates: {[key: string]: unknown}[] | null;\n}\n"], "names": ["Base", "ApiVersion", "Country", "Province"], "mappings": ";;;;;;;AAAA;;AAEwH;AAgBlH,MAAO,YAAa,SAAQA,SAAI,CAAA;AAC7B,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;IAChD,OAAO,OAAO,GAAiC;AACvD,QAAA,WAAW,EAAEC,eAAO;AACpB,QAAA,WAAW,EAAEC;KACd;IACS,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,qBAAqB;KACpF;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,eAAe;AAC3B,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAe;AACjD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AACzC,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,+BAA+B;AAC/B,IAAA,SAAS;AACT,IAAA,EAAE;AACF,IAAA,iBAAiB;AACjB,IAAA,IAAI;AACJ,IAAA,0BAA0B;AAC1B,IAAA,UAAU;AACV,IAAA,SAAS;AACT,IAAA,2BAA2B;;;;;"}
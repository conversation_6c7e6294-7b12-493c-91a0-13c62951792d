{"version": 3, "file": "get-hmac-key.js", "sources": ["../../../../../../lib/utils/get-hmac-key.ts"], "sourcesContent": ["export function getHMAC<PERSON>ey(key: string): Uint8Array {\n  const arrayBuffer = new Uint8Array(key.length);\n  for (let i = 0, keyLen = key.length; i < keyLen; i++) {\n    arrayBuffer[i] = key.charCodeAt(i);\n  }\n\n  return arrayBuffer;\n}\n"], "names": [], "mappings": ";;AAAM,SAAU,UAAU,CAAC,GAAW,EAAA;IACpC,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC;AAC9C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QACpD,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IACpC;AAEA,IAAA,OAAO,WAAW;AACpB;;;;"}
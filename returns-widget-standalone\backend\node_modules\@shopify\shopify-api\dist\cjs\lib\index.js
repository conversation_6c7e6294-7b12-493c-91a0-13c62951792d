'use strict';

var loadRestResources = require('../rest/load-rest-resources.js');
var runtimeString = require('../runtime/platform/runtime-string.js');
var flags = require('../future/flags.js');
var config = require('./config.js');
var index$1 = require('./clients/index.js');
var index$9 = require('./auth/index.js');
var index$8 = require('./session/index.js');
var index$7 = require('./utils/index.js');
var index$6 = require('./webhooks/index.js');
var index$5 = require('./billing/index.js');
var index$2 = require('./logger/index.js');
var version = require('./version.js');
var client$1 = require('./clients/admin/rest/client.js');
var index$4 = require('./flow/index.js');
var index$3 = require('./fulfillment-service/index.js');
var error = require('./error.js');
var session = require('./session/session.js');
var types = require('./types.js');
var types$1 = require('./auth/oauth/types.js');
var index = require('./auth/scopes/index.js');
var tokenExchange = require('./auth/oauth/token-exchange.js');
var types$2 = require('./billing/types.js');
var types$3 = require('./clients/types.js');
var types$4 = require('./webhooks/types.js');
var types$5 = require('./utils/types.js');
var client = require('./clients/admin/graphql/client.js');

function shopifyApi({ future, restResources, ...config$1 }) {
    const libConfig = { ...config$1, future, restResources };
    const validatedConfig = config.validateConfig(libConfig);
    const shopify = {
        config: validatedConfig,
        clients: index$1.clientClasses(validatedConfig),
        auth: index$9.shopifyAuth(validatedConfig),
        session: index$8.shopifySession(validatedConfig),
        utils: index$7.shopifyUtils(validatedConfig),
        webhooks: index$6.shopifyWebhooks(validatedConfig),
        billing: index$5.shopifyBilling(validatedConfig),
        flow: index$4.shopifyFlow(validatedConfig),
        fulfillmentService: index$3.fulfillmentService(validatedConfig),
        logger: index$2.logger(validatedConfig),
        rest: {},
    };
    if (restResources) {
        shopify.rest = loadRestResources.loadRestResources({
            resources: restResources,
            config: validatedConfig,
            RestClient: client$1.restClientClass({ config: validatedConfig }),
        });
    }
    shopify.logger
        .info(`version ${version.SHOPIFY_API_LIBRARY_VERSION}, environment ${runtimeString.abstractRuntimeString()}`)
        .catch((err) => console.log(err));
    flags.logDisabledFutureFlags(validatedConfig, shopify.logger);
    return shopify;
}

exports.RestClient = client$1.RestClient;
exports.BillingError = error.BillingError;
exports.BotActivityDetected = error.BotActivityDetected;
exports.CookieNotFound = error.CookieNotFound;
exports.FeatureDeprecatedError = error.FeatureDeprecatedError;
exports.GraphqlQueryError = error.GraphqlQueryError;
exports.HttpInternalError = error.HttpInternalError;
exports.HttpMaxRetriesError = error.HttpMaxRetriesError;
exports.HttpRequestError = error.HttpRequestError;
exports.HttpResponseError = error.HttpResponseError;
exports.HttpRetriableError = error.HttpRetriableError;
exports.HttpThrottlingError = error.HttpThrottlingError;
exports.InvalidDeliveryMethodError = error.InvalidDeliveryMethodError;
exports.InvalidHmacError = error.InvalidHmacError;
exports.InvalidHostError = error.InvalidHostError;
exports.InvalidJwtError = error.InvalidJwtError;
exports.InvalidOAuthError = error.InvalidOAuthError;
exports.InvalidRequestError = error.InvalidRequestError;
exports.InvalidSession = error.InvalidSession;
exports.InvalidShopError = error.InvalidShopError;
exports.InvalidWebhookError = error.InvalidWebhookError;
exports.MissingJwtTokenError = error.MissingJwtTokenError;
exports.MissingRequiredArgument = error.MissingRequiredArgument;
exports.MissingWebhookCallbackError = error.MissingWebhookCallbackError;
exports.PrivateAppError = error.PrivateAppError;
exports.RestResourceError = error.RestResourceError;
exports.SafeCompareError = error.SafeCompareError;
exports.SessionStorageError = error.SessionStorageError;
exports.ShopifyError = error.ShopifyError;
exports.UnsupportedClientType = error.UnsupportedClientType;
exports.Session = session.Session;
Object.defineProperty(exports, "ApiVersion", {
    enumerable: true,
    get: function () { return types.ApiVersion; }
});
Object.defineProperty(exports, "BillingInterval", {
    enumerable: true,
    get: function () { return types.BillingInterval; }
});
Object.defineProperty(exports, "BillingReplacementBehavior", {
    enumerable: true,
    get: function () { return types.BillingReplacementBehavior; }
});
Object.defineProperty(exports, "ClientType", {
    enumerable: true,
    get: function () { return types.ClientType; }
});
exports.LATEST_API_VERSION = types.LATEST_API_VERSION;
exports.LIBRARY_NAME = types.LIBRARY_NAME;
Object.defineProperty(exports, "LogSeverity", {
    enumerable: true,
    get: function () { return types.LogSeverity; }
});
exports.RELEASE_CANDIDATE_API_VERSION = types.RELEASE_CANDIDATE_API_VERSION;
Object.defineProperty(exports, "ShopifyHeader", {
    enumerable: true,
    get: function () { return types.ShopifyHeader; }
});
exports.privacyTopics = types.privacyTopics;
exports.SESSION_COOKIE_NAME = types$1.SESSION_COOKIE_NAME;
exports.STATE_COOKIE_NAME = types$1.STATE_COOKIE_NAME;
exports.AuthScopes = index.AuthScopes;
Object.defineProperty(exports, "RequestedTokenType", {
    enumerable: true,
    get: function () { return tokenExchange.RequestedTokenType; }
});
exports.APP_SUBSCRIPTION_FRAGMENT = types$2.APP_SUBSCRIPTION_FRAGMENT;
Object.defineProperty(exports, "DataType", {
    enumerable: true,
    get: function () { return types$3.DataType; }
});
Object.defineProperty(exports, "DeliveryMethod", {
    enumerable: true,
    get: function () { return types$4.DeliveryMethod; }
});
Object.defineProperty(exports, "WebhookOperation", {
    enumerable: true,
    get: function () { return types$4.WebhookOperation; }
});
exports.WebhookValidationErrorReason = types$4.WebhookValidationErrorReason;
Object.defineProperty(exports, "HmacValidationType", {
    enumerable: true,
    get: function () { return types$5.HmacValidationType; }
});
exports.ValidationErrorReason = types$5.ValidationErrorReason;
exports.GraphqlClient = client.GraphqlClient;
exports.shopifyApi = shopifyApi;
//# sourceMappingURL=index.js.map

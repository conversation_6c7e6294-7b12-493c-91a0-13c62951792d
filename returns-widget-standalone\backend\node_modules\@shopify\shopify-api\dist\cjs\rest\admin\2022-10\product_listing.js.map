{"version": 3, "file": "product_listing.js", "sources": ["../../../../../../../rest/admin/2022-10/product_listing.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\nimport {Image} from './image';\nimport {Variant} from './variant';\n\ninterface FindArgs {\n  session: Session;\n  product_id: number | string;\n}\ninterface DeleteArgs {\n  session: Session;\n  product_id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  product_ids?: unknown;\n  limit?: unknown;\n  collection_id?: unknown;\n  updated_at_min?: unknown;\n  handle?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n}\ninterface ProductIdsArgs {\n  [key: string]: unknown;\n  session: Session;\n  limit?: unknown;\n}\n\nexport class ProductListing extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {\n    \"images\": Image,\n    \"variants\": Variant\n  };\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"product_id\"], \"path\": \"product_listings/<product_id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [], \"path\": \"product_listings/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"product_listings.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"product_id\"], \"path\": \"product_listings/<product_id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"product_ids\", \"ids\": [], \"path\": \"product_listings/product_ids.json\"},\n    {\"http_method\": \"put\", \"operation\": \"put\", \"ids\": [\"product_id\"], \"path\": \"product_listings/<product_id>.json\"}\n  ];\n  protected static primaryKey: string = \"product_id\";\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"product_listing\",\n      \"plural\": \"product_listings\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      product_id\n    }: FindArgs\n  ): Promise<ProductListing | null> {\n    const result = await this.baseFind<ProductListing>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"product_id\": product_id},\n      params: {},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async delete(\n    {\n      session,\n      product_id\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<ProductListing>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"product_id\": product_id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      product_ids = null,\n      limit = null,\n      collection_id = null,\n      updated_at_min = null,\n      handle = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<ProductListing>> {\n    const response = await this.baseFind<ProductListing>({\n      session: session,\n      urlIds: {},\n      params: {\"product_ids\": product_ids, \"limit\": limit, \"collection_id\": collection_id, \"updated_at_min\": updated_at_min, \"handle\": handle, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<ProductListing>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {},\n      params: {...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async product_ids(\n    {\n      session,\n      limit = null,\n      ...otherArgs\n    }: ProductIdsArgs\n  ): Promise<unknown> {\n    const response = await this.request<ProductListing>({\n      http_method: \"get\",\n      operation: \"product_ids\",\n      session: session,\n      urlIds: {},\n      params: {\"limit\": limit, ...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public body_html: string | null;\n  public created_at: string | null;\n  public handle: string | null;\n  public images: Image[] | null | {[key: string]: any};\n  public options: {[key: string]: unknown}[] | null;\n  public product_id: number | null;\n  public product_type: string | null;\n  public published_at: string | null;\n  public tags: string | null;\n  public title: string | null;\n  public updated_at: string | null;\n  public variants: Variant[] | null | {[key: string]: any};\n  public vendor: string | null;\n}\n"], "names": ["Base", "ApiVersion", "Image", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;AAAA;;AAEwH;AAqClH,MAAO,cAAe,SAAQA,SAAI,CAAA;AAC/B,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;IAChD,OAAO,OAAO,GAAiC;AACvD,QAAA,QAAQ,EAAEC,WAAK;AACf,QAAA,UAAU,EAAEC;KACb;IACS,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,oCAAoC,EAAC;AACrH,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,6BAA6B,EAAC;AAC9F,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,uBAAuB,EAAC;AACtF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,oCAAoC,EAAC;AAC/G,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,mCAAmC,EAAC;AAC1G,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,oCAAoC;KAC/G;AACS,IAAA,OAAO,UAAU,GAAW,YAAY;IACxC,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,iBAAiB;AAC7B,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,IAAI,CACtB,EACE,OAAO,EACP,UAAU,EACD,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAiB;AACjD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,YAAY,EAAE,UAAU,EAAC;AAClC,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,MAAM,CACxB,EACE,OAAO,EACP,UAAU,EACC,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAiB;AAClD,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,YAAY,EAAE,UAAU,EAAC;AAClC,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,WAAW,GAAG,IAAI,EAClB,KAAK,GAAG,IAAI,EACZ,aAAa,GAAG,IAAI,EACpB,cAAc,GAAG,IAAI,EACrB,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAiB;AACnD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AACvJ,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;IAEO,aAAa,KAAK,CACvB,EACE,OAAO,EACP,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAiB;AAClD,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACtB,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,aAAa,WAAW,CAC7B,EACE,OAAO,EACP,KAAK,GAAG,IAAI,EACZ,GAAG,SAAS,EACG,EAAA;AAEjB,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAiB;AAClD,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,aAAa;AACxB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS,EAAC;AACtC,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,SAAS;AACT,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,MAAM;AACN,IAAA,OAAO;AACP,IAAA,UAAU;AACV,IAAA,YAAY;AACZ,IAAA,YAAY;AACZ,IAAA,IAAI;AACJ,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,QAAQ;AACR,IAAA,MAAM;;;;;"}
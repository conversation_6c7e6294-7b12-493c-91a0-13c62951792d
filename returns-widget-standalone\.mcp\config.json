{"mcpServers": {"shopify-api": {"command": "node", "args": ["mcp-shopify-server.js"], "env": {"SHOPIFY_ACCESS_TOKEN": "", "SHOPIFY_DOMAIN": ""}}, "sendcloud-api": {"command": "node", "args": ["mcp-sendcloud-server.js"], "env": {"SENDCLOUD_PUBLIC_KEY": "", "SENDCLOUD_SECRET_KEY": ""}}, "database-tools": {"command": "node", "args": ["mcp-database-server.js"], "env": {"DATABASE_PATH": "./backend/database.sqlite"}}, "testing-tools": {"command": "node", "args": ["mcp-testing-server.js"]}}}
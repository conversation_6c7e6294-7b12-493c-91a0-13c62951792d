"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = void 0;
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const crypto_1 = __importDefault(require("crypto"));
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';
class DatabaseManager {
    constructor() {
        this.db = new better_sqlite3_1.default('merchants.db');
        this.initTables();
    }
    initTables() {
        // Create merchants table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS merchants (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        store_name TEXT NOT NULL,
        shopify_domain TEXT NOT NULL,
        shopify_api_key TEXT,
        shopify_api_secret TEXT,
        sendcloud_api_key TEXT,
        sendcloud_api_secret TEXT,
        is_active BOOLEAN DEFAULT 1,
        plan TEXT DEFAULT 'free',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // Create merchant settings table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS merchant_settings (
        merchant_id TEXT PRIMARY KEY,
        widget_branding_color TEXT DEFAULT '#3b82f6',
        widget_button_text TEXT DEFAULT 'Start Return',
        return_reasons TEXT DEFAULT '["Defective", "Wrong Size", "Changed Mind", "Damaged in Shipping"]',
        auto_approve_returns BOOLEAN DEFAULT 1,
        require_photos BOOLEAN DEFAULT 0,
        return_window_days INTEGER DEFAULT 30,
        FOREIGN KEY (merchant_id) REFERENCES merchants (id)
      )
    `);
        // Create returns table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS returns (
        id TEXT PRIMARY KEY,
        merchant_id TEXT NOT NULL,
        shopify_order_id TEXT NOT NULL,
        customer_email TEXT NOT NULL,
        return_items TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        tracking_number TEXT,
        label_url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (merchant_id) REFERENCES merchants (id)
      )
    `);
        // Migrate existing returns table if needed
        try {
            this.db.exec(`ALTER TABLE returns ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP`);
        }
        catch (error) {
            // Column already exists, ignore error
        }
        try {
            this.db.exec(`ALTER TABLE returns ADD COLUMN tracking_number TEXT`);
        }
        catch (error) {
            // Column already exists, ignore error
        }
        try {
            this.db.exec(`ALTER TABLE returns ADD COLUMN order_number TEXT`);
        }
        catch (error) {
            // Column already exists, ignore error
        }
        try {
            this.db.exec(`ALTER TABLE returns ADD COLUMN notes TEXT`);
        }
        catch (error) {
            // Column already exists, ignore error
        }
        try {
            this.db.exec(`ALTER TABLE returns ADD COLUMN carrier_name TEXT`);
        }
        catch (error) {
            // Column already exists, ignore error
        }
        try {
            this.db.exec(`ALTER TABLE returns ADD COLUMN service_id TEXT`);
        }
        catch (error) {
            // Column already exists, ignore error
        }
    }
    // Encryption utilities
    encrypt(text) {
        const algorithm = 'aes-256-cbc';
        const key = crypto_1.default.scryptSync(ENCRYPTION_KEY, 'salt', 32);
        const iv = crypto_1.default.randomBytes(16);
        const cipher = crypto_1.default.createCipheriv(algorithm, key, iv);
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return iv.toString('hex') + ':' + encrypted;
    }
    decrypt(encryptedText) {
        try {
            // Check if this is new format (with IV prefix)
            if (encryptedText.includes(':')) {
                const algorithm = 'aes-256-cbc';
                const key = crypto_1.default.scryptSync(ENCRYPTION_KEY, 'salt', 32);
                const parts = encryptedText.split(':');
                if (parts.length !== 2) {
                    throw new Error('Invalid encrypted text format');
                }
                const iv = Buffer.from(parts[0], 'hex');
                const encrypted = parts[1];
                if (iv.length !== 16) {
                    throw new Error('Invalid IV length');
                }
                const decipher = crypto_1.default.createDecipheriv(algorithm, key, iv);
                let decrypted = decipher.update(encrypted, 'hex', 'utf8');
                decrypted += decipher.final('utf8');
                return decrypted;
            }
            else {
                // Fallback to old format (deprecated createDecipher)
                const decipher = crypto_1.default.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
                let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
                decrypted += decipher.final('utf8');
                return decrypted;
            }
        }
        catch (error) {
            console.error('Decryption failed:', error);
            throw new Error('Failed to decrypt data');
        }
    }
    // Merchant operations
    async createMerchant(merchantData) {
        const id = crypto_1.default.randomUUID();
        const hashedPassword = await bcryptjs_1.default.hash(merchantData.password, 10);
        const stmt = this.db.prepare(`
      INSERT INTO merchants (id, email, password, store_name, shopify_domain, shopify_api_key, shopify_api_secret, sendcloud_api_key, sendcloud_api_secret, is_active, plan, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);
        stmt.run(id, merchantData.email, hashedPassword, merchantData.storeName, merchantData.shopifyDomain, merchantData.shopifyApiKey && merchantData.shopifyApiKey.length > 0 ? this.encrypt(merchantData.shopifyApiKey) : null, merchantData.shopifyApiSecret && merchantData.shopifyApiSecret.length > 0 ? this.encrypt(merchantData.shopifyApiSecret) : null, merchantData.sendcloudApiKey && merchantData.sendcloudApiKey.length > 0 ? this.encrypt(merchantData.sendcloudApiKey) : null, merchantData.sendcloudApiSecret && merchantData.sendcloudApiSecret.length > 0 ? this.encrypt(merchantData.sendcloudApiSecret) : null, merchantData.isActive ? 1 : 0, merchantData.plan);
        // Create default settings
        this.db.prepare(`
      INSERT INTO merchant_settings (merchant_id) VALUES (?)
    `).run(id);
        return id;
    }
    getMerchantByEmail(email) {
        const stmt = this.db.prepare('SELECT * FROM merchants WHERE email = ?');
        const row = stmt.get(email);
        if (!row)
            return null;
        return {
            id: row.id,
            email: row.email,
            password: row.password,
            storeName: row.store_name,
            shopifyDomain: row.shopify_domain,
            shopifyApiKey: row.shopify_api_key ? this.decrypt(row.shopify_api_key) : '',
            shopifyApiSecret: row.shopify_api_secret ? this.decrypt(row.shopify_api_secret) : '',
            sendcloudApiKey: row.sendcloud_api_key ? this.decrypt(row.sendcloud_api_key) : '',
            sendcloudApiSecret: row.sendcloud_api_secret ? this.decrypt(row.sendcloud_api_secret) : '',
            isActive: Boolean(row.is_active),
            plan: row.plan,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        };
    }
    getMerchantById(id) {
        const stmt = this.db.prepare('SELECT * FROM merchants WHERE id = ?');
        const row = stmt.get(id);
        if (!row)
            return null;
        return {
            id: row.id,
            email: row.email,
            password: row.password,
            storeName: row.store_name,
            shopifyDomain: row.shopify_domain,
            shopifyApiKey: row.shopify_api_key ? this.decrypt(row.shopify_api_key) : '',
            shopifyApiSecret: row.shopify_api_secret ? this.decrypt(row.shopify_api_secret) : '',
            sendcloudApiKey: row.sendcloud_api_key ? this.decrypt(row.sendcloud_api_key) : '',
            sendcloudApiSecret: row.sendcloud_api_secret ? this.decrypt(row.sendcloud_api_secret) : '',
            isActive: Boolean(row.is_active),
            plan: row.plan,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        };
    }
    updateMerchantCredentials(merchantId, credentials) {
        const updates = [];
        const values = [];
        if (credentials.shopifyApiKey) {
            updates.push('shopify_api_key = ?');
            values.push(this.encrypt(credentials.shopifyApiKey));
        }
        if (credentials.shopifyApiSecret) {
            updates.push('shopify_api_secret = ?');
            values.push(this.encrypt(credentials.shopifyApiSecret));
        }
        if (credentials.sendcloudApiKey) {
            updates.push('sendcloud_api_key = ?');
            values.push(this.encrypt(credentials.sendcloudApiKey));
        }
        if (credentials.sendcloudApiSecret) {
            updates.push('sendcloud_api_secret = ?');
            values.push(this.encrypt(credentials.sendcloudApiSecret));
        }
        if (updates.length > 0) {
            updates.push('updated_at = CURRENT_TIMESTAMP');
            values.push(merchantId);
            const stmt = this.db.prepare(`UPDATE merchants SET ${updates.join(', ')} WHERE id = ?`);
            stmt.run(...values);
        }
    }
    // Return operations
    createReturn(returnData) {
        const id = crypto_1.default.randomUUID();
        const stmt = this.db.prepare(`
      INSERT INTO returns (id, merchant_id, shopify_order_id, order_number, customer_email, return_items, status, notes, carrier_name, service_id, tracking_number, label_url, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);
        stmt.run(id, returnData.merchant_id, returnData.shopify_order_id, returnData.order_number, returnData.customer_email, JSON.stringify(returnData.return_items), returnData.status, returnData.notes || null, 'dashboard', // carrier_name - default value for dashboard returns
        'dashboard', // service_id - default value for dashboard returns
        returnData.tracking_number || null, returnData.label_url || null);
        return id;
    }
    getReturnsByMerchant(merchantId) {
        const stmt = this.db.prepare('SELECT * FROM returns WHERE merchant_id = ? ORDER BY created_at DESC');
        const rows = stmt.all(merchantId);
        return rows.map(row => ({
            id: row.id,
            merchant_id: row.merchant_id,
            shopify_order_id: row.shopify_order_id,
            order_number: row.order_number,
            customer_email: row.customer_email,
            return_items: JSON.parse(row.return_items),
            status: row.status,
            notes: row.notes,
            created_at: row.created_at,
            updated_at: row.updated_at || row.created_at,
            tracking_number: row.tracking_number,
            label_url: row.label_url
        }));
    }
    getReturnById(returnId, merchantId) {
        const stmt = this.db.prepare('SELECT * FROM returns WHERE id = ? AND merchant_id = ?');
        const row = stmt.get(returnId, merchantId);
        if (!row)
            return null;
        return {
            id: row.id,
            merchant_id: row.merchant_id,
            shopify_order_id: row.shopify_order_id,
            order_number: row.order_number,
            customer_email: row.customer_email,
            return_items: JSON.parse(row.return_items),
            status: row.status,
            notes: row.notes,
            created_at: row.created_at,
            updated_at: row.updated_at || row.created_at,
            tracking_number: row.tracking_number,
            label_url: row.label_url
        };
    }
    updateReturnStatus(returnId, merchantId, status) {
        const stmt = this.db.prepare(`
      UPDATE returns SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND merchant_id = ?
    `);
        const result = stmt.run(status, returnId, merchantId);
        return result.changes > 0;
    }
    updateReturn(returnId, updates) {
        const fields = Object.keys(updates).filter(key => key !== 'id').map(key => `${key} = ?`);
        const values = Object.keys(updates).filter(key => key !== 'id').map(key => updates[key]);
        if (fields.length === 0)
            return false;
        const stmt = this.db.prepare(`
      UPDATE returns SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?
    `);
        const result = stmt.run(...values, returnId);
        return result.changes > 0;
    }
}
exports.db = new DatabaseManager();

import React, { useState, useEffect } from 'react';
import { ArrowLeft, Package, Clock, CheckCircle, XCircle } from 'lucide-react';
import { api } from '../services/api';
import styles from './ReturnsPage.module.css';

interface ReturnRecord {
  id: string;
  order_id: string;
  customer_email: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  created_at: string;
  items: any[];
}

export const ReturnsPage: React.FC = () => {
  const [returns, setReturns] = useState<ReturnRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReturn, setSelectedReturn] = useState<ReturnRecord | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'completed'>('all');

  useEffect(() => {
    loadReturns();
  }, []);

  const loadReturns = async () => {
    try {
      setLoading(true);
      const data = await api.getReturns();
      setReturns(data);
    } catch (error) {
      console.error('Error loading returns:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateReturnStatus = async (returnId: string, status: 'approved' | 'rejected') => {
    try {
      await api.updateReturnStatus(returnId, status);
      await loadReturns(); // Reload the data
      if (selectedReturn?.id === returnId) {
        setSelectedReturn(prev => prev ? { ...prev, status } : null);
      }
    } catch (error) {
      console.error('Error updating return status:', error);
    }
  };

  const filteredReturns = returns.filter(returnItem => {
    if (filter === 'all') return true;
    return returnItem.status === filter;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} />;
      case 'approved':
        return <CheckCircle size={16} />;
      case 'rejected':
        return <XCircle size={16} />;
      case 'completed':
        return <Package size={16} />;
      default:
        return <Clock size={16} />;
    }
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (selectedReturn) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <button
            onClick={() => setSelectedReturn(null)}
            className={styles.backButton}
          >
            <ArrowLeft size={16} />
            Back to Returns
          </button>

          <div className={styles.returnHeader}>
            <h1 className={styles.returnTitle}>
              Return #{selectedReturn.id.slice(-6)}
            </h1>
            <span className={`${styles.statusBadge} ${styles[selectedReturn.status]}`}>
              {getStatusIcon(selectedReturn.status)}
              {selectedReturn.status.toUpperCase()}
            </span>
          </div>
        </div>

        <div className={styles.detailsGrid}>
          {/* Return Items */}
          <div>
            <div className={styles.card}>
              <h3 className={styles.cardHeader}>
                <Package size={20} />
                Return Items
              </h3>

              <div className={styles.itemsList}>
                {(selectedReturn.items || []).map((item, index) => (
                  <div key={index} className={styles.itemCard}>
                    <div className={styles.itemImage}>
                      <Package size={32} />
                    </div>
                    <div className={styles.itemDetails}>
                      <h4 className={styles.itemTitle}>Line Item</h4>
                      <p className={styles.itemMeta}>Quantity: {item.quantity || 1}</p>
                      <p className={styles.itemMeta}>Reason: {item.reason || 'Changed Mind'}</p>
                      {item.condition && (
                        <p className={styles.itemMeta}>Condition: {item.condition}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Timeline & Actions */}
          <div>
            {/* Timeline */}
            <div className={styles.card} style={{ marginBottom: '1.5rem' }}>
              <h3 className={styles.cardHeader}>
                <Clock size={20} />
                Timeline
              </h3>

              <div className={styles.timeline}>
                <div className={styles.timelineItem}>
                  <div className={styles.timelineIcon}>
                    <Package size={16} />
                  </div>
                  <div className={styles.timelineContent}>
                    <p className={styles.timelineTitle}>Return Created</p>
                    <p className={styles.timelineDate}>{formatDate(selectedReturn.created_at)}</p>
                  </div>
                </div>

                {selectedReturn.status !== 'pending' && (
                  <div className={styles.timelineItem}>
                    <div className={styles.timelineIcon}>
                      {getStatusIcon(selectedReturn.status)}
                    </div>
                    <div className={styles.timelineContent}>
                      <p className={styles.timelineTitle}>Status Updated</p>
                      <p className={styles.timelineDate}>{formatDate(selectedReturn.created_at)}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div className={styles.card} style={{ marginBottom: '1.5rem' }}>
              <h3 className={styles.cardHeader}>Customer Information</h3>

              <div className={styles.customerInfo}>
                <div className={styles.infoItem}>
                  <p className={styles.infoLabel}>Email</p>
                  <p className={styles.infoValue}>{selectedReturn.customer_email}</p>
                </div>
                <div className={styles.infoItem}>
                  <p className={styles.infoLabel}>Order ID</p>
                  <p className={styles.infoValue}>{selectedReturn.order_id}</p>
                </div>
              </div>
            </div>

            {/* Actions */}
            {selectedReturn.status === 'pending' && (
              <div className={styles.card}>
                <h3 className={styles.cardHeader}>Actions</h3>

                <div className={styles.actions}>
                  <button
                    onClick={() => updateReturnStatus(selectedReturn.id, 'approved')}
                    className={`${styles.actionButton} ${styles.approveButton}`}
                  >
                    <CheckCircle size={16} />
                    Approve Return
                  </button>
                  <button
                    onClick={() => updateReturnStatus(selectedReturn.id, 'rejected')}
                    className={`${styles.actionButton} ${styles.rejectButton}`}
                  >
                    <XCircle size={16} />
                    Reject Return
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Returns Management</h1>
        <p>Manage and process return requests from customers.</p>
      </div>

      {/* Filter Tabs */}
      <div className={styles.filterTabs}>
        <div className={styles.tabsContainer}>
          <nav className={styles.tabsList}>
            {[
              { key: 'all', label: 'All', count: returns.length },
              { key: 'pending', label: 'Pending', count: returns.filter(r => r.status === 'pending').length },
              { key: 'approved', label: 'Approved', count: returns.filter(r => r.status === 'approved').length },
              { key: 'completed', label: 'Completed', count: returns.filter(r => r.status === 'completed').length }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`${styles.tab} ${filter === tab.key ? styles.active : ''}`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Returns List */}
      {loading ? (
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p className={styles.loadingText}>Loading returns...</p>
        </div>
      ) : filteredReturns.length === 0 ? (
        <div className={styles.emptyState}>
          <Package size={48} className={styles.emptyIcon} />
          <h3 className={styles.emptyTitle}>No returns found</h3>
          <p className={styles.emptyDescription}>No returns match the selected filter.</p>
        </div>
      ) : (
        <div className={styles.table}>
          <div className={styles.tableContainer}>
            <table className={styles.tableElement}>
              <thead className={styles.tableHeader}>
                <tr>
                  <th className={styles.tableHeaderCell}>
                    Return ID
                  </th>
                  <th className={styles.tableHeaderCell}>
                    Customer
                  </th>
                  <th className={styles.tableHeaderCell}>
                    Items
                  </th>
                  <th className={styles.tableHeaderCell}>
                    Status
                  </th>
                  <th className={styles.tableHeaderCell}>
                    Created
                  </th>
                  <th className={styles.tableHeaderCell}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredReturns.map((returnItem) => (
                  <tr key={returnItem.id} className={styles.tableRow}>
                    <td className={styles.tableCell}>
                      <div className={styles.cellContent}>
                        <div className={styles.primaryText}>
                          #{returnItem.id.slice(-6)}
                        </div>
                        <div className={styles.secondaryText}>
                          Order: {returnItem.order_id?.replace('gid://shopify/Order/', '') || 'N/A'}
                        </div>
                      </div>
                    </td>
                    <td className={styles.tableCell}>
                      <div className={styles.primaryText}>{returnItem.customer_email}</div>
                    </td>
                    <td className={styles.tableCell}>
                      <div className={styles.primaryText}>{returnItem.items?.length || 1} item{(returnItem.items?.length || 1) !== 1 ? 's' : ''}</div>
                    </td>
                    <td className={styles.tableCell}>
                      <span className={`${styles.statusBadge} ${styles[returnItem.status]}`}>
                        {getStatusIcon(returnItem.status)}
                        {returnItem.status.toUpperCase()}
                      </span>
                    </td>
                    <td className={styles.tableCell}>
                      <div className={styles.secondaryText}>{formatDate(returnItem.created_at)}</div>
                    </td>
                    <td className={styles.tableCell}>
                      <button
                        onClick={() => setSelectedReturn(returnItem)}
                        className={styles.viewButton}
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

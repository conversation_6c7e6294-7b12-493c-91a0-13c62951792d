{"version": 3, "file": "version-compatible.js", "sources": ["../../../../../../lib/utils/version-compatible.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\nimport {ApiVersion} from '../types';\n\nexport function versionCompatible(config: ConfigInterface) {\n  return (\n    referenceVersion: ApiVersion,\n    currentVersion: ApiVersion = config.apiVersion,\n  ): boolean => {\n    // Return true if not using a dated version\n    if (currentVersion === ApiVersion.Unstable) {\n      return true;\n    }\n    const numericVersion = (version: string) =>\n      parseInt(version.replace('-', ''), 10);\n    const current = numericVersion(currentVersion);\n    const reference = numericVersion(referenceVersion);\n    return current >= reference;\n  };\n}\n\nexport function versionPriorTo(config: ConfigInterface) {\n  return (\n    referenceVersion: ApiVersion,\n    currentVersion: ApiVersion = config.apiVersion,\n  ): boolean => {\n    return !versionCompatible(config)(referenceVersion, currentVersion);\n  };\n}\n"], "names": ["ApiVersion"], "mappings": ";;;;AAGM,SAAU,iBAAiB,CAAC,MAAuB,EAAA;IACvD,OAAO,CACL,gBAA4B,EAC5B,cAAA,GAA6B,MAAM,CAAC,UAAU,KACnC;;AAEX,QAAA,IAAI,cAAc,KAAKA,gBAAU,CAAC,QAAQ,EAAE;AAC1C,YAAA,OAAO,IAAI;QACb;QACA,MAAM,cAAc,GAAG,CAAC,OAAe,KACrC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AACxC,QAAA,MAAM,OAAO,GAAG,cAAc,CAAC,cAAc,CAAC;AAC9C,QAAA,MAAM,SAAS,GAAG,cAAc,CAAC,gBAAgB,CAAC;QAClD,OAAO,OAAO,IAAI,SAAS;AAC7B,IAAA,CAAC;AACH;AAEM,SAAU,cAAc,CAAC,MAAuB,EAAA;IACpD,OAAO,CACL,gBAA4B,EAC5B,cAAA,GAA6B,MAAM,CAAC,UAAU,KACnC;QACX,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,cAAc,CAAC;AACrE,IAAA,CAAC;AACH;;;;;"}
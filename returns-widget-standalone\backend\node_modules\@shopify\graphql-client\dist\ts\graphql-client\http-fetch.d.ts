import { CustomFetchApi, GraphQLClient, Logger } from './types';
interface GenerateHttpFetchOptions {
    clientLogger: Logger;
    customFetchApi?: CustomFetchApi;
    client?: string;
    defaultRetryWaitTime?: number;
    retriableCodes?: number[];
}
export declare function generateHttpFetch({ clientLogger, customFetchApi, client, defaultRetryWaitTime, retriableCodes, }: GenerateHttpFetchOptions): (requestParams: Parameters<CustomFetchApi>, count: number, maxRetries: number) => ReturnType<GraphQLClient["fetch"]>;
export {};
//# sourceMappingURL=http-fetch.d.ts.map
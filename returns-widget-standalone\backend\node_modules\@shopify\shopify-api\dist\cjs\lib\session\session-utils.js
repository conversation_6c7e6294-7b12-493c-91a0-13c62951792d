'use strict';

var types = require('../auth/oauth/types.js');
var index = require('../../runtime/http/index.js');
var shopValidator = require('../utils/shop-validator.js');
var index$1 = require('../logger/index.js');
var error = require('../error.js');
var decodeSessionToken = require('./decode-session-token.js');
var session = require('./session.js');
var cookies = require('../../runtime/http/cookies.js');

function getJwtSessionId(config) {
    return (shop, userId) => {
        return `${shopValidator.sanitizeShop(config)(shop, true)}_${userId}`;
    };
}
function getOfflineId(config) {
    return (shop) => {
        return `offline_${shopValidator.sanitizeShop(config)(shop, true)}`;
    };
}
function getCurrentSessionId(config) {
    return async function getCurrentSessionId({ isOnline, ...adapterArgs }) {
        const request = await index.abstractConvertRequest(adapterArgs);
        const log = index$1.logger(config);
        if (config.isEmbeddedApp) {
            log.debug('App is embedded, looking for session id in JWT payload', {
                isOnline,
            });
            const authHeader = request.headers.Authorization;
            if (authHeader) {
                const matches = (typeof authHeader === 'string' ? authHeader : authHeader[0]).match(/^Bearer (.+)$/);
                if (!matches) {
                    log.error('Missing Bearer token in authorization header', { isOnline });
                    throw new error.MissingJwtTokenError('Missing Bearer token in authorization header');
                }
                const jwtPayload = await decodeSessionToken.decodeSessionToken(config)(matches[1]);
                const shop = jwtPayload.dest.replace(/^https:\/\//, '');
                log.debug('Found valid JWT payload', { shop, isOnline });
                if (isOnline) {
                    return getJwtSessionId(config)(shop, jwtPayload.sub);
                }
                else {
                    return getOfflineId(config)(shop);
                }
            }
            else {
                log.error('Missing Authorization header, was the request made with authenticatedFetch?', { isOnline });
            }
        }
        else {
            log.debug('App is not embedded, looking for session id in cookies', {
                isOnline,
            });
            const cookies$1 = new cookies.Cookies(request, {}, {
                keys: [config.apiSecretKey],
            });
            return cookies$1.getAndVerify(types.SESSION_COOKIE_NAME);
        }
        return undefined;
    };
}
function customAppSession(config) {
    return (shop) => {
        return new session.Session({
            id: '',
            shop: `${shopValidator.sanitizeShop(config)(shop, true)}`,
            state: '',
            isOnline: false,
        });
    };
}

exports.customAppSession = customAppSession;
exports.getCurrentSessionId = getCurrentSessionId;
exports.getJwtSessionId = getJwtSessionId;
exports.getOfflineId = getOfflineId;
//# sourceMappingURL=session-utils.js.map

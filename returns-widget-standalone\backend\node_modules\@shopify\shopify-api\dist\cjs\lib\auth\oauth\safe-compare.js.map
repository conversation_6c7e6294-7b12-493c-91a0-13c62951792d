{"version": 3, "file": "safe-compare.js", "sources": ["../../../../../../../lib/auth/oauth/safe-compare.ts"], "sourcesContent": ["import * as ShopifyErrors from '../../error';\n\nexport type SafeCompare = (\n  strA: string | Record<string, string> | string[] | number[],\n  strB: string | Record<string, string> | string[] | number[],\n) => boolean;\n\nexport const safeCompare: SafeCompare = (strA, strB) => {\n  if (typeof strA === typeof strB) {\n    const enc = new TextEncoder();\n    const buffA = enc.encode(JSON.stringify(strA));\n    const buffB = enc.encode(JSON.stringify(strB));\n\n    if (buffA.length === buffB.length) {\n      return timingSafeEqual(buffA, buffB);\n    }\n  } else {\n    throw new ShopifyErrors.SafeCompareError(\n      `Mismatched data types provided: ${typeof strA} and ${typeof strB}`,\n    );\n  }\n  return false;\n};\n\n// Buffer must be same length for this function to be secure.\nfunction timingSafeEqual(bufA: ArrayBuffer, bufB: ArrayBuffer): boolean {\n  const viewA = new Uint8Array(bufA);\n  const viewB = new Uint8Array(bufB);\n  let out = 0;\n  for (let i = 0; i < viewA.length; i++) {\n    out |= viewA[i] ^ viewB[i];\n  }\n  return out === 0;\n}\n"], "names": ["ShopifyErrors.SafeCompareError"], "mappings": ";;;;MAOa,WAAW,GAAgB,CAAC,IAAI,EAAE,IAAI,KAAI;AACrD,IAAA,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE;AAC/B,QAAA,MAAM,GAAG,GAAG,IAAI,WAAW,EAAE;AAC7B,QAAA,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9C,QAAA,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;AACjC,YAAA,OAAO,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC;QACtC;IACF;SAAO;AACL,QAAA,MAAM,IAAIA,sBAA8B,CACtC,CAAA,gCAAA,EAAmC,OAAO,IAAI,CAAA,KAAA,EAAQ,OAAO,IAAI,CAAA,CAAE,CACpE;IACH;AACA,IAAA,OAAO,KAAK;AACd;AAEA;AACA,SAAS,eAAe,CAAC,IAAiB,EAAE,IAAiB,EAAA;AAC3D,IAAA,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC;AAClC,IAAA,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC;IAClC,IAAI,GAAG,GAAG,CAAC;AACX,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IAC5B;IACA,OAAO,GAAG,KAAK,CAAC;AAClB;;;;"}
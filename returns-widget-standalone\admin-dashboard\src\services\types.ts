// Types for the Admin Dashboard
export interface AuthUser {
  id: string
  email: string
  storeName: string
  shopifyDomain: string
  createdAt: string
}

export interface RegisterData {
  email: string
  password: string
  storeName: string
  shopifyDomain: string
}

export interface LoginData {
  email: string
  password: string
}

export interface MerchantSettings {
  widgetBrandingColor?: string
  returnPolicy?: string
  returnWindow?: number
  requirePhotos?: boolean
  autoApprove?: boolean
}

export interface ReturnRecord {
  id: string
  merchant_id: string
  shopify_order_id: string
  customer_email: string
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  return_items: ReturnItem[]
  created_at: string
  updated_at: string
  tracking_number?: string
  label_url?: string
}

export interface ReturnItem {
  lineItemId: string
  quantity: number
  reason: string
  condition: string
  customerNote?: string
}

export interface ShopifyCredentials {
  accessToken: string
}

export interface SendcloudCredentials {
  publicKey: string
  secretKey: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

export interface DashboardMetrics {
  totalReturns: number
  pendingReturns: number
  approvedReturns: number
  rejectedReturns: number
  recentReturns: ReturnRecord[]
}

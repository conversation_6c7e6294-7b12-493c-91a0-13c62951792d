{"version": 3, "file": "usage_charge.js", "sources": ["../../../../../../../rest/admin/2022-10/usage_charge.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\nimport {Currency} from './currency';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  recurring_application_charge_id?: number | string | null;\n  fields?: unknown;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  recurring_application_charge_id?: number | string | null;\n  fields?: unknown;\n}\n\nexport class UsageCharge extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {\n    \"currency\": Currency\n  };\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"recurring_application_charge_id\"], \"path\": \"recurring_application_charges/<recurring_application_charge_id>/usage_charges.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"recurring_application_charge_id\", \"id\"], \"path\": \"recurring_application_charges/<recurring_application_charge_id>/usage_charges/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [\"recurring_application_charge_id\"], \"path\": \"recurring_application_charges/<recurring_application_charge_id>/usage_charges.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"usage_charge\",\n      \"plural\": \"usage_charges\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      recurring_application_charge_id = null,\n      fields = null\n    }: FindArgs\n  ): Promise<UsageCharge | null> {\n    const result = await this.baseFind<UsageCharge>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id, \"recurring_application_charge_id\": recurring_application_charge_id},\n      params: {\"fields\": fields},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async all(\n    {\n      session,\n      recurring_application_charge_id = null,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<UsageCharge>> {\n    const response = await this.baseFind<UsageCharge>({\n      session: session,\n      urlIds: {\"recurring_application_charge_id\": recurring_application_charge_id},\n      params: {\"fields\": fields, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public created_at: string | null;\n  public currency: Currency | null | {[key: string]: any};\n  public description: string | null;\n  public id: number | null;\n  public price: string | null;\n  public recurring_application_charge_id: number | null;\n  public updated_at: string | null;\n}\n"], "names": ["Base", "ApiVersion", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAAA;;AAEwH;AAsBlH,MAAO,WAAY,SAAQA,SAAI,CAAA;AAC5B,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;IAErC,OAAO,MAAM,GAAiC;AACtD,QAAA,UAAU,EAAEC;KACb;AACS,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,iCAAiC,CAAC,EAAE,MAAM,EAAE,oFAAoF,EAAC;AACpL,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,iCAAiC,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,yFAAyF,EAAC;AAC/L,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,iCAAiC,CAAC,EAAE,MAAM,EAAE,oFAAoF;KACtL;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,cAAc;AAC1B,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,+BAA+B,GAAG,IAAI,EACtC,MAAM,GAAG,IAAI,EACJ,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAc;AAC9C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAE,iCAAiC,EAAE,+BAA+B,EAAC;AACtF,YAAA,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAC;AAC3B,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,+BAA+B,GAAG,IAAI,EACtC,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAc;AAChD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,iCAAiC,EAAE,+BAA+B,EAAC;YAC5E,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AACzC,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,UAAU;AACV,IAAA,QAAQ;AACR,IAAA,WAAW;AACX,IAAA,EAAE;AACF,IAAA,KAAK;AACL,IAAA,+BAA+B;AAC/B,IAAA,UAAU;;;;;"}
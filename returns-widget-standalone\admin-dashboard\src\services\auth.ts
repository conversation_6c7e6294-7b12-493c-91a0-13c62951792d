import type { 
  AuthUser, 
  RegisterData, 
  LoginData, 
  ApiResponse 
} from './types'

const API_BASE = import.meta.env.VITE_API_URL || 'http://localhost:3001/api'

export const authService = {
  async register(data: RegisterData): Promise<AuthUser> {
    const response = await fetch(`${API_BASE}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    })
    
    const result: ApiResponse<{ merchant: AuthUser; token: string }> = await response.json()
    
    if (!response.ok || !result.success) {
      throw new Error(result.error || 'Registration failed')
    }
    
    // Store the JWT token
    localStorage.setItem('token', result.data!.token)
    
    return result.data!.merchant
  },

  async login(data: LoginData): Promise<AuthUser> {
    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    })
    
    const result: ApiResponse<{ merchant: AuthUser; token: string }> = await response.json()
    
    if (!response.ok || !result.success) {
      throw new Error(result.error || 'Login failed')
    }
    
    // Store the JWT token
    localStorage.setItem('token', result.data!.token)
    
    return result.data!.merchant
  },

  logout() {
    localStorage.removeItem('token')
  },

  getToken(): string | null {
    return localStorage.getItem('token')
  },

  isAuthenticated(): boolean {
    return !!this.getToken()
  }
}

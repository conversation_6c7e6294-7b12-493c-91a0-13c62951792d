import { getCurrentSupportedApiVersions, validateDomainAndGetStoreUrl, validateApiVersion, createGraphQLClient, generateGetHeaders, generateGetGQLClientParams } from '@shopify/graphql-client';
import { CLIENT, DEFAULT_CLIENT_VERSION, DEFAULT_CONTENT_TYPE, ACCESS_TOKEN_HEADER } from '../constants.mjs';
import { validateServerSideUsage, validateRequiredAccessToken } from '../validations.mjs';

function createAdminApiClient({ storeDomain, apiVersion, accessToken, userAgentPrefix, retries = 0, customFetchApi, logger, isTesting, }) {
    const currentSupportedApiVersions = getCurrentSupportedApiVersions();
    const storeUrl = validateDomainAndGetStoreUrl({
        client: CLIENT,
        storeDomain,
    });
    const baseApiVersionValidationParams = {
        client: CLIENT,
        currentSupportedApiVersions,
        logger,
    };
    validateServerSideUsage(isTesting);
    validateApiVersion({
        client: CLIENT,
        currentSupportedApiVersions,
        apiVersion,
        logger,
    });
    validateRequiredAccessToken(accessToken);
    const apiUrlFormatter = generateApiUrlFormatter(storeUrl, apiVersion, baseApiVersionValidationParams);
    const config = {
        storeDomain: storeUrl,
        apiVersion,
        accessToken,
        headers: {
            'Content-Type': DEFAULT_CONTENT_TYPE,
            Accept: DEFAULT_CONTENT_TYPE,
            [ACCESS_TOKEN_HEADER]: accessToken,
            'User-Agent': `${userAgentPrefix ? `${userAgentPrefix} | ` : ''}${CLIENT} v${DEFAULT_CLIENT_VERSION}`,
        },
        apiUrl: apiUrlFormatter(),
        userAgentPrefix,
    };
    const graphqlClient = createGraphQLClient({
        headers: config.headers,
        url: config.apiUrl,
        retries,
        customFetchApi,
        logger,
    });
    const getHeaders = generateGetHeaders(config);
    const getApiUrl = generateGetApiUrl(config, apiUrlFormatter);
    const getGQLClientParams = generateGetGQLClientParams({
        getHeaders,
        getApiUrl,
    });
    const client = {
        config,
        getHeaders,
        getApiUrl,
        fetch: (...props) => {
            return graphqlClient.fetch(...getGQLClientParams(...props));
        },
        request: (...props) => {
            return graphqlClient.request(...getGQLClientParams(...props));
        },
    };
    return Object.freeze(client);
}
function generateApiUrlFormatter(storeUrl, defaultApiVersion, baseApiVersionValidationParams) {
    return (apiVersion) => {
        if (apiVersion) {
            validateApiVersion({
                ...baseApiVersionValidationParams,
                apiVersion,
            });
        }
        const urlApiVersion = (apiVersion ?? defaultApiVersion).trim();
        return `${storeUrl}/admin/api/${urlApiVersion}/graphql.json`;
    };
}
function generateGetApiUrl(config, apiUrlFormatter) {
    return (propApiVersion) => {
        return propApiVersion ? apiUrlFormatter(propApiVersion) : config.apiUrl;
    };
}

export { createAdminApiClient };
//# sourceMappingURL=client.mjs.map

{"version": 3, "file": "types.js", "sources": ["../../../../../../lib/billing/types.ts"], "sourcesContent": ["import {\n  BillingInterval,\n  BillingReplacementBehavior,\n  RecurringBillingIntervals,\n} from '../types';\nimport {Session} from '../session/session';\nimport {\n  FeatureEnabled,\n  FutureFlagOptions,\n  FutureFlags,\n} from '../../future/flags';\n\nexport interface BillingConfigPlan {\n  /**\n   * Amount to charge for this plan.\n   */\n  amount: number;\n  /**\n   * Currency code for this plan.\n   */\n  currencyCode: string;\n}\n\nexport interface BillingConfigOneTimePlan extends BillingConfigPlan {\n  /**\n   * Interval for this plan.\n   *\n   * Must be set to `OneTime`.\n   */\n  interval: BillingInterval.OneTime;\n}\n\nexport interface BillingConfigSubscriptionPlan extends BillingConfigPlan {\n  /**\n   * Recurring interval for this plan.\n   *\n   * Must be either `Every30Days` or `Annual`.\n   */\n  interval: Exclude<RecurringBillingIntervals, BillingInterval.Usage>;\n  /**\n   * How many trial days to give before charging for this plan.\n   */\n  trialDays?: number;\n  /**\n   * The behavior to use when replacing an existing subscription with a new one.\n   */\n  replacementBehavior?: BillingReplacementBehavior;\n  /**\n   * The discount to apply to this plan.\n   */\n  discount?: BillingConfigSubscriptionPlanDiscount;\n}\n\nexport interface BillingConfigSubscriptionPlanDiscountAmount {\n  /**\n   * The amount to discount.\n   *\n   * Cannot be set if `percentage` is set.\n   */\n  amount: number;\n  /**\n   * The percentage to discount.\n   *\n   * Cannot be set if `amount` is set.\n   */\n  percentage?: never;\n}\n\nexport interface BillingConfigSubscriptionPlanDiscountPercentage {\n  /**\n   * The amount to discount.\n   *\n   * Cannot be set if `percentage` is set.\n   */\n  amount?: never;\n  /**\n   * The percentage to discount.\n   *\n   * Cannot be set if `amount` is set.\n   */\n  percentage: number;\n}\n\nexport interface BillingConfigSubscriptionPlanDiscount {\n  /**\n   * The number of intervals to apply the discount for.\n   */\n  durationLimitInIntervals?: number;\n  /**\n   * The discount to apply.\n   */\n  value:\n    | BillingConfigSubscriptionPlanDiscountAmount\n    | BillingConfigSubscriptionPlanDiscountPercentage;\n}\n\nexport interface BillingConfigUsagePlan extends BillingConfigPlan {\n  /**\n   * Interval for this plan.\n   *\n   * Must be set to `Usage`.\n   */\n  interval: BillingInterval.Usage;\n  /**\n   * Usage terms for this plan.\n   */\n  usageTerms: string;\n  /**\n   * How many trial days to give before charging for this plan.\n   */\n  trialDays?: number;\n  /**\n   * The behavior to use when replacing an existing subscription with a new one.\n   */\n  replacementBehavior?: BillingReplacementBehavior;\n}\n\nexport type BillingConfigLegacyItem =\n  | BillingConfigOneTimePlan\n  | BillingConfigSubscriptionPlan\n  | BillingConfigUsagePlan;\n\nexport type BillingConfigItem<\n  Future extends FutureFlagOptions = FutureFlagOptions,\n> =\n  FeatureEnabled<Future, 'lineItemBilling'> extends true\n    ? BillingConfigOneTimePlan | BillingConfigSubscriptionLineItemPlan\n    : BillingConfigLegacyItem;\n\n// Type this as an interface to improve TSDoc support for it.\n\n/**\n * Billing configuration options, indexed by an app-specific plan name.\n */\nexport interface BillingConfig<\n  Future extends FutureFlagOptions = FutureFlagOptions,\n> {\n  /**\n   * An individual billing plan.\n   */\n  [plan: string]: BillingConfigItem<\n    Future & {\n      lineItemBilling: Future extends FutureFlags\n        ? Future['lineItemBilling'] extends true\n          ? true\n          : Future['lineItemBilling'] extends false\n            ? false\n            : Future['v10_lineItemBilling'] extends true\n              ? true\n              : false\n        : false;\n    }\n  >;\n}\n\nexport type RequestConfigOverrides =\n  | Partial<BillingConfigOneTimePlan>\n  | Partial<BillingConfigSubscriptionPlan>\n  | Partial<BillingConfigUsagePlan>;\n\nexport interface BillingConfigLineItem {\n  /**\n   * The amount to charge for this line item.\n   */\n  amount: number;\n  /**\n   * The currency code for this line item.\n   */\n  currencyCode: string;\n}\n\nexport interface BillingConfigRecurringLineItem extends BillingConfigLineItem {\n  /**\n   * The recurring interval for this line item.\n   *\n   * Must be either `Every30Days` or `Annual`.\n   */\n  interval: BillingInterval.Every30Days | BillingInterval.Annual;\n  /**\n   * An optional discount to apply for this line item.\n   */\n  discount?: BillingConfigSubscriptionPlanDiscount;\n}\n\nexport interface BillingConfigUsageLineItem extends BillingConfigLineItem {\n  /**\n   * The usage interval for this line item.\n   *\n   * Must be set to `Usage`.\n   */\n  interval: BillingInterval.Usage;\n  /**\n   * The capped amount or the maximum amount to be charged in the interval.\n   */\n  amount: number;\n  /**\n   * Usage terms for this line item.\n   */\n  terms: string;\n}\n\nexport interface BillingConfigSubscriptionLineItemPlan {\n  /**\n   * The replacement behavior to use for this plan.\n   */\n  replacementBehavior?: BillingReplacementBehavior;\n  /**\n   * How many trial days to give before charging for this plan.\n   */\n  trialDays?: number;\n  /**\n   * The line items for this plan.\n   */\n  lineItems: (BillingConfigRecurringLineItem | BillingConfigUsageLineItem)[];\n}\n\ntype DeepPartial<T> = T extends object\n  ? {[P in keyof T]?: DeepPartial<T[P]>}\n  : T;\nexport type RequestConfigLineItemOverrides =\n  DeepPartial<BillingConfigSubscriptionLineItemPlan>;\n\ninterface BillingCheckParamsNew {\n  /**\n   * The session to use for this check.\n   */\n  session: Session;\n  /**\n   * The plans to accept for this check.\n   */\n  plans?: string[] | string;\n  /**\n   * Whether to include charges that were created on test mode. Test shops and demo shops cannot be charged.\n   */\n  isTest?: boolean;\n}\n\ninterface BillingCheckParamsOld extends BillingCheckParamsNew {\n  /**\n   * The plans to accept for this check.\n   */\n  plans: string[] | string;\n  /**\n   * Whether to return the full response object.\n   */\n  returnObject?: boolean;\n}\n\nexport type BillingCheckParams<\n  Future extends FutureFlagOptions = FutureFlagOptions,\n> =\n  FeatureEnabled<Future, 'unstable_managedPricingSupport'> extends true\n    ? BillingCheckParamsNew\n    : BillingCheckParamsOld;\n\nexport interface BillingCheckResponseObject {\n  /**\n   * Whether the user has an active payment method.\n   */\n  hasActivePayment: boolean;\n  /**\n   * The one-time purchases the shop has.\n   */\n  oneTimePurchases: OneTimePurchase[];\n  /**\n   * The active subscriptions the shop has.\n   */\n  appSubscriptions: AppSubscription[];\n}\n\nexport type BillingCheckResponse<\n  Params extends BillingCheckParams<Future>,\n  Future extends FutureFlagOptions = FutureFlagOptions,\n> =\n  FeatureEnabled<Future, 'unstable_managedPricingSupport'> extends true\n    ? BillingCheckResponseObject\n    : Params extends BillingCheckParamsOld\n      ? Params['returnObject'] extends true\n        ? BillingCheckResponseObject\n        : boolean\n      : never;\n\ntype BillingRequestOverridesType<\n  Future extends FutureFlagOptions = FutureFlagOptions,\n> =\n  FeatureEnabled<Future, 'lineItemBilling'> extends true\n    ? RequestConfigOverrides & RequestConfigLineItemOverrides\n    : RequestConfigOverrides;\n\nexport type BillingRequestParams<\n  Future extends FutureFlagOptions = FutureFlagOptions,\n> = {\n  /**\n   * The session to use for this request.\n   */\n  session: Session;\n  /**\n   * The plan to request.\n   */\n  plan: string;\n  /**\n   * Whether this is a test purchase.\n   */\n  isTest?: boolean;\n  /**\n   * Override the return URL after the purchase is complete.\n   */\n  returnUrl?: string;\n  /**\n   * Whether to return the full response object.\n   */\n  returnObject?: boolean;\n} & BillingRequestOverridesType<Future>;\n\nexport interface BillingRequestResponseObject {\n  /**\n   * The confirmation URL for this request.\n   */\n  confirmationUrl: string;\n  /**\n   * The one-time purchase created by this request.\n   */\n  oneTimePurchase?: OneTimePurchase;\n  /**\n   * The app subscription created by this request.\n   */\n  appSubscription?: AppSubscription;\n}\n\nexport type BillingRequestResponse<Params extends BillingRequestParams> =\n  Params['returnObject'] extends true ? BillingRequestResponseObject : string;\n\nexport interface BillingCancelParams {\n  /**\n   * The session to use for this request.\n   */\n  session: Session;\n  /**\n   * The subscription ID to cancel.\n   */\n  subscriptionId: string;\n  /**\n   * Whether to prorate the cancellation.\n   */\n  prorate?: boolean;\n  /**\n   * Whether to consider test purchases.\n   */\n  isTest?: boolean;\n}\n\nexport interface BillingSubscriptionParams {\n  /**\n   * The session to use for this request.\n   */\n  session: Session;\n}\n\nexport interface AppSubscription {\n  /**\n   * The ID of the app subscription.\n   */\n  id: string;\n  /**\n   * The name of the purchased plan.\n   */\n  name: string;\n  /**\n   * Whether this is a test subscription.\n   */\n  test: boolean;\n  /**\n   * The number of trial days for this subscription.\n   */\n  trialDays: number;\n  /**\n   * The date and time when the subscription was created.\n   */\n  createdAt: string;\n  /**\n   * The date and time when the current period ends.\n   */\n  currentPeriodEnd: string;\n  /**\n   * The return URL for this subscription.\n   */\n  returnUrl: string;\n\n  /*\n   * The line items for this plan. This will become mandatory in v10.\n   */\n  lineItems?: ActiveSubscriptionLineItem[];\n\n  /*\n   * The status of the subscription. [ACTIVE, CANCELLED, PENDING, DECLINED, EXPIRED, FROZEN, ACCEPTED]\n   */\n  status:\n    | 'ACTIVE'\n    | 'CANCELLED'\n    | 'PENDING'\n    | 'DECLINED'\n    | 'EXPIRED'\n    | 'FROZEN'\n    | 'ACCEPTED';\n}\n\nexport interface ActiveSubscriptions {\n  activeSubscriptions: AppSubscription[];\n}\n\nexport interface ActiveSubscriptionLineItem {\n  /*\n   * The ID of the line item.\n   */\n  id: string;\n  /*\n   * The details of the plan.\n   */\n  plan: AppPlan;\n}\n\nexport interface RecurringAppPlan {\n  /*\n   * The interval for this plan is charged on.\n   */\n  interval: BillingInterval.Every30Days | BillingInterval.Annual;\n  /*\n   * The price of the plan.\n   */\n  price: Money;\n  /*\n   * The discount applied to the plan.\n   */\n  discount: AppPlanDiscount;\n}\n\nexport interface UsageAppPlan {\n  /*\n   * The total usage records for interval.\n   */\n  balanceUsed: Money;\n  /*\n   * The capped amount prevents the merchant from being charged for any usage over that amount during a billing period.\n   */\n  cappedAmount: Money;\n  /*\n   * The terms and conditions for app usage pricing.\n   */\n  terms: string;\n}\n\nexport interface Money {\n  amount: number;\n  currencyCode: string;\n}\n\nexport interface AppPlanDiscount {\n  /*\n   * The total number of intervals the discount applies to.\n   */\n  durationLimitInIntervals: number;\n  /*\n   * The remaining number of intervals the discount applies to.\n   */\n  remainingDurationInIntervals: number;\n  /*\n   * The price after the discount is applied.\n   */\n  priceAfterDiscount: Money;\n  /*\n   * The value of the discount applied every billing interval.\n   */\n  value: AppPlanDiscountAmount;\n}\n\nexport interface AppSubscriptionLineItemUpdatePayload {\n  userErrors: string[];\n  confirmationUrl: string;\n  appSubscription: AppSubscription;\n}\n\nexport type UpdateCappedAmountConfirmation = Omit<\n  AppSubscriptionLineItemUpdatePayload,\n  'userErrors'\n>;\n\ntype AppPlanDiscountAmount =\n  | BillingConfigSubscriptionPlanDiscountAmount\n  | BillingConfigSubscriptionPlanDiscountPercentage;\n\nexport interface AppPlan {\n  /*\n   * The pricing details of the plan.\n   */\n  pricingDetails: RecurringAppPlan | UsageAppPlan;\n}\nexport interface OneTimePurchase {\n  /**\n   * The ID of the one-time purchase.\n   */\n  id: string;\n  /**\n   * The name of the purchased plan.\n   */\n  name: string;\n  /**\n   * Whether this is a test purchase.\n   */\n  test: boolean;\n  /*\n   * The status of the subscription. [ACTIVE, CANCELLED, PENDING, DECLINED, EXPIRED, FROZEN, ACCEPTED]\n   */\n  status:\n    | 'ACTIVE'\n    | 'CANCELLED'\n    | 'PENDING'\n    | 'DECLINED'\n    | 'EXPIRED'\n    | 'FROZEN'\n    | 'ACCEPTED';\n}\n\nexport interface BillingCreateUsageRecordParams {\n  /**\n   * The session to use for this request.\n   */\n  session: Session;\n  /**\n   * The description of the usage record.\n   */\n  description: string;\n  /**\n   * The price and currency of the usage record.\n   */\n  price: {\n    /**\n     * The amount to charge for this usage record.\n     */\n    amount: number;\n    /**\n     * The currency code for this usage record.\n     */\n    currencyCode: string;\n  };\n  /**\n   * The subscription line item ID to associate the usage record with.\n   */\n  subscriptionLineItemId?: string;\n  /**\n   * The idempotency key for this request.\n   */\n  idempotencyKey?: string;\n  /**\n   * Whether this is a test charge.\n   * */\n  isTest?: boolean;\n}\n\nexport interface UsageRecord {\n  /**\n   * The ID of the usage record.\n   */\n  id: string;\n  /**\n   * The description of the usage record.\n   */\n  description: string;\n  /**\n   * The price and currency of the usage record.\n   */\n  price: {\n    /**\n     * The amount to charge for this usage record.\n     */\n    amount: number;\n    /**\n     * The currency code for this usage record.\n     */\n    currencyCode: string;\n  };\n  /**\n   * The subscription line item associated with the usage record.\n   */\n  plan: ActiveSubscriptionLineItem;\n  /**\n   * The idempotency key for this request.\n   */\n  idempotencyKey?: string;\n  /**\n   * The subscription line item associated with the usage record.\n   */\n  subscriptionLineItem: AppSubscriptionLineItem;\n}\n\nexport interface AppSubscriptionLineItem {\n  /**\n   * The ID of the subscription line item.\n   */\n  id: string;\n  /**\n   * The plan associated with the subscription line item.\n   */\n  plan: AppPlan;\n}\n\nexport interface BillingUpdateUsageCappedAmountParams {\n  /**\n   * The session to use for this request.\n   */\n  session: Session;\n  /**\n   * The subscription line item ID to update.\n   */\n  subscriptionLineItemId: string;\n  /**\n   * The maximum charge for the usage billing plan.\n   */\n  cappedAmount: {\n    /**\n     * The amount to update.\n     */\n    amount: number;\n    /**\n     * The currency code to update.\n     */\n    currencyCode: string;\n  };\n}\n\ninterface OneTimePurchases {\n  oneTimePurchases: {\n    edges: {\n      node: OneTimePurchase;\n    }[];\n    pageInfo: {\n      endCursor: string;\n      hasNextPage: boolean;\n    };\n  };\n}\n\nexport type CurrentAppInstallation = OneTimePurchases & ActiveSubscriptions;\n\nexport interface CurrentAppInstallations {\n  currentAppInstallation?: CurrentAppInstallation;\n}\n\nexport interface RequestResponse {\n  userErrors: string[];\n  confirmationUrl: string;\n}\n\ninterface AppSubscriptionCreate {\n  userErrors: string[];\n  confirmationUrl: string;\n  appSubscription: AppSubscription;\n}\n\ninterface AppPurchaseOneTimeCreate {\n  userErrors: string[];\n  confirmationUrl: string;\n  oneTimePurchase: OneTimePurchase;\n}\n\nexport interface RecurringPaymentResponse {\n  appSubscriptionCreate?: AppSubscriptionCreate;\n}\n\nexport interface SinglePaymentResponse {\n  appPurchaseOneTimeCreate?: AppPurchaseOneTimeCreate;\n}\n\nexport type RequestResponseData =\n  | AppSubscriptionCreate\n  | AppPurchaseOneTimeCreate;\n\nexport interface SubscriptionResponse {\n  currentAppInstallation?: ActiveSubscriptions;\n}\n\nexport interface CancelResponse {\n  appSubscriptionCancel?: {\n    appSubscription: AppSubscription;\n    userErrors: string[];\n  };\n}\n\nexport interface UsageRecordCreateResponse {\n  appUsageRecordCreate?: {\n    appUsageRecord: UsageRecord;\n    userErrors: string[];\n  };\n}\n\nexport interface BillingUpdateUsageCappedAmountResponse {\n  appSubscriptionLineItemUpdate?: AppSubscriptionLineItemUpdatePayload;\n}\n\nexport type BillingCheck<Future extends FutureFlagOptions> = <\n  Params extends BillingCheckParams<Future>,\n>(\n  params: Params,\n) => Promise<BillingCheckResponse<Params, Future>>;\n\nexport type BillingRequest = <Params extends BillingRequestParams>(\n  params: Params,\n) => Promise<BillingRequestResponse<Params>>;\n\nexport type BillingCancel = (\n  params: BillingCancelParams,\n) => Promise<AppSubscription>;\n\nexport type BillingSubscriptions = (\n  params: BillingSubscriptionParams,\n) => Promise<ActiveSubscriptions>;\n\nexport type BillingCreateUsageRecord = (\n  params: BillingCreateUsageRecordParams,\n) => Promise<UsageRecord>;\n\nexport type BillingUpdateUsageCappedAmount = (\n  params: BillingUpdateUsageCappedAmountParams,\n) => Promise<UpdateCappedAmountConfirmation>;\n\nexport interface ShopifyBilling<Future extends FutureFlagOptions> {\n  check: BillingCheck<Future>;\n  request: BillingRequest;\n  cancel: BillingCancel;\n  subscriptions: BillingSubscriptions;\n  createUsageRecord: BillingCreateUsageRecord;\n  updateUsageCappedAmount: BillingUpdateUsageCappedAmount;\n}\n\nexport const APP_SUBSCRIPTION_FRAGMENT = `\n  fragment AppSubscriptionFragment on AppSubscription {\n    id\n    name\n    test\n    status\n    trialDays\n    createdAt\n    currentPeriodEnd\n    returnUrl\n    lineItems {\n      id\n      plan {\n        pricingDetails {\n          ... on AppRecurringPricing {\n            price {\n              amount\n              currencyCode\n            }\n            interval\n            discount {\n              durationLimitInIntervals\n              remainingDurationInIntervals\n              priceAfterDiscount {\n                amount\n              }\n              value {\n                ... on AppSubscriptionDiscountAmount {\n                  amount {\n                    amount\n                    currencyCode\n                  }\n                }\n                ... on AppSubscriptionDiscountPercentage {\n                  percentage\n                }\n              }\n            }\n          }\n          ... on AppUsagePricing {\n            balanceUsed {\n              amount\n              currencyCode\n            }\n            cappedAmount {\n              amount\n              currencyCode\n            }\n            terms\n          }\n        }\n      }\n    }\n  }\n`;\n"], "names": [], "mappings": ";;AA6tBO,MAAM,yBAAyB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateId = generateId;
exports.sanitizeEmail = sanitizeEmail;
exports.validateEmail = validateEmail;
exports.generateReturnNumber = generateReturnNumber;
const crypto_1 = require("crypto");
function generateId() {
    return (0, crypto_1.randomBytes)(16).toString('hex');
}
function sanitizeEmail(email) {
    return email.toLowerCase().trim();
}
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function generateReturnNumber() {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `RET${timestamp.slice(-6)}${random}`;
}

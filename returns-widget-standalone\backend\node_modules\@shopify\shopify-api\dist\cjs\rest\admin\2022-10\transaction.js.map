{"version": 3, "file": "transaction.js", "sources": ["../../../../../../../rest/admin/2022-10/transaction.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  order_id?: number | string | null;\n  fields?: unknown;\n  in_shop_currency?: unknown;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  order_id?: number | string | null;\n  since_id?: unknown;\n  fields?: unknown;\n  in_shop_currency?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n  order_id?: number | string | null;\n}\n\nexport class Transaction extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [\"order_id\"], \"path\": \"orders/<order_id>/transactions/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"order_id\"], \"path\": \"orders/<order_id>/transactions.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"order_id\", \"id\"], \"path\": \"orders/<order_id>/transactions/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [\"order_id\"], \"path\": \"orders/<order_id>/transactions.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"transaction\",\n      \"plural\": \"transactions\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      order_id = null,\n      fields = null,\n      in_shop_currency = null\n    }: FindArgs\n  ): Promise<Transaction | null> {\n    const result = await this.baseFind<Transaction>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id, \"order_id\": order_id},\n      params: {\"fields\": fields, \"in_shop_currency\": in_shop_currency},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async all(\n    {\n      session,\n      order_id = null,\n      since_id = null,\n      fields = null,\n      in_shop_currency = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Transaction>> {\n    const response = await this.baseFind<Transaction>({\n      session: session,\n      urlIds: {\"order_id\": order_id},\n      params: {\"since_id\": since_id, \"fields\": fields, \"in_shop_currency\": in_shop_currency, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      order_id = null,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<Transaction>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {\"order_id\": order_id},\n      params: {...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public kind: string | null;\n  public amount: string | null;\n  public authorization: string | null;\n  public authorization_expires_at: string | null;\n  public created_at: string | null;\n  public currency: string | null;\n  public currency_exchange_adjustment: {[key: string]: unknown} | null;\n  public device_id: number | null;\n  public error_code: string | null;\n  public extended_authorization_attributes: {[key: string]: unknown} | null;\n  public gateway: string | null;\n  public id: number | null;\n  public location_id: number | null;\n  public message: string | null;\n  public order_id: number | null;\n  public parent_id: number | null;\n  public payment_details: {[key: string]: unknown} | null;\n  public payments_refund_attributes: {[key: string]: unknown} | null;\n  public processed_at: string | null;\n  public receipt: {[key: string]: unknown} | null;\n  public source_name: string | null;\n  public status: string | null;\n  public test: boolean | null;\n  public user_id: number | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AA4BlH,MAAO,WAAY,SAAQA,SAAI,CAAA;AAC5B,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,2CAA2C,EAAC;AACtH,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,qCAAqC,EAAC;AAC9G,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,0CAA0C,EAAC;AACzH,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,qCAAqC;KAChH;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,aAAa;AACzB,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,QAAQ,GAAG,IAAI,EACf,MAAM,GAAG,IAAI,EACb,gBAAgB,GAAG,IAAI,EACd,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAc;AAC9C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAC;YACxC,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,EAAC;AACjE,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,GAAG,CACrB,EACE,OAAO,EACP,QAAQ,GAAG,IAAI,EACf,QAAQ,GAAG,IAAI,EACf,MAAM,GAAG,IAAI,EACb,gBAAgB,GAAG,IAAI,EACvB,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAc;AAChD,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAC;AAC9B,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,SAAS,EAAC;AACrG,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,aAAa,KAAK,CACvB,EACE,OAAO,EACP,QAAQ,GAAG,IAAI,EACf,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAc;AAC/C,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAC;AAC9B,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACtB,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,IAAI;AACJ,IAAA,MAAM;AACN,IAAA,aAAa;AACb,IAAA,wBAAwB;AACxB,IAAA,UAAU;AACV,IAAA,QAAQ;AACR,IAAA,4BAA4B;AAC5B,IAAA,SAAS;AACT,IAAA,UAAU;AACV,IAAA,iCAAiC;AACjC,IAAA,OAAO;AACP,IAAA,EAAE;AACF,IAAA,WAAW;AACX,IAAA,OAAO;AACP,IAAA,QAAQ;AACR,IAAA,SAAS;AACT,IAAA,eAAe;AACf,IAAA,0BAA0B;AAC1B,IAAA,YAAY;AACZ,IAAA,OAAO;AACP,IAAA,WAAW;AACX,IAAA,MAAM;AACN,IAAA,IAAI;AACJ,IAAA,OAAO;;;;;"}
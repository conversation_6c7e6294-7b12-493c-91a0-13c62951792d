{"version": 3, "file": "get-embedded-app-url.js", "sources": ["../../../../../../lib/auth/get-embedded-app-url.ts"], "sourcesContent": ["import * as ShopifyErrors from '../error';\nimport {ConfigInterface} from '../base-types';\nimport {abstractConvertRequest} from '../../runtime/http';\nimport {sanitizeHost} from '../utils/shop-validator';\n\nimport {decodeHost} from './decode-host';\nimport {GetEmbeddedAppUrlParams} from './types';\n\nexport type GetEmbeddedAppUrl = (\n  params: GetEmbeddedAppUrlParams,\n) => Promise<string>;\n\nexport type BuildEmbeddedAppUrl = (host: string) => string;\n\nexport function getEmbeddedAppUrl(config: ConfigInterface): GetEmbeddedAppUrl {\n  return async ({...adapterArgs}: GetEmbeddedAppUrlParams): Promise<string> => {\n    const request = await abstractConvertRequest(adapterArgs);\n\n    if (!request) {\n      throw new ShopifyErrors.MissingRequiredArgument(\n        'getEmbeddedAppUrl requires a request object argument',\n      );\n    }\n\n    if (!request.url) {\n      throw new ShopifyErrors.InvalidRequestError(\n        'Request does not contain a URL',\n      );\n    }\n\n    const url = new URL(request.url, `https://${request.headers.host}`);\n    const host = url.searchParams.get('host');\n\n    if (typeof host !== 'string') {\n      throw new ShopifyErrors.InvalidRequestError(\n        'Request does not contain a host query parameter',\n      );\n    }\n\n    return buildEmbeddedAppUrl(config)(host);\n  };\n}\n\nexport function buildEmbeddedAppUrl(\n  config: ConfigInterface,\n): BuildEmbeddedAppUrl {\n  return (host: string): string => {\n    sanitizeHost()(host, true);\n    const decodedHost = decodeHost(host);\n\n    return `https://${decodedHost}/apps/${config.apiKey}`;\n  };\n}\n"], "names": ["abstractConvertRequest", "ShopifyErrors.MissingRequiredArgument", "ShopifyErrors.InvalidRequestError", "sanitizeHost", "decodeHost"], "mappings": ";;;;;;;AAcM,SAAU,iBAAiB,CAAC,MAAuB,EAAA;AACvD,IAAA,OAAO,OAAO,EAAC,GAAG,WAAW,EAA0B,KAAqB;AAC1E,QAAA,MAAM,OAAO,GAAG,MAAMA,4BAAsB,CAAC,WAAW,CAAC;QAEzD,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,MAAM,IAAIC,6BAAqC,CAC7C,sDAAsD,CACvD;QACH;AAEA,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AAChB,YAAA,MAAM,IAAIC,yBAAiC,CACzC,gCAAgC,CACjC;QACH;AAEA,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAA,QAAA,EAAW,OAAO,CAAC,OAAO,CAAC,IAAI,CAAA,CAAE,CAAC;QACnE,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;AAEzC,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,YAAA,MAAM,IAAIA,yBAAiC,CACzC,iDAAiD,CAClD;QACH;AAEA,QAAA,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAC1C,IAAA,CAAC;AACH;AAEM,SAAU,mBAAmB,CACjC,MAAuB,EAAA;IAEvB,OAAO,CAAC,IAAY,KAAY;AAC9B,QAAAC,0BAAY,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAC1B,QAAA,MAAM,WAAW,GAAGC,qBAAU,CAAC,IAAI,CAAC;AAEpC,QAAA,OAAO,WAAW,WAAW,CAAA,MAAA,EAAS,MAAM,CAAC,MAAM,EAAE;AACvD,IAAA,CAAC;AACH;;;;;"}
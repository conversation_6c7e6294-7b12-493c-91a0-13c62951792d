.container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.backButton:hover {
  background: #e5e7eb;
  color: #111827;
}

.headerInfo {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.headerInfo h1 {
  margin: 0;
  color: #111827;
  font-size: 28px;
  font-weight: 700;
}

.status {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  color: #6b7280;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  color: #ef4444;
  text-align: center;
}

.error p {
  margin: 16px 0;
}

.errorActions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.retryButton {
  padding: 8px 16px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background: #1d4ed8;
}

.content {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 32px;
}

.mainSection {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.cardHeader h2 {
  margin: 0;
  color: #111827;
  font-size: 18px;
  font-weight: 600;
}

.cardHeader svg {
  color: #2563eb;
}

.items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.item {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #f3f4f6;
}

.itemInfo h4 {
  margin: 0 0 12px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
}

.itemInfo p {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 14px;
}

.itemInfo p:last-child {
  margin-bottom: 0;
}

.customerInfo p {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
}

.customerInfo p:last-child {
  margin-bottom: 0;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.timelineItem {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.timelineIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #dbeafe;
  color: #2563eb;
  border-radius: 50%;
  flex-shrink: 0;
}

.timelineContent h4 {
  margin: 0 0 4px 0;
  color: #111827;
  font-size: 14px;
  font-weight: 600;
}

.timelineContent p {
  margin: 0;
  color: #6b7280;
  font-size: 13px;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s;
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.approveButton {
  background: #10b981;
  color: white;
}

.approveButton:hover:not(:disabled) {
  background: #059669;
}

.rejectButton {
  background: #ef4444;
  color: white;
}

.rejectButton:hover:not(:disabled) {
  background: #dc2626;
}

.labelInfo p {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
}

.downloadButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: #2563eb;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: background-color 0.2s;
}

.downloadButton:hover {
  background: #1d4ed8;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .headerInfo {
    width: 100%;
  }

  .errorActions {
    flex-direction: column;
    width: 100%;
  }
}

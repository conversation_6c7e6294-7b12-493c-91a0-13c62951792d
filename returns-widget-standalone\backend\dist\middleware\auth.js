"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateToken = exports.loadMerchant = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const database_1 = require("../models/database");
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-key-change-in-production';
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    jsonwebtoken_1.default.verify(token, JWT_SECRET, (err, decoded) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.merchantId = decoded.merchantId;
        next();
    });
};
exports.authenticateToken = authenticateToken;
const loadMerchant = (req, res, next) => {
    if (!req.merchantId) {
        return res.status(401).json({ error: 'Merchant ID required' });
    }
    const merchant = database_1.db.getMerchantById(req.merchantId);
    if (!merchant || !merchant.isActive) {
        return res.status(404).json({ error: 'Merchant not found or inactive' });
    }
    req.merchant = merchant;
    next();
};
exports.loadMerchant = loadMerchant;
const generateToken = (merchantId) => {
    return jsonwebtoken_1.default.sign({ merchantId }, JWT_SECRET, { expiresIn: '30d' });
};
exports.generateToken = generateToken;

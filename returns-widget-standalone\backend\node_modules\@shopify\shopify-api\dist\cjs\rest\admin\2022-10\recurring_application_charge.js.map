{"version": 3, "file": "recurring_application_charge.js", "sources": ["../../../../../../../rest/admin/2022-10/recurring_application_charge.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  fields?: unknown;\n}\ninterface DeleteArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  since_id?: unknown;\n  fields?: unknown;\n}\ninterface CustomizeArgs {\n  [key: string]: unknown;\n  body?: {[key: string]: unknown} | null;\n}\n\nexport class RecurringApplicationCharge extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"id\"], \"path\": \"recurring_application_charges/<id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"recurring_application_charges.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"recurring_application_charges/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [], \"path\": \"recurring_application_charges.json\"},\n    {\"http_method\": \"put\", \"operation\": \"customize\", \"ids\": [\"id\"], \"path\": \"recurring_application_charges/<id>/customize.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"recurring_application_charge\",\n      \"plural\": \"recurring_application_charges\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      fields = null\n    }: FindArgs\n  ): Promise<RecurringApplicationCharge | null> {\n    const result = await this.baseFind<RecurringApplicationCharge>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {\"fields\": fields},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async delete(\n    {\n      session,\n      id\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<RecurringApplicationCharge>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      since_id = null,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<RecurringApplicationCharge>> {\n    const response = await this.baseFind<RecurringApplicationCharge>({\n      session: session,\n      urlIds: {},\n      params: {\"since_id\": since_id, \"fields\": fields, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public async customize(\n    {\n      body = null,\n      ...otherArgs\n    }: CustomizeArgs\n  ): Promise<unknown> {\n    const response = await this.request<RecurringApplicationCharge>({\n      http_method: \"put\",\n      operation: \"customize\",\n      session: this.session,\n      urlIds: {\"id\": this.id},\n      params: {...otherArgs},\n      body: body,\n      entity: this,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public activated_on: string | null;\n  public billing_on: string | null;\n  public cancelled_on: string | null;\n  public capped_amount: string | number | null;\n  public confirmation_url: string | null;\n  public created_at: string | null;\n  public currency: string | null;\n  public id: number | null;\n  public name: string | null;\n  public price: string | number | null;\n  public return_url: string | null;\n  public status: string | null;\n  public terms: string | null;\n  public test: boolean | null;\n  public trial_days: number | null;\n  public trial_ends_on: string | null;\n  public updated_at: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AA2BlH,MAAO,0BAA2B,SAAQA,SAAI,CAAA;AAC3C,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,yCAAyC,EAAC;AAClH,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,oCAAoC,EAAC;AACnG,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,yCAAyC,EAAC;AAC5G,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,oCAAoC,EAAC;AACrG,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,mDAAmD;KAC5H;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,8BAA8B;AAC1C,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,MAAM,GAAG,IAAI,EACJ,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAA6B;AAC7D,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAC;AAC3B,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,MAAM,CACxB,EACE,OAAO,EACP,EAAE,EACS,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAA6B;AAC9D,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,QAAQ,GAAG,IAAI,EACf,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAA6B;AAC/D,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AAC/D,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;IAEO,MAAM,SAAS,CACpB,EACE,IAAI,GAAG,IAAI,EACX,GAAG,SAAS,EACE,EAAA;AAEhB,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAA6B;AAC9D,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,WAAW;YACtB,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAC;AACvB,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACtB,YAAA,IAAI,EAAE,IAAI;AACV,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,YAAY;AACZ,IAAA,UAAU;AACV,IAAA,YAAY;AACZ,IAAA,aAAa;AACb,IAAA,gBAAgB;AAChB,IAAA,UAAU;AACV,IAAA,QAAQ;AACR,IAAA,EAAE;AACF,IAAA,IAAI;AACJ,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,KAAK;AACL,IAAA,IAAI;AACJ,IAAA,UAAU;AACV,IAAA,aAAa;AACb,IAAA,UAAU;;;;;"}
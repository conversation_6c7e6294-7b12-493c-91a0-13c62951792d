{"version": 3, "file": "validate.js", "sources": ["../../../../../../lib/webhooks/validate.ts"], "sourcesContent": ["import {logger} from '../logger';\nimport {validateHmacFromRequestFactory} from '../utils/hmac-validator';\nimport {HmacValidationType, ValidationErrorReason} from '../utils/types';\nimport {\n  abstractConvertRequest,\n  getHeader,\n  Headers,\n  NormalizedRequest,\n} from '../../runtime/http';\nimport {ShopifyHeader} from '../types';\nimport {ConfigInterface} from '../base-types';\n\nimport {\n  WebhookFields,\n  WebhookValidateParams,\n  WebhookValidation,\n  WebhookValidationErrorReason,\n  WebhookValidationMissingHeaders,\n  WebhookValidationValid,\n} from './types';\nimport {topicForStorage} from './registry';\n\nconst OPTIONAL_HANDLER_PROPERTIES = {\n  subTopic: ShopifyHeader.SubTopic,\n};\n\nconst HANDLER_PROPERTIES = {\n  apiVersion: ShopifyHeader.ApiVersion,\n  domain: ShopifyHeader.Domain,\n  hmac: ShopifyHeader.Hmac,\n  topic: ShopifyHeader.Topic,\n  webhookId: ShopifyHeader.WebhookId,\n  ...OPTIONAL_HANDLER_PROPERTIES,\n};\n\nexport function validateFactory(config: ConfigInterface) {\n  return async function validate({\n    rawBody,\n    ...adapterArgs\n  }: WebhookValidateParams): Promise<WebhookValidation> {\n    const request: NormalizedRequest =\n      await abstractConvertRequest(adapterArgs);\n\n    const validHmacResult = await validateHmacFromRequestFactory(config)({\n      type: HmacValidationType.Webhook,\n      rawBody,\n      ...adapterArgs,\n    });\n\n    if (!validHmacResult.valid) {\n      if (validHmacResult.reason === ValidationErrorReason.InvalidHmac) {\n        const log = logger(config);\n        await log.debug(\n          \"Webhook HMAC validation failed. Please note that events manually triggered from a store's Notifications settings will fail this validation. To test this, please use the CLI or trigger the actual event in a development store.\",\n        );\n      }\n      return validHmacResult;\n    }\n\n    return checkWebhookHeaders(request.headers);\n  };\n}\n\nfunction checkWebhookHeaders(\n  headers: Headers,\n): WebhookValidationMissingHeaders | WebhookValidationValid {\n  const missingHeaders: ShopifyHeader[] = [];\n  const entries = Object.entries(HANDLER_PROPERTIES) as [\n    keyof WebhookFields,\n    ShopifyHeader,\n  ][];\n  const headerValues = entries.reduce((acc, [property, headerName]) => {\n    const headerValue = getHeader(headers, headerName);\n    if (headerValue) {\n      acc[property] = headerValue;\n    } else if (!(property in OPTIONAL_HANDLER_PROPERTIES)) {\n      missingHeaders.push(headerName);\n    }\n\n    return acc;\n  }, {} as WebhookFields);\n\n  if (missingHeaders.length) {\n    return {\n      valid: false,\n      reason: WebhookValidationErrorReason.MissingHeaders,\n      missingHeaders,\n    };\n  } else {\n    return {\n      valid: true,\n      ...headerValues,\n      ...(headerValues.subTopic ? {subTopic: headerValues.subTopic} : {}),\n      topic: topicForStorage(headerValues.topic),\n    };\n  }\n}\n"], "names": ["ShopifyHeader", "abstractConvertRequest", "validateHmacFromRequestFactory", "HmacValidationType", "ValidationErrorReason", "logger", "headers", "<PERSON><PERSON><PERSON><PERSON>", "WebhookValidationErrorReason", "topicForStorage"], "mappings": ";;;;;;;;;;;AAsBA,MAAM,2BAA2B,GAAG;IAClC,QAAQ,EAAEA,mBAAa,CAAC,QAAQ;CACjC;AAED,MAAM,kBAAkB,GAAG;IACzB,UAAU,EAAEA,mBAAa,CAAC,UAAU;IACpC,MAAM,EAAEA,mBAAa,CAAC,MAAM;IAC5B,IAAI,EAAEA,mBAAa,CAAC,IAAI;IACxB,KAAK,EAAEA,mBAAa,CAAC,KAAK;IAC1B,SAAS,EAAEA,mBAAa,CAAC,SAAS;AAClC,IAAA,GAAG,2BAA2B;CAC/B;AAEK,SAAU,eAAe,CAAC,MAAuB,EAAA;IACrD,OAAO,eAAe,QAAQ,CAAC,EAC7B,OAAO,EACP,GAAG,WAAW,EACQ,EAAA;AACtB,QAAA,MAAM,OAAO,GACX,MAAMC,4BAAsB,CAAC,WAAW,CAAC;AAE3C,QAAA,MAAM,eAAe,GAAG,MAAMC,4CAA8B,CAAC,MAAM,CAAC,CAAC;YACnE,IAAI,EAAEC,0BAAkB,CAAC,OAAO;YAChC,OAAO;AACP,YAAA,GAAG,WAAW;AACf,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;YAC1B,IAAI,eAAe,CAAC,MAAM,KAAKC,6BAAqB,CAAC,WAAW,EAAE;AAChE,gBAAA,MAAM,GAAG,GAAGC,cAAM,CAAC,MAAM,CAAC;AAC1B,gBAAA,MAAM,GAAG,CAAC,KAAK,CACb,kOAAkO,CACnO;YACH;AACA,YAAA,OAAO,eAAe;QACxB;AAEA,QAAA,OAAO,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC;AAC7C,IAAA,CAAC;AACH;AAEA,SAAS,mBAAmB,CAC1BC,SAAgB,EAAA;IAEhB,MAAM,cAAc,GAAoB,EAAE;IAC1C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAG9C;AACH,IAAA,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAI;QAClE,MAAM,WAAW,GAAGC,iBAAS,CAACD,SAAO,EAAE,UAAU,CAAC;QAClD,IAAI,WAAW,EAAE;AACf,YAAA,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW;QAC7B;AAAO,aAAA,IAAI,EAAE,QAAQ,IAAI,2BAA2B,CAAC,EAAE;AACrD,YAAA,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;QACjC;AAEA,QAAA,OAAO,GAAG;IACZ,CAAC,EAAE,EAAmB,CAAC;AAEvB,IAAA,IAAI,cAAc,CAAC,MAAM,EAAE;QACzB,OAAO;AACL,YAAA,KAAK,EAAE,KAAK;YACZ,MAAM,EAAEE,oCAA4B,CAAC,cAAc;YACnD,cAAc;SACf;IACH;SAAO;QACL,OAAO;AACL,YAAA,KAAK,EAAE,IAAI;AACX,YAAA,GAAG,YAAY;AACf,YAAA,IAAI,YAAY,CAAC,QAAQ,GAAG,EAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAC,GAAG,EAAE,CAAC;AACnE,YAAA,KAAK,EAAEC,wBAAe,CAAC,YAAY,CAAC,KAAK,CAAC;SAC3C;IACH;AACF;;;;"}
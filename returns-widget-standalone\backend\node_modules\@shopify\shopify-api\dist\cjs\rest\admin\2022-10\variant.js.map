{"version": 3, "file": "variant.js", "sources": ["../../../../../../../rest/admin/2022-10/variant.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  fields?: unknown;\n}\ninterface DeleteArgs {\n  session: Session;\n  id: number | string;\n  product_id?: number | string | null;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  product_id?: number | string | null;\n  limit?: unknown;\n  presentment_currencies?: unknown;\n  since_id?: unknown;\n  fields?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n  product_id?: number | string | null;\n}\n\nexport class Variant extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"product_id\", \"id\"], \"path\": \"products/<product_id>/variants/<id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [\"product_id\"], \"path\": \"products/<product_id>/variants/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"product_id\"], \"path\": \"products/<product_id>/variants.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"variants/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [\"product_id\"], \"path\": \"products/<product_id>/variants.json\"},\n    {\"http_method\": \"put\", \"operation\": \"put\", \"ids\": [\"id\"], \"path\": \"variants/<id>.json\"}\n  ];\n  protected static readOnlyAttributes: string[] = [\n    \"inventory_quantity\"\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"variant\",\n      \"plural\": \"variants\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      fields = null\n    }: FindArgs\n  ): Promise<Variant | null> {\n    const result = await this.baseFind<Variant>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {\"fields\": fields},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async delete(\n    {\n      session,\n      id,\n      product_id = null\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<Variant>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"id\": id, \"product_id\": product_id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      product_id = null,\n      limit = null,\n      presentment_currencies = null,\n      since_id = null,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Variant>> {\n    const response = await this.baseFind<Variant>({\n      session: session,\n      urlIds: {\"product_id\": product_id},\n      params: {\"limit\": limit, \"presentment_currencies\": presentment_currencies, \"since_id\": since_id, \"fields\": fields, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      product_id = null,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<Variant>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {\"product_id\": product_id},\n      params: {...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public barcode: string | null;\n  public compare_at_price: string | null;\n  public created_at: string | null;\n  public fulfillment_service: string | null;\n  public grams: number | null;\n  public id: number | null;\n  public image_id: number | null;\n  public inventory_item_id: number | null;\n  public inventory_management: string | null;\n  public inventory_policy: string | null;\n  public inventory_quantity: number | null;\n  public old_inventory_quantity: number | null;\n  public option: {[key: string]: unknown} | null;\n  public position: number | null;\n  public presentment_prices: {[key: string]: unknown}[] | null;\n  public price: string | null;\n  public product_id: number | null;\n  public requires_shipping: boolean | null;\n  public sku: string | null;\n  public tax_code: string | null;\n  public taxable: boolean | null;\n  public title: string | null;\n  public updated_at: string | null;\n  public weight: number | null;\n  public weight_unit: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAgClH,MAAO,OAAQ,SAAQA,SAAI,CAAA;AACxB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,0CAA0C,EAAC;AACjI,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,2CAA2C,EAAC;AACxH,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,qCAAqC,EAAC;AAChH,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB,EAAC;AACvF,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,qCAAqC,EAAC;AAClH,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB;KACvF;IACS,OAAO,kBAAkB,GAAa;QAC9C;KACD;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,SAAS;AACrB,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,MAAM,GAAG,IAAI,EACJ,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAU;AAC1C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAC;AAC3B,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;AAEO,IAAA,aAAa,MAAM,CACxB,EACE,OAAO,EACP,EAAE,EACF,UAAU,GAAG,IAAI,EACN,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAU;AAC3C,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAE,UAAU,EAAC;AAC5C,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,UAAU,GAAG,IAAI,EACjB,KAAK,GAAG,IAAI,EACZ,sBAAsB,GAAG,IAAI,EAC7B,QAAQ,GAAG,IAAI,EACf,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAU;AAC5C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,YAAY,EAAE,UAAU,EAAC;YAClC,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,wBAAwB,EAAE,sBAAsB,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AACjI,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,aAAa,KAAK,CACvB,EACE,OAAO,EACP,UAAU,GAAG,IAAI,EACjB,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAU;AAC3C,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,YAAY,EAAE,UAAU,EAAC;AAClC,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACtB,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,OAAO;AACP,IAAA,gBAAgB;AAChB,IAAA,UAAU;AACV,IAAA,mBAAmB;AACnB,IAAA,KAAK;AACL,IAAA,EAAE;AACF,IAAA,QAAQ;AACR,IAAA,iBAAiB;AACjB,IAAA,oBAAoB;AACpB,IAAA,gBAAgB;AAChB,IAAA,kBAAkB;AAClB,IAAA,sBAAsB;AACtB,IAAA,MAAM;AACN,IAAA,QAAQ;AACR,IAAA,kBAAkB;AAClB,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,iBAAiB;AACjB,IAAA,GAAG;AACH,IAAA,QAAQ;AACR,IAAA,OAAO;AACP,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,WAAW;;;;;"}
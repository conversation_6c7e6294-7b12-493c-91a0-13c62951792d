import { useState } from 'react'
import { api } from '../../services/api'
import { CheckCircle, AlertCircle, ExternalLink } from 'lucide-react'
import styles from './ShopifySettings.module.css'

export const ShopifySettings = () => {
  const [accessToken, setAccessToken] = useState('')
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean
    shopName?: string
  } | null>(null)

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      await api.saveShopifyCredentials({ accessToken })
      setSuccess('Shopify credentials saved successfully!')
      
      // Auto-test connection after saving
      await testConnection()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save credentials')
    } finally {
      setLoading(false)
    }
  }

  const testConnection = async () => {
    if (!accessToken.trim()) {
      setError('Please enter an access token first')
      return
    }

    setTesting(true)
    setError(null)

    try {
      const result = await api.testShopifyConnection()
      setConnectionStatus(result)
      
      if (result.connected) {
        setSuccess(`Connected to ${result.shopName || 'your Shopify store'}!`)
      } else {
        setError('Failed to connect to Shopify. Please check your credentials.')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection test failed')
      setConnectionStatus({ connected: false })
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Shopify Integration</h2>
        <p>Connect your Shopify store to enable order lookup and return creation.</p>
      </div>

      <div className={styles.setupGuide}>
        <h3>Setup Instructions</h3>
        <ol>
          <li>Go to your Shopify admin panel</li>
          <li>Navigate to <strong>Settings → Apps and sales channels</strong></li>
          <li>Click <strong>Develop apps</strong></li>
          <li>Create a new app called "Returns Widget"</li>
          <li>Configure these scopes: <code>read_orders</code>, <code>write_orders</code>, <code>read_returns</code>, <code>write_returns</code></li>
          <li>Generate an access token and paste it below</li>
        </ol>
        <a 
          href="https://help.shopify.com/en/manual/apps/app-types/custom-apps"
          target="_blank"
          rel="noopener noreferrer"
          className={styles.helpLink}
        >
          <ExternalLink size={16} />
          View detailed setup guide
        </a>
      </div>

      <form onSubmit={handleSave} className={styles.form}>
        <div className={styles.field}>
          <label htmlFor="accessToken">
            Shopify Access Token
            <span className={styles.required}>*</span>
          </label>
          <input
            id="accessToken"
            type="password"
            value={accessToken}
            onChange={(e) => setAccessToken(e.target.value)}
            placeholder="shpat_..."
            required
            disabled={loading || testing}
            className={styles.input}
          />
          <small className={styles.hint}>
            Your private app access token from Shopify admin
          </small>
        </div>

        {connectionStatus && (
          <div className={`${styles.status} ${connectionStatus.connected ? styles.success : styles.error}`}>
            {connectionStatus.connected ? (
              <>
                <CheckCircle size={20} />
                <span>Connected to {connectionStatus.shopName}</span>
              </>
            ) : (
              <>
                <AlertCircle size={20} />
                <span>Connection failed</span>
              </>
            )}
          </div>
        )}

        {error && (
          <div className={styles.errorMessage}>
            <AlertCircle size={16} />
            {error}
          </div>
        )}

        {success && (
          <div className={styles.successMessage}>
            <CheckCircle size={16} />
            {success}
          </div>
        )}

        <div className={styles.actions}>
          <button
            type="button"
            onClick={testConnection}
            disabled={!accessToken.trim() || testing || loading}
            className={styles.testButton}
          >
            {testing ? 'Testing...' : 'Test Connection'}
          </button>
          
          <button
            type="submit"
            disabled={loading || testing || !accessToken.trim()}
            className={styles.saveButton}
          >
            {loading ? 'Saving...' : 'Save Credentials'}
          </button>
        </div>
      </form>
    </div>
  )
}

{"version": 3, "file": "nonce.js", "sources": ["../../../../../../../lib/auth/oauth/nonce.ts"], "sourcesContent": ["import {crypto} from '../../../runtime/crypto';\n\nexport type Nonce = () => string;\n\nexport function nonce(): string {\n  const length = 15;\n\n  const bytes = crypto.getRandomValues(new Uint8Array(length));\n\n  const nonce = bytes\n    .map((byte: number) => {\n      return byte % 10;\n    })\n    .join('');\n\n  return nonce;\n}\n"], "names": ["crypto"], "mappings": ";;;;;SAIgB,KAAK,GAAA;IACnB,MAAM,MAAM,GAAG,EAAE;AAEjB,IAAA,MAAM,KAAK,GAAGA,aAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAE5D,MAAM,KAAK,GAAG;AACX,SAAA,GAAG,CAAC,CAAC,IAAY,KAAI;QACpB,OAAO,IAAI,GAAG,EAAE;AAClB,IAAA,CAAC;SACA,IAAI,CAAC,EAAE,CAAC;AAEX,IAAA,OAAO,KAAK;AACd;;;;"}
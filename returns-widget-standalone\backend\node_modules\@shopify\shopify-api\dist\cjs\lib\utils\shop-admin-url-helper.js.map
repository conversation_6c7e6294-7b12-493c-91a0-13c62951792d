{"version": 3, "file": "shop-admin-url-helper.js", "sources": ["../../../../../../lib/utils/shop-admin-url-helper.ts"], "sourcesContent": ["// Converts admin.shopify.com/store/my-shop to my-shop.myshopify.com\nexport function shopAdminUrlToLegacyUrl(shopAdminUrl: string): string | null {\n  const shopUrl = removeProtocol(shopAdminUrl);\n\n  const isShopAdminUrl = shopUrl.split('.')[0] === 'admin';\n\n  if (!isShopAdminUrl) {\n    return null;\n  }\n\n  const regex = new RegExp(`admin\\\\..+/store/([^/]+)`);\n  const matches = shopUrl.match(regex);\n\n  if (matches && matches.length === 2) {\n    const shopName = matches[1];\n    const isSpinUrl = shopUrl.includes('spin.dev/store/');\n    const isLocalUrl = shopUrl.includes('shop.dev/store/');\n\n    if (isSpinUrl) {\n      return spinAdminUrlToLegacyUrl(shopUrl);\n    } else if (isLocalUrl) {\n      return localAdminUrlToLegacyUrl(shopUrl);\n    } else {\n      return `${shopName}.myshopify.com`;\n    }\n  } else {\n    return null;\n  }\n}\n\n// Converts my-shop.myshopify.com to admin.shopify.com/store/my-shop\nexport function legacyUrlToShopAdminUrl(legacyAdminUrl: string): string | null {\n  const shopUrl = removeProtocol(legacyAdminUrl);\n  const regex = new RegExp(`(.+)\\\\.myshopify\\\\.com$`);\n  const matches = shopUrl.match(regex);\n\n  if (matches && matches.length === 2) {\n    const shopName = matches[1];\n    return `admin.shopify.com/store/${shopName}`;\n  } else {\n    const isSpinUrl = shopUrl.endsWith('spin.dev');\n    const isLocalUrl = shopUrl.endsWith('shop.dev');\n    if (isSpinUrl) {\n      return spinLegacyUrlToAdminUrl(shopUrl);\n    } else if (isLocalUrl) {\n      return localLegacyUrlToAdminUrl(shopUrl);\n    } else {\n      return null;\n    }\n  }\n}\n\nfunction spinAdminUrlToLegacyUrl(shopAdminUrl: string) {\n  const spinRegex = new RegExp(`admin\\\\.web\\\\.(.+\\\\.spin\\\\.dev)/store/(.+)`);\n  const spinMatches = shopAdminUrl.match(spinRegex);\n\n  if (spinMatches && spinMatches.length === 3) {\n    const spinUrl = spinMatches[1];\n    const shopName = spinMatches[2];\n    return `${shopName}.shopify.${spinUrl}`;\n  } else {\n    return null;\n  }\n}\n\nfunction localAdminUrlToLegacyUrl(shopAdminUrl: string) {\n  const localRegex = new RegExp(`admin\\\\.shop\\\\.dev/store/(.+)`);\n  const localMatches = shopAdminUrl.match(localRegex);\n\n  if (localMatches && localMatches.length === 2) {\n    const shopName = localMatches[1];\n    return `${shopName}.shop.dev`;\n  } else {\n    return null;\n  }\n}\n\nfunction spinLegacyUrlToAdminUrl(legacyAdminUrl: string) {\n  const spinRegex = new RegExp(`(.+)\\\\.shopify\\\\.(.+\\\\.spin\\\\.dev)`);\n  const spinMatches = legacyAdminUrl.match(spinRegex);\n\n  if (spinMatches && spinMatches.length === 3) {\n    const shopName = spinMatches[1];\n    const spinUrl = spinMatches[2];\n    return `admin.web.${spinUrl}/store/${shopName}`;\n  } else {\n    return null;\n  }\n}\n\nfunction localLegacyUrlToAdminUrl(legacyAdminUrl: string) {\n  const localRegex = new RegExp(`(.+)\\\\.shop\\\\.dev$`);\n  const localMatches = legacyAdminUrl.match(localRegex);\n\n  if (localMatches && localMatches.length === 2) {\n    const shopName = localMatches[1];\n    return `admin.shop.dev/store/${shopName}`;\n  } else {\n    return null;\n  }\n}\nfunction removeProtocol(url: string): string {\n  return url.replace(/^https?:\\/\\//, '').replace(/\\/$/, '');\n}\n"], "names": [], "mappings": ";;AAAA;AACM,SAAU,uBAAuB,CAAC,YAAoB,EAAA;AAC1D,IAAA,MAAM,OAAO,GAAG,cAAc,CAAC,YAAY,CAAC;AAE5C,IAAA,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO;IAExD,IAAI,CAAC,cAAc,EAAE;AACnB,QAAA,OAAO,IAAI;IACb;AAEA,IAAA,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,CAAA,wBAAA,CAA0B,CAAC;IACpD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;IAEpC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AACnC,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;QAC3B,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QACrD,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QAEtD,IAAI,SAAS,EAAE;AACb,YAAA,OAAO,uBAAuB,CAAC,OAAO,CAAC;QACzC;aAAO,IAAI,UAAU,EAAE;AACrB,YAAA,OAAO,wBAAwB,CAAC,OAAO,CAAC;QAC1C;aAAO;YACL,OAAO,CAAA,EAAG,QAAQ,CAAA,cAAA,CAAgB;QACpC;IACF;SAAO;AACL,QAAA,OAAO,IAAI;IACb;AACF;AAEA;AACM,SAAU,uBAAuB,CAAC,cAAsB,EAAA;AAC5D,IAAA,MAAM,OAAO,GAAG,cAAc,CAAC,cAAc,CAAC;AAC9C,IAAA,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,CAAA,uBAAA,CAAyB,CAAC;IACnD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;IAEpC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AACnC,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;QAC3B,OAAO,CAAA,wBAAA,EAA2B,QAAQ,CAAA,CAAE;IAC9C;SAAO;QACL,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC/C,IAAI,SAAS,EAAE;AACb,YAAA,OAAO,uBAAuB,CAAC,OAAO,CAAC;QACzC;aAAO,IAAI,UAAU,EAAE;AACrB,YAAA,OAAO,wBAAwB,CAAC,OAAO,CAAC;QAC1C;aAAO;AACL,YAAA,OAAO,IAAI;QACb;IACF;AACF;AAEA,SAAS,uBAAuB,CAAC,YAAoB,EAAA;AACnD,IAAA,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,CAAA,0CAAA,CAA4C,CAAC;IAC1E,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC;IAEjD,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3C,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC;AAC9B,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC;AAC/B,QAAA,OAAO,CAAA,EAAG,QAAQ,CAAA,SAAA,EAAY,OAAO,EAAE;IACzC;SAAO;AACL,QAAA,OAAO,IAAI;IACb;AACF;AAEA,SAAS,wBAAwB,CAAC,YAAoB,EAAA;AACpD,IAAA,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,CAAA,6BAAA,CAA+B,CAAC;IAC9D,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC;IAEnD,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7C,QAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC;QAChC,OAAO,CAAA,EAAG,QAAQ,CAAA,SAAA,CAAW;IAC/B;SAAO;AACL,QAAA,OAAO,IAAI;IACb;AACF;AAEA,SAAS,uBAAuB,CAAC,cAAsB,EAAA;AACrD,IAAA,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,CAAA,kCAAA,CAAoC,CAAC;IAClE,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC;IAEnD,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3C,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC;AAC/B,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC;AAC9B,QAAA,OAAO,CAAA,UAAA,EAAa,OAAO,CAAA,OAAA,EAAU,QAAQ,EAAE;IACjD;SAAO;AACL,QAAA,OAAO,IAAI;IACb;AACF;AAEA,SAAS,wBAAwB,CAAC,cAAsB,EAAA;AACtD,IAAA,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,CAAA,kBAAA,CAAoB,CAAC;IACnD,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC;IAErD,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7C,QAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC;QAChC,OAAO,CAAA,qBAAA,EAAwB,QAAQ,CAAA,CAAE;IAC3C;SAAO;AACL,QAAA,OAAO,IAAI;IACb;AACF;AACA,SAAS,cAAc,CAAC,GAAW,EAAA;AACjC,IAAA,OAAO,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC3D;;;;;"}
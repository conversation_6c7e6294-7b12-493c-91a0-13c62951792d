{"version": 3, "file": "types.js", "sources": ["../../src/rest/types.ts"], "sourcesContent": ["import {CustomFetchApi} from '@shopify/graphql-client';\n\nimport {AdminApiClientOptions} from '../types';\n\nexport enum Method {\n  Get = 'GET',\n  Post = 'POST',\n  Put = 'PUT',\n  Delete = 'DELETE',\n}\n\ntype SearchParamField = string | number;\nexport type SearchParamFields =\n  | SearchParamField\n  | SearchParamField[]\n  | Record<string, SearchParamField | SearchParamField[]>;\nexport type SearchParams = Record<string, SearchParamFields>;\n\nexport type HeaderOptions = Record<string, string | number | string[]>;\n\nexport interface GetRequestOptions {\n  headers?: HeaderOptions;\n  data?: Record<string, any> | string;\n  searchParams?: SearchParams;\n  retries?: number;\n  apiVersion?: string;\n}\n\nexport interface PostRequestOptions extends GetRequestOptions {\n  data: Required<GetRequestOptions>['data'];\n}\n\nexport interface PutRequestOptions extends PostRequestOptions {}\n\nexport interface DeleteRequestOptions extends GetRequestOptions {}\n\nexport interface AdminRestApiClientOptions extends AdminApiClientOptions {\n  scheme?: 'https' | 'http';\n  defaultRetryTime?: number;\n  formatPaths?: boolean;\n}\n\nexport type RequestOptions = (GetRequestOptions | PostRequestOptions) & {\n  method: Method;\n};\n\nexport interface AdminRestApiClient {\n  get: (\n    path: string,\n    options?: GetRequestOptions,\n  ) => ReturnType<CustomFetchApi>;\n  put: (\n    path: string,\n    options?: PutRequestOptions,\n  ) => ReturnType<CustomFetchApi>;\n  post: (\n    path: string,\n    options?: PostRequestOptions,\n  ) => ReturnType<CustomFetchApi>;\n  delete: (\n    path: string,\n    options?: DeleteRequestOptions,\n  ) => ReturnType<CustomFetchApi>;\n}\n"], "names": ["Method"], "mappings": ";;AAIYA;AAAZ,CAAA,UAAY,MAAM,EAAA;AAChB,IAAA,MAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACX,IAAA,MAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,MAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACX,IAAA,MAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EALWA,cAAM,KAANA,cAAM,GAAA,EAAA,CAAA,CAAA;;"}
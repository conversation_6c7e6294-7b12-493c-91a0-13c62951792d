'use strict';

require('../../runtime/crypto/crypto.js');
require('../../runtime/crypto/types.js');
var headers = require('../../runtime/http/headers.js');

async function webApiConvertRequest(adapterArgs) {
    const request = adapterArgs.rawRequest;
    const headers$1 = {};
    for (const [key, value] of request.headers.entries()) {
        headers.addHeader(headers$1, key, value);
    }
    return {
        headers: headers$1,
        method: request.method ?? 'GET',
        url: new URL(request.url).toString(),
    };
}
async function webApiConvertHeaders(headers$1, _adapterArgs) {
    const remixHeaders = new Headers();
    headers.flatHeaders(headers$1 ?? {}).forEach(([key, value]) => remixHeaders.append(key, value));
    return Promise.resolve(remixHeaders);
}
async function webApiConvertResponse(resp, adapterArgs) {
    return new Response(resp.body, {
        status: resp.statusCode,
        statusText: resp.statusText,
        headers: await webApiConvertHeaders(resp.headers ?? {}),
    });
}
function webApiRuntimeString() {
    return 'Web API';
}

exports.webApiConvertHeaders = webApiConvertHeaders;
exports.webApiConvertRequest = webApiConvertRequest;
exports.webApiConvertResponse = webApiConvertResponse;
exports.webApiRuntimeString = webApiRuntimeString;
//# sourceMappingURL=adapter.js.map

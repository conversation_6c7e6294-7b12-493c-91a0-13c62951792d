{"version": 3, "file": "adapter.js", "sources": ["../../../../../../adapters/web-api/adapter.ts"], "sourcesContent": ["import type {\n  Headers as ShopifyHeaders,\n  AdapterArgs,\n  NormalizedResponse,\n  NormalizedRequest,\n} from '../../runtime';\nimport {addHeader, flatHeaders} from '../../runtime';\n\ninterface WebApiAdapterArgs extends AdapterArgs {\n  rawRequest: Request;\n}\n\nexport async function webApiConvertRequest(\n  adapterArgs: WebApiAdapterArgs,\n): Promise<NormalizedRequest> {\n  const request = adapterArgs.rawRequest;\n  const headers = {};\n  for (const [key, value] of request.headers.entries()) {\n    addHeader(headers, key, value);\n  }\n\n  return {\n    headers,\n    method: request.method ?? 'GET',\n    url: new URL(request.url).toString(),\n  };\n}\n\nexport async function webApiConvertHeaders(\n  headers: ShopifyHeaders,\n  _adapterArgs: WebApiAdapterArgs,\n): Promise<Headers> {\n  const remixHeaders = new Headers();\n  flatHeaders(headers ?? {}).forEach(([key, value]) =>\n    remixHeaders.append(key, value),\n  );\n  return Promise.resolve(remixHeaders);\n}\n\nexport async function webApiConvertResponse(\n  resp: NormalizedResponse,\n  adapterArgs: WebApiAdapterArgs,\n): Promise<Response> {\n  return new Response(resp.body, {\n    status: resp.statusCode,\n    statusText: resp.statusText,\n    headers: await webApiConvertHeaders(resp.headers ?? {}, adapterArgs),\n  });\n}\n\nexport function webApiRuntimeString(): string {\n  return 'Web API';\n}\n"], "names": ["headers", "addHeader", "flatHeaders"], "mappings": ";;;;;;AAYO,eAAe,oBAAoB,CACxC,WAA8B,EAAA;AAE9B,IAAA,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU;IACtC,MAAMA,SAAO,GAAG,EAAE;AAClB,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;AACpD,QAAAC,iBAAS,CAACD,SAAO,EAAE,GAAG,EAAE,KAAK,CAAC;IAChC;IAEA,OAAO;iBACLA,SAAO;AACP,QAAA,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK;QAC/B,GAAG,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;KACrC;AACH;AAEO,eAAe,oBAAoB,CACxCA,SAAuB,EACvB,YAA+B,EAAA;AAE/B,IAAA,MAAM,YAAY,GAAG,IAAI,OAAO,EAAE;IAClCE,mBAAW,CAACF,SAAO,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAC9C,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAChC;AACD,IAAA,OAAO,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;AACtC;AAEO,eAAe,qBAAqB,CACzC,IAAwB,EACxB,WAA8B,EAAA;AAE9B,IAAA,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE;QAC7B,MAAM,EAAE,IAAI,CAAC,UAAU;QACvB,UAAU,EAAE,IAAI,CAAC,UAAU;QAC3B,OAAO,EAAE,MAAM,oBAAoB,CAAC,IAAI,CAAC,OAAO,IAAI,EAAe,CAAC;AACrE,KAAA,CAAC;AACJ;SAEgB,mBAAmB,GAAA;AACjC,IAAA,OAAO,SAAS;AAClB;;;;;;;"}
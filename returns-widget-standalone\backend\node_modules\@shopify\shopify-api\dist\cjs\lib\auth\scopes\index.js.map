{"version": 3, "file": "index.js", "sources": ["../../../../../../../lib/auth/scopes/index.ts"], "sourcesContent": ["/**\n * A class that represents a set of access token scopes.\n */\nclass AuthScopes {\n  public static SCOPE_DELIMITER = ',';\n\n  private compressedScopes: Set<string>;\n  private expandedScopes: Set<string>;\n  private originalScopes: Set<string>;\n\n  constructor(scopes: string | string[] | AuthScopes | undefined) {\n    let scopesArray: string[] = [];\n    if (typeof scopes === 'string') {\n      scopesArray = scopes.split(\n        new RegExp(`${AuthScopes.SCOPE_DELIMITER}\\\\s*`),\n      );\n    } else if (Array.isArray(scopes)) {\n      scopesArray = scopes;\n    } else if (scopes) {\n      scopesArray = Array.from(scopes.expandedScopes);\n    }\n\n    scopesArray = scopesArray\n      .map((scope) => scope.trim())\n      .filter((scope) => scope.length);\n\n    const impliedScopes = this.getImpliedScopes(scopesArray);\n\n    const scopeSet = new Set(scopesArray);\n    const impliedSet = new Set(impliedScopes);\n\n    this.compressedScopes = new Set(\n      [...scopeSet].filter((x) => !impliedSet.has(x)),\n    );\n    this.expandedScopes = new Set([...scopeSet, ...impliedSet]);\n    this.originalScopes = scopeSet;\n  }\n\n  /**\n   * Checks whether the current set of scopes includes the given one.\n   */\n  public has(scope: string | string[] | AuthScopes | undefined) {\n    let other: AuthScopes;\n\n    if (scope instanceof AuthScopes) {\n      other = scope;\n    } else {\n      other = new AuthScopes(scope);\n    }\n\n    return (\n      other.toArray().filter((x) => !this.expandedScopes.has(x)).length === 0\n    );\n  }\n\n  /**\n   * Checks whether the current set of scopes equals the given one.\n   */\n  public equals(otherScopes: string | string[] | AuthScopes | undefined) {\n    let other: AuthScopes;\n\n    if (otherScopes instanceof AuthScopes) {\n      other = otherScopes;\n    } else {\n      other = new AuthScopes(otherScopes);\n    }\n\n    return (\n      this.compressedScopes.size === other.compressedScopes.size &&\n      this.toArray().filter((x) => !other.has(x)).length === 0\n    );\n  }\n\n  /**\n   * Returns a comma-separated string with the current set of scopes.\n   */\n  public toString() {\n    return this.toArray().join(AuthScopes.SCOPE_DELIMITER);\n  }\n\n  /**\n   * Returns an array with the current set of scopes.\n   */\n  public toArray(returnOriginalScopes = false) {\n    return returnOriginalScopes\n      ? [...this.originalScopes]\n      : [...this.compressedScopes];\n  }\n\n  private getImpliedScopes(scopesArray: string[]): string[] {\n    return scopesArray.reduce((array: string[], current: string) => {\n      const matches = current.match(/^(unauthenticated_)?write_(.*)$/);\n      if (matches) {\n        array.push(`${matches[1] ? matches[1] : ''}read_${matches[2]}`);\n      }\n\n      return array;\n    }, []);\n  }\n}\n\nexport {AuthScopes};\n"], "names": [], "mappings": ";;AAAA;;AAEG;AACH,MAAM,UAAU,CAAA;AACP,IAAA,OAAO,eAAe,GAAG,GAAG;AAE3B,IAAA,gBAAgB;AAChB,IAAA,cAAc;AACd,IAAA,cAAc;AAEtB,IAAA,WAAA,CAAY,MAAkD,EAAA;QAC5D,IAAI,WAAW,GAAa,EAAE;AAC9B,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,YAAA,WAAW,GAAG,MAAM,CAAC,KAAK,CACxB,IAAI,MAAM,CAAC,CAAA,EAAG,UAAU,CAAC,eAAe,CAAA,IAAA,CAAM,CAAC,CAChD;QACH;AAAO,aAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAChC,WAAW,GAAG,MAAM;QACtB;aAAO,IAAI,MAAM,EAAE;YACjB,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QACjD;AAEA,QAAA,WAAW,GAAG;aACX,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;aAC3B,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,CAAC;QAElC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;AAExD,QAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC;AACrC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC;QAEzC,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,CAC7B,CAAC,GAAG,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChD;AACD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,EAAE,GAAG,UAAU,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ;IAChC;AAEA;;AAEG;AACI,IAAA,GAAG,CAAC,KAAiD,EAAA;AAC1D,QAAA,IAAI,KAAiB;AAErB,QAAA,IAAI,KAAK,YAAY,UAAU,EAAE;YAC/B,KAAK,GAAG,KAAK;QACf;aAAO;AACL,YAAA,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC;QAC/B;AAEA,QAAA,QACE,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC;IAE3E;AAEA;;AAEG;AACI,IAAA,MAAM,CAAC,WAAuD,EAAA;AACnE,QAAA,IAAI,KAAiB;AAErB,QAAA,IAAI,WAAW,YAAY,UAAU,EAAE;YACrC,KAAK,GAAG,WAAW;QACrB;aAAO;AACL,YAAA,KAAK,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC;QACrC;QAEA,QACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,KAAK,CAAC,gBAAgB,CAAC,IAAI;YAC1D,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC;IAE5D;AAEA;;AAEG;IACI,QAAQ,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;IACxD;AAEA;;AAEG;IACI,OAAO,CAAC,oBAAoB,GAAG,KAAK,EAAA;AACzC,QAAA,OAAO;AACL,cAAE,CAAC,GAAG,IAAI,CAAC,cAAc;AACzB,cAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;IAChC;AAEQ,IAAA,gBAAgB,CAAC,WAAqB,EAAA;QAC5C,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,KAAe,EAAE,OAAe,KAAI;YAC7D,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,iCAAiC,CAAC;YAChE,IAAI,OAAO,EAAE;gBACX,KAAK,CAAC,IAAI,CAAC,CAAA,EAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA,KAAA,EAAQ,OAAO,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC;YACjE;AAEA,YAAA,OAAO,KAAK;QACd,CAAC,EAAE,EAAE,CAAC;IACR;;;;;"}
export declare const CLIENT = "GraphQL Client";
export declare const MIN_RETRIES = 0;
export declare const MAX_RETRIES = 3;
export declare const GQL_API_ERROR = "An error occurred while fetching from the API. Review 'graphQLErrors' for details.";
export declare const UNEXPECTED_CONTENT_TYPE_ERROR = "Response returned unexpected Content-Type:";
export declare const NO_DATA_OR_ERRORS_ERROR = "An unknown error has occurred. The API did not return a data object or any errors in its response.";
export declare const CONTENT_TYPES: {
    json: string;
    multipart: string;
};
export declare const SDK_VARIANT_HEADER = "X-SDK-Variant";
export declare const SDK_VERSION_HEADER = "X-SDK-Version";
export declare const DEFAULT_SDK_VARIANT = "shopify-graphql-client";
export declare const DEFAULT_CLIENT_VERSION = "ROLLUP_REPLACE_CLIENT_VERSION";
export declare const RETRY_WAIT_TIME = 1000;
export declare const RETRIABLE_STATUS_CODES: number[];
export declare const DEFER_OPERATION_REGEX: RegExp;
export declare const NEWLINE_SEPARATOR = "\r\n";
export declare const BOUNDARY_HEADER_REGEX: RegExp;
export declare const HEADER_SEPARATOR: string;
//# sourceMappingURL=constants.d.ts.map
import { useState, useEffect } from 'react'
import { api } from '../../services/api'
import { CheckCircle, AlertCircle, FileText, Clock, Camera } from 'lucide-react'
import styles from './ReturnPolicySettings.module.css'

interface ReturnPolicyData {
  returnWindow: number
  requirePhotos: boolean
  autoApprove: boolean
  returnReasons: string[]
  returnPolicy: string
  widgetBrandingColor: string
}

export const ReturnPolicySettings = () => {
  const [settings, setSettings] = useState<ReturnPolicyData>({
    returnWindow: 30,
    requirePhotos: false,
    autoApprove: true,
    returnReasons: ['Defective item', 'Wrong size', 'Changed mind', 'Not as described'],
    returnPolicy: '',
    widgetBrandingColor: '#3b82f6'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [newReason, setNewReason] = useState('')

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      const data = await api.getSettings()
      if (data) {
        setSettings(prev => ({ ...prev, ...data }))
      }
    } catch (err) {
      console.log('Settings not found, using defaults')
    }
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      await api.updateSettings(settings)
      setSuccess('Return policy settings saved successfully!')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings')
    } finally {
      setLoading(false)
    }
  }

  const addReturnReason = () => {
    if (newReason.trim() && !settings.returnReasons.includes(newReason.trim())) {
      setSettings(prev => ({
        ...prev,
        returnReasons: [...prev.returnReasons, newReason.trim()]
      }))
      setNewReason('')
    }
  }

  const removeReturnReason = (reason: string) => {
    setSettings(prev => ({
      ...prev,
      returnReasons: prev.returnReasons.filter(r => r !== reason)
    }))
  }

  const updateSetting = (key: keyof ReturnPolicyData, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Return Policy Settings</h2>
        <p>Configure your return policies and widget appearance.</p>
      </div>

      <form onSubmit={handleSave} className={styles.form}>
        <div className={styles.section}>
          <h3>
            <Clock size={20} />
            Return Window
          </h3>
          <div className={styles.field}>
            <label htmlFor="returnWindow">Return window (days)</label>
            <input
              id="returnWindow"
              type="number"
              min="1"
              max="365"
              value={settings.returnWindow}
              onChange={(e) => updateSetting('returnWindow', parseInt(e.target.value))}
              className={styles.input}
              disabled={loading}
            />
            <small className={styles.hint}>
              Number of days customers have to initiate a return
            </small>
          </div>
        </div>

        <div className={styles.section}>
          <h3>
            <FileText size={20} />
            Return Options
          </h3>
          
          <div className={styles.checkboxField}>
            <input
              id="autoApprove"
              type="checkbox"
              checked={settings.autoApprove}
              onChange={(e) => updateSetting('autoApprove', e.target.checked)}
              disabled={loading}
            />
            <label htmlFor="autoApprove">
              Auto-approve returns
              <small>Automatically approve return requests without manual review</small>
            </label>
          </div>

          <div className={styles.checkboxField}>
            <input
              id="requirePhotos"
              type="checkbox"
              checked={settings.requirePhotos}
              onChange={(e) => updateSetting('requirePhotos', e.target.checked)}
              disabled={loading}
            />
            <label htmlFor="requirePhotos">
              <Camera size={16} className={styles.inline} />
              Require photos for returns
              <small>Customers must upload photos of items when requesting returns</small>
            </label>
          </div>
        </div>

        <div className={styles.section}>
          <h3>Return Reasons</h3>
          <div className={styles.reasonsList}>
            {settings.returnReasons.map((reason, index) => (
              <div key={index} className={styles.reasonItem}>
                <span>{reason}</span>
                <button
                  type="button"
                  onClick={() => removeReturnReason(reason)}
                  className={styles.removeButton}
                  disabled={loading}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
          
          <div className={styles.addReason}>
            <input
              type="text"
              value={newReason}
              onChange={(e) => setNewReason(e.target.value)}
              placeholder="Add new return reason..."
              className={styles.input}
              disabled={loading}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addReturnReason())}
            />
            <button
              type="button"
              onClick={addReturnReason}
              disabled={!newReason.trim() || loading}
              className={styles.addButton}
            >
              Add
            </button>
          </div>
        </div>

        <div className={styles.section}>
          <h3>Widget Branding</h3>
          <div className={styles.field}>
            <label htmlFor="brandingColor">Primary color</label>
            <div className={styles.colorField}>
              <input
                id="brandingColor"
                type="color"
                value={settings.widgetBrandingColor}
                onChange={(e) => updateSetting('widgetBrandingColor', e.target.value)}
                className={styles.colorInput}
                disabled={loading}
              />
              <input
                type="text"
                value={settings.widgetBrandingColor}
                onChange={(e) => updateSetting('widgetBrandingColor', e.target.value)}
                className={styles.input}
                disabled={loading}
                placeholder="#3b82f6"
              />
            </div>
            <small className={styles.hint}>
              This color will be used for buttons and accents in the returns widget
            </small>
          </div>
        </div>

        <div className={styles.section}>
          <h3>Return Policy Text</h3>
          <div className={styles.field}>
            <label htmlFor="returnPolicy">Policy description (optional)</label>
            <textarea
              id="returnPolicy"
              value={settings.returnPolicy}
              onChange={(e) => updateSetting('returnPolicy', e.target.value)}
              placeholder="Enter your return policy details that will be shown to customers..."
              rows={6}
              className={styles.textarea}
              disabled={loading}
            />
            <small className={styles.hint}>
              This text will be displayed to customers in the returns widget
            </small>
          </div>
        </div>

        {error && (
          <div className={styles.errorMessage}>
            <AlertCircle size={16} />
            {error}
          </div>
        )}

        {success && (
          <div className={styles.successMessage}>
            <CheckCircle size={16} />
            {success}
          </div>
        )}

        <div className={styles.actions}>
          <button
            type="submit"
            disabled={loading}
            className={styles.saveButton}
          >
            {loading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  )
}

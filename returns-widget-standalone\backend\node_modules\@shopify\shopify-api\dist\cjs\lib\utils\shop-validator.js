'use strict';

var error = require('../error.js');
var decodeHost = require('../auth/decode-host.js');
var shopAdminUrlHelper = require('./shop-admin-url-helper.js');

function sanitizeShop(config) {
    return (shop, throwOnInvalid = false) => {
        let shopUrl = shop;
        const domainsRegex = [
            'myshopify\\.com',
            'shopify\\.com',
            'myshopify\\.io',
            'shop\\.dev',
        ];
        if (config.customShopDomains) {
            domainsRegex.push(...config.customShopDomains.map((regex) => typeof regex === 'string' ? regex : regex.source));
        }
        const shopUrlRegex = new RegExp(`^[a-zA-Z0-9][a-zA-Z0-9-_]*\\.(${domainsRegex.join('|')})[/]*$`);
        const shopAdminRegex = new RegExp(`^admin\\.(${domainsRegex.join('|')})/store/([a-zA-Z0-9][a-zA-Z0-9-_]*)$`);
        const isShopAdminUrl = shopAdminRegex.test(shopUrl);
        if (isShopAdminUrl) {
            shopUrl = shopAdminUrlHelper.shopAdminUrlToLegacyUrl(shopUrl) || '';
        }
        const sanitizedShop = shopUrlRegex.test(shopUrl) ? shopUrl : null;
        if (!sanitizedShop && throwOnInvalid) {
            throw new error.InvalidShopError('Received invalid shop argument');
        }
        return sanitizedShop;
    };
}
function sanitizeHost() {
    return (host, throwOnInvalid = false) => {
        const base64regex = /^[0-9a-zA-Z+/]+={0,2}$/;
        let sanitizedHost = base64regex.test(host) ? host : null;
        if (sanitizedHost) {
            const { hostname } = new URL(`https://${decodeHost.decodeHost(sanitizedHost)}`);
            const originsRegex = [
                'myshopify\\.com',
                'shopify\\.com',
                'myshopify\\.io',
                'spin\\.dev',
                'shop\\.dev',
            ];
            const hostRegex = new RegExp(`\\.(${originsRegex.join('|')})$`);
            if (!hostRegex.test(hostname)) {
                sanitizedHost = null;
            }
        }
        if (!sanitizedHost && throwOnInvalid) {
            throw new error.InvalidHostError('Received invalid host argument');
        }
        return sanitizedHost;
    };
}

exports.sanitizeHost = sanitizeHost;
exports.sanitizeShop = sanitizeShop;
//# sourceMappingURL=shop-validator.js.map

import { GraphQLClient } from '../../types';
type ClientFunctionNames = keyof Omit<GraphQLClient, 'config'>;
export declare const fetchApiTests: (functionName: ClientFunctionNames, gqlOperation?: string) => void;
export declare const parametersTests: (functionName: ClientFunctionNames, gqlOperation?: string) => void;
export declare const sdkHeadersTests: (functionName: ClientFunctionNames, gqlOperation?: string) => void;
export declare const retryTests: (functionName: ClientFunctionNames, gqlOperation?: string) => void;
export {};
//# sourceMappingURL=common-tests.d.ts.map
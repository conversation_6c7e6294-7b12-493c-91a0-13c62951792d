{"version": 3, "file": "create-session.js", "sources": ["../../../../../../../lib/auth/oauth/create-session.ts"], "sourcesContent": ["import {v4 as uuidv4} from 'uuid';\n\nimport {Session} from '../../session/session';\nimport {ConfigInterface} from '../../base-types';\nimport {logger} from '../../logger';\nimport {getJwtSessionId, getOfflineId} from '../../session/session-utils';\n\nimport {\n  AccessTokenResponse,\n  OnlineAccessResponse,\n  OnlineAccessInfo,\n  OfflineAccessResponse,\n} from './types';\n\nexport function createSession({\n  config,\n  accessTokenResponse,\n  shop,\n  state,\n}: {\n  config: ConfigInterface;\n  accessTokenResponse: AccessTokenResponse;\n  shop: string;\n  state: string;\n}): Session {\n  const associatedUser = (accessTokenResponse as OnlineAccessResponse)\n    .associated_user;\n  const isOnline = Boolean(associatedUser);\n\n  logger(config).info('Creating new session', {shop, isOnline});\n\n  const getSessionExpiration = (expires_in: number) =>\n    new Date(Date.now() + expires_in * 1000);\n\n  const getOnlineSessionProperties = (responseBody: OnlineAccessResponse) => {\n    const {access_token, scope, ...rest} = responseBody;\n    const sessionId = config.isEmbeddedApp\n      ? getJwtSessionId(config)(\n          shop,\n          `${(rest as OnlineAccessInfo).associated_user.id}`,\n        )\n      : uuidv4();\n\n    return {\n      id: sessionId,\n      onlineAccessInfo: rest,\n      expires: getSessionExpiration(rest.expires_in),\n    };\n  };\n\n  const getOfflineSessionProperties = (responseBody: OfflineAccessResponse) => {\n    const {expires_in} = responseBody;\n    return {\n      id: getOfflineId(config)(shop),\n      ...(expires_in && {expires: getSessionExpiration(expires_in)}),\n    };\n  };\n\n  return new Session({\n    shop,\n    state,\n    isOnline,\n    accessToken: accessTokenResponse.access_token,\n    scope: accessTokenResponse.scope,\n    ...(isOnline\n      ? getOnlineSessionProperties(accessTokenResponse as OnlineAccessResponse)\n      : getOfflineSessionProperties(\n          accessTokenResponse as OfflineAccessResponse,\n        )),\n  });\n}\n"], "names": ["logger", "getJwtSessionId", "uuidv4", "getOfflineId", "Session"], "mappings": ";;;;;;;AAcM,SAAU,aAAa,CAAC,EAC5B,MAAM,EACN,mBAAmB,EACnB,IAAI,EACJ,KAAK,GAMN,EAAA;IACC,MAAM,cAAc,GAAI;AACrB,SAAA,eAAe;AAClB,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;AAExC,IAAAA,YAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC,CAAC;AAE7D,IAAA,MAAM,oBAAoB,GAAG,CAAC,UAAkB,KAC9C,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,IAAI,CAAC;AAE1C,IAAA,MAAM,0BAA0B,GAAG,CAAC,YAAkC,KAAI;QACxE,MAAM,EAAC,YAAY,EAAE,KAAK,EAAE,GAAG,IAAI,EAAC,GAAG,YAAY;AACnD,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC;AACvB,cAAEC,4BAAe,CAAC,MAAM,CAAC,CACrB,IAAI,EACJ,CAAA,EAAI,IAAyB,CAAC,eAAe,CAAC,EAAE,EAAE;cAEpDC,OAAM,EAAE;QAEZ,OAAO;AACL,YAAA,EAAE,EAAE,SAAS;AACb,YAAA,gBAAgB,EAAE,IAAI;AACtB,YAAA,OAAO,EAAE,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC;SAC/C;AACH,IAAA,CAAC;AAED,IAAA,MAAM,2BAA2B,GAAG,CAAC,YAAmC,KAAI;AAC1E,QAAA,MAAM,EAAC,UAAU,EAAC,GAAG,YAAY;QACjC,OAAO;AACL,YAAA,EAAE,EAAEC,yBAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;YAC9B,IAAI,UAAU,IAAI,EAAC,OAAO,EAAE,oBAAoB,CAAC,UAAU,CAAC,EAAC,CAAC;SAC/D;AACH,IAAA,CAAC;IAED,OAAO,IAAIC,eAAO,CAAC;QACjB,IAAI;QACJ,KAAK;QACL,QAAQ;QACR,WAAW,EAAE,mBAAmB,CAAC,YAAY;QAC7C,KAAK,EAAE,mBAAmB,CAAC,KAAK;AAChC,QAAA,IAAI;AACF,cAAE,0BAA0B,CAAC,mBAA2C;AACxE,cAAE,2BAA2B,CACzB,mBAA4C,CAC7C,CAAC;AACP,KAAA,CAAC;AACJ;;;;"}
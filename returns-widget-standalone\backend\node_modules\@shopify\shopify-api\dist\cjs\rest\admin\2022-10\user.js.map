{"version": 3, "file": "user.js", "sources": ["../../../../../../../rest/admin/2022-10/user.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  limit?: unknown;\n  page_info?: unknown;\n}\ninterface CurrentArgs {\n  [key: string]: unknown;\n  session: Session;\n}\n\nexport class User extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"current\", \"ids\": [], \"path\": \"users/current.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"users.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"users/<id>.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"user\",\n      \"plural\": \"users\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id\n    }: FindArgs\n  ): Promise<User | null> {\n    const result = await this.baseFind<User>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async all(\n    {\n      session,\n      limit = null,\n      page_info = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<User>> {\n    const response = await this.baseFind<User>({\n      session: session,\n      urlIds: {},\n      params: {\"limit\": limit, \"page_info\": page_info, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async current(\n    {\n      session,\n      ...otherArgs\n    }: CurrentArgs\n  ): Promise<unknown> {\n    const response = await this.request<User>({\n      http_method: \"get\",\n      operation: \"current\",\n      session: session,\n      urlIds: {},\n      params: {...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public account_owner: boolean | null;\n  public bio: string | null;\n  public email: string | null;\n  public first_name: string | null;\n  public id: number | null;\n  public im: string | null;\n  public last_name: string | null;\n  public locale: string | null;\n  public permissions: string[] | null;\n  public phone: string | null;\n  public receive_announcements: number | null;\n  public screen_name: string | null;\n  public url: string | null;\n  public user_type: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAsBlH,MAAO,IAAK,SAAQA,SAAI,CAAA;AACrB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,oBAAoB,EAAC;AACvF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,YAAY,EAAC;AAC3E,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,iBAAiB;KACpF;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,MAAM;AAClB,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACO,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAO;AACvC,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,KAAK,GAAG,IAAI,EACZ,SAAS,GAAG,IAAI,EAChB,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAO;AACzC,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,SAAS,EAAC;AAC/D,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;IAEO,aAAa,OAAO,CACzB,EACE,OAAO,EACP,GAAG,SAAS,EACA,EAAA;AAEd,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAO;AACxC,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACtB,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,aAAa;AACb,IAAA,GAAG;AACH,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,EAAE;AACF,IAAA,EAAE;AACF,IAAA,SAAS;AACT,IAAA,MAAM;AACN,IAAA,WAAW;AACX,IAAA,KAAK;AACL,IAAA,qBAAqB;AACrB,IAAA,WAAW;AACX,IAAA,GAAG;AACH,IAAA,SAAS;;;;;"}
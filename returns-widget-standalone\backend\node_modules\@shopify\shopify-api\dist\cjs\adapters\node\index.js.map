{"version": 3, "file": "index.js", "sources": ["../../../../../../adapters/node/index.ts"], "sourcesContent": ["import crypto from 'crypto';\n\nimport fetch from 'node-fetch';\n\nimport {\n  setAbstractFetchFunc,\n  setAbstractConvertRequestFunc,\n  setAbstractConvertIncomingResponseFunc,\n  setAbstractConvertResponseFunc,\n  setAbstractConvertHeadersFunc,\n  setAbstractRuntimeString,\n  setCrypto,\n  AbstractFetchFunc,\n} from '../../runtime';\n\nimport {\n  nodeConvertRequest,\n  nodeConvertIncomingResponse,\n  nodeConvertAndSendResponse,\n  nodeConvertAndSetHeaders,\n  nodeRuntimeString,\n} from './adapter';\n\n// For the purposes of this package, fetch correctly implements everything we need\nsetAbstractFetchFunc(fetch as any as AbstractFetchFunc);\nsetAbstractConvertRequestFunc(nodeConvertRequest);\nsetAbstractConvertIncomingResponseFunc(nodeConvertIncomingResponse);\nsetAbstractConvertResponseFunc(nodeConvertAndSendResponse);\nsetAbstractConvertHeadersFunc(nodeConvertAndSetHeaders);\nsetAbstractRuntimeString(nodeRuntimeString);\nsetCrypto(crypto as any);\n"], "names": ["setAbstractFetchFunc", "setAbstractConvertRequestFunc", "nodeConvertRequest", "setAbstractConvertIncomingResponseFunc", "nodeConvertIncomingResponse", "setAbstractConvertResponseFunc", "nodeConvertAndSendResponse", "setAbstractConvertHeadersFunc", "nodeConvertAndSetHeaders", "setAbstractRuntimeString", "nodeRuntimeString", "setCrypto", "crypto"], "mappings": ";;;;;;;;;;AAuBA;AACAA,0BAAoB,CAAC,KAAiC,CAAC;AACvDC,mCAA6B,CAACC,0BAAkB,CAAC;AACjDC,4CAAsC,CAACC,mCAA2B,CAAC;AACnEC,oCAA8B,CAACC,kCAA0B,CAAC;AAC1DC,mCAA6B,CAACC,gCAAwB,CAAC;AACvDC,sCAAwB,CAACC,yBAAiB,CAAC;AAC3CC,gBAAS,CAACC,QAAa,CAAC;;"}
'use strict';

var index = require('../logger/index.js');
var types = require('../types.js');
var index$1 = require('../../runtime/http/index.js');
require('../../runtime/crypto/types.js');
require('../../runtime/crypto/crypto.js');

function fetchRequestFactory(config) {
    return async function fetchRequest(url, options) {
        const log = index.logger(config);
        const doLog = config.logger.httpRequests && config.logger.level === types.LogSeverity.Debug;
        if (doLog) {
            log.debug('Making HTTP request', {
                method: options?.method || 'GET',
                url,
                ...(options?.body && { body: options?.body }),
            });
        }
        const response = await index$1.abstractFetch(url, options);
        if (doLog) {
            log.debug('HTTP request completed', {
                method: options?.method || 'GET',
                url,
                status: response.status,
            });
        }
        return response;
    };
}

exports.fetchRequestFactory = fetchRequestFactory;
//# sourceMappingURL=fetch-request.js.map

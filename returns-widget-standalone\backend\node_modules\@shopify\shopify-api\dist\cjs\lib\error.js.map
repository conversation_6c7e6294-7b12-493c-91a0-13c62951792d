{"version": 3, "file": "error.js", "sources": ["../../../../../lib/error.ts"], "sourcesContent": ["import {AdapterResponse} from '../runtime/http/types';\n\nexport class ShopifyError extends Error {\n  constructor(message?: string) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class InvalidHmacError extends ShopifyError {}\nexport class InvalidShopError extends ShopifyError {}\nexport class InvalidHostError extends ShopifyError {}\nexport class InvalidJwtError extends ShopifyError {}\nexport class MissingJwtTokenError extends ShopifyError {}\nexport class InvalidDeliveryMethodError extends ShopifyError {}\n\nexport class SafeCompareError extends ShopifyError {}\nexport class PrivateAppError extends ShopifyError {}\n\nexport class HttpRequestError extends ShopifyError {}\nexport class HttpMaxRetriesError extends ShopifyError {}\n\ninterface HttpResponseData {\n  code: number;\n  statusText: string;\n  body?: Record<string, unknown>;\n  headers?: Record<string, unknown>;\n}\n\ninterface HttpResponseErrorParams extends HttpResponseData {\n  message: string;\n}\nexport class HttpResponseError<\n  ResponseType extends HttpResponseData = HttpResponseData,\n> extends ShopifyError {\n  readonly response: ResponseType;\n\n  constructor({\n    message,\n    code,\n    statusText,\n    body,\n    headers,\n  }: HttpResponseErrorParams) {\n    super(message);\n    this.response = {\n      code,\n      statusText,\n      body,\n      headers,\n    } as ResponseType;\n  }\n}\nexport class HttpRetriableError<\n  ResponseType extends HttpResponseData = HttpResponseData,\n> extends HttpResponseError<ResponseType> {}\nexport class HttpInternalError extends HttpRetriableError {}\n\ninterface HttpThrottlingErrorData extends HttpResponseData {\n  retryAfter?: number;\n}\ninterface HttpThrottlingErrorParams extends HttpThrottlingErrorData {\n  message: string;\n}\nexport class HttpThrottlingError extends HttpRetriableError<HttpThrottlingErrorData> {\n  constructor({retryAfter, ...params}: HttpThrottlingErrorParams) {\n    super(params);\n    this.response.retryAfter = retryAfter;\n  }\n}\n\nexport class RestResourceError extends ShopifyError {}\n\ninterface GraphqlQueryErrorParams {\n  message: string;\n  response: Record<string, unknown>;\n  headers?: Record<string, unknown>;\n  body?: Record<string, any>;\n}\n\nexport class GraphqlQueryError extends ShopifyError {\n  readonly response: Record<string, unknown>;\n  readonly headers?: Record<string, unknown>;\n  readonly body?: Record<string, any>;\n\n  constructor({message, response, headers, body}: GraphqlQueryErrorParams) {\n    super(message);\n    this.response = response;\n    this.headers = headers;\n    this.body = body;\n  }\n}\n\nexport class InvalidOAuthError extends ShopifyError {}\nexport class BotActivityDetected extends ShopifyError {}\nexport class CookieNotFound extends ShopifyError {}\nexport class InvalidSession extends ShopifyError {}\n\ninterface InvalidWebhookParams {\n  message: string;\n  response: AdapterResponse;\n}\nexport class InvalidWebhookError extends ShopifyError {\n  readonly response: AdapterResponse;\n\n  constructor({message, response}: InvalidWebhookParams) {\n    super(message);\n    this.response = response;\n  }\n}\nexport class MissingWebhookCallbackError extends InvalidWebhookError {}\nexport class SessionStorageError extends ShopifyError {}\nexport class MissingRequiredArgument extends ShopifyError {}\nexport class UnsupportedClientType extends ShopifyError {}\nexport class InvalidRequestError extends ShopifyError {}\ninterface BillingErrorParams {\n  message: string;\n  errorData: any;\n}\nexport class BillingError extends ShopifyError {\n  readonly errorData: any;\n\n  constructor({message, errorData}: BillingErrorParams) {\n    super(message);\n    this.errorData = errorData;\n  }\n}\nexport class FeatureDeprecatedError extends ShopifyError {}\n"], "names": [], "mappings": ";;AAEM,MAAO,YAAa,SAAQ,KAAK,CAAA;AACrC,IAAA,WAAA,CAAY,OAAgB,EAAA;QAC1B,KAAK,CAAC,OAAO,CAAC;QACd,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;IACnD;AACD;AAEK,MAAO,gBAAiB,SAAQ,YAAY,CAAA;AAAG;AAC/C,MAAO,gBAAiB,SAAQ,YAAY,CAAA;AAAG;AAC/C,MAAO,gBAAiB,SAAQ,YAAY,CAAA;AAAG;AAC/C,MAAO,eAAgB,SAAQ,YAAY,CAAA;AAAG;AAC9C,MAAO,oBAAqB,SAAQ,YAAY,CAAA;AAAG;AACnD,MAAO,0BAA2B,SAAQ,YAAY,CAAA;AAAG;AAEzD,MAAO,gBAAiB,SAAQ,YAAY,CAAA;AAAG;AAC/C,MAAO,eAAgB,SAAQ,YAAY,CAAA;AAAG;AAE9C,MAAO,gBAAiB,SAAQ,YAAY,CAAA;AAAG;AAC/C,MAAO,mBAAoB,SAAQ,YAAY,CAAA;AAAG;AAYlD,MAAO,iBAEX,SAAQ,YAAY,CAAA;AACX,IAAA,QAAQ;IAEjB,WAAA,CAAY,EACV,OAAO,EACP,IAAI,EACJ,UAAU,EACV,IAAI,EACJ,OAAO,GACiB,EAAA;QACxB,KAAK,CAAC,OAAO,CAAC;QACd,IAAI,CAAC,QAAQ,GAAG;YACd,IAAI;YACJ,UAAU;YACV,IAAI;YACJ,OAAO;SACQ;IACnB;AACD;AACK,MAAO,kBAEX,SAAQ,iBAA+B,CAAA;AAAG;AACtC,MAAO,iBAAkB,SAAQ,kBAAkB,CAAA;AAAG;AAQtD,MAAO,mBAAoB,SAAQ,kBAA2C,CAAA;AAClF,IAAA,WAAA,CAAY,EAAC,UAAU,EAAE,GAAG,MAAM,EAA4B,EAAA;QAC5D,KAAK,CAAC,MAAM,CAAC;AACb,QAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAU;IACvC;AACD;AAEK,MAAO,iBAAkB,SAAQ,YAAY,CAAA;AAAG;AAShD,MAAO,iBAAkB,SAAQ,YAAY,CAAA;AACxC,IAAA,QAAQ;AACR,IAAA,OAAO;AACP,IAAA,IAAI;IAEb,WAAA,CAAY,EAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAA0B,EAAA;QACrE,KAAK,CAAC,OAAO,CAAC;AACd,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;AACtB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;IAClB;AACD;AAEK,MAAO,iBAAkB,SAAQ,YAAY,CAAA;AAAG;AAChD,MAAO,mBAAoB,SAAQ,YAAY,CAAA;AAAG;AAClD,MAAO,cAAe,SAAQ,YAAY,CAAA;AAAG;AAC7C,MAAO,cAAe,SAAQ,YAAY,CAAA;AAAG;AAM7C,MAAO,mBAAoB,SAAQ,YAAY,CAAA;AAC1C,IAAA,QAAQ;AAEjB,IAAA,WAAA,CAAY,EAAC,OAAO,EAAE,QAAQ,EAAuB,EAAA;QACnD,KAAK,CAAC,OAAO,CAAC;AACd,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;IAC1B;AACD;AACK,MAAO,2BAA4B,SAAQ,mBAAmB,CAAA;AAAG;AACjE,MAAO,mBAAoB,SAAQ,YAAY,CAAA;AAAG;AAClD,MAAO,uBAAwB,SAAQ,YAAY,CAAA;AAAG;AACtD,MAAO,qBAAsB,SAAQ,YAAY,CAAA;AAAG;AACpD,MAAO,mBAAoB,SAAQ,YAAY,CAAA;AAAG;AAKlD,MAAO,YAAa,SAAQ,YAAY,CAAA;AACnC,IAAA,SAAS;AAElB,IAAA,WAAA,CAAY,EAAC,OAAO,EAAE,SAAS,EAAqB,EAAA;QAClD,KAAK,CAAC,OAAO,CAAC;AACd,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;IAC5B;AACD;AACK,MAAO,sBAAuB,SAAQ,YAAY,CAAA;AAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
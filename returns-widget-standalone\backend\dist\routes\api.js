"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const database_1 = require("../models/database");
const auth_1 = require("../middleware/auth");
const shopifyService_1 = require("../services/shopifyService");
const sendcloudService_1 = require("../services/sendcloudService");
const utils_1 = require("../lib/utils");
const router = express_1.default.Router();
// Public routes
router.post('/auth/register', async (req, res) => {
    try {
        console.log('Registration request received:', req.body);
        const { email, password, storeName, shopifyDomain } = req.body;
        if (!email || !password || !storeName || !shopifyDomain) {
            console.log('Missing required fields');
            return res.status(400).json({
                success: false,
                error: 'All fields are required'
            });
        }
        console.log('Checking if merchant exists...');
        // Check if merchant already exists
        const existingMerchant = database_1.db.getMerchantByEmail(email);
        if (existingMerchant) {
            console.log('Merchant already exists:', email);
            return res.status(400).json({
                success: false,
                error: 'Merchant already exists'
            });
        }
        console.log('Creating new merchant...');
        const merchantId = await database_1.db.createMerchant({
            email,
            password,
            storeName,
            shopifyDomain,
            shopifyApiKey: '',
            shopifyApiSecret: '',
            sendcloudApiKey: '',
            sendcloudApiSecret: '',
            isActive: true,
            plan: 'free'
        });
        console.log('Merchant created with ID:', merchantId);
        const token = (0, auth_1.generateToken)(merchantId);
        res.status(201).json({
            success: true,
            data: {
                token,
                merchant: {
                    id: merchantId,
                    email,
                    storeName,
                    shopifyDomain,
                    plan: 'free'
                }
            }
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            error: 'Registration failed'
        });
    }
});
router.post('/auth/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        const merchant = database_1.db.getMerchantByEmail(email);
        if (!merchant) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        const isValidPassword = await bcryptjs_1.default.compare(password, merchant.password);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        if (!merchant.isActive) {
            return res.status(401).json({ error: 'Account is inactive' });
        }
        const token = (0, auth_1.generateToken)(merchant.id);
        res.json({
            success: true,
            data: {
                token,
                merchant: {
                    id: merchant.id,
                    email: merchant.email,
                    storeName: merchant.storeName,
                    shopifyDomain: merchant.shopifyDomain,
                    plan: merchant.plan,
                    hasShopifyCredentials: !!(merchant.shopifyApiKey && merchant.shopifyApiSecret),
                    hasSendcloudCredentials: !!(merchant.sendcloudApiKey && merchant.sendcloudApiSecret)
                }
            }
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});
// Protected routes (require authentication)
router.use(auth_1.authenticateToken);
router.use(auth_1.loadMerchant);
router.post('/credentials/shopify', async (req, res) => {
    try {
        const { accessToken } = req.body;
        if (!accessToken) {
            return res.status(400).json({
                success: false,
                error: 'Access token is required'
            });
        }
        // Test the Shopify connection
        const shopifyService = new shopifyService_1.ShopifyService(req.merchant.shopifyDomain, accessToken);
        const isValid = await shopifyService.testConnection();
        if (!isValid) {
            return res.status(400).json({
                success: false,
                error: 'Invalid Shopify access token'
            });
        }
        // Save credentials
        database_1.db.updateMerchantCredentials(req.merchantId, {
            shopifyApiKey: accessToken,
            shopifyApiSecret: accessToken // Using access token for both
        });
        res.json({
            success: true,
            data: { message: 'Shopify credentials saved successfully' }
        });
    }
    catch (error) {
        console.error('Shopify credentials error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to save Shopify credentials'
        });
    }
});
// Test Shopify connection endpoint
router.post('/credentials/shopify/test', async (req, res) => {
    try {
        const merchant = await database_1.db.getMerchantById(req.merchantId);
        if (!merchant?.shopifyApiSecret) {
            return res.status(400).json({
                success: false,
                error: 'No Shopify credentials found'
            });
        }
        const shopifyService = new shopifyService_1.ShopifyService(merchant.shopifyDomain, merchant.shopifyApiSecret);
        const shopInfo = await shopifyService.getShopInfo();
        res.json({
            success: true,
            data: {
                connected: true,
                shopName: shopInfo?.name || merchant.shopifyDomain
            }
        });
    }
    catch (error) {
        console.error('Shopify test error:', error);
        res.json({
            success: true,
            data: { connected: false }
        });
    }
});
router.post('/credentials/sendcloud', async (req, res) => {
    try {
        const { publicKey, secretKey } = req.body;
        if (!publicKey || !secretKey) {
            return res.status(400).json({
                success: false,
                error: 'Public and secret keys are required'
            });
        }
        // Test the Sendcloud connection
        const sendcloudService = new sendcloudService_1.SendcloudService(publicKey, secretKey);
        await sendcloudService.getSenderAddresses(); // This will throw if invalid
        // Save credentials
        database_1.db.updateMerchantCredentials(req.merchantId, {
            sendcloudApiKey: publicKey,
            sendcloudApiSecret: secretKey
        });
        res.json({
            success: true,
            data: { message: 'Sendcloud credentials saved successfully' }
        });
    }
    catch (error) {
        console.error('Sendcloud credentials error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to save Sendcloud credentials'
        });
    }
});
// Test Sendcloud connection endpoint
router.post('/credentials/sendcloud/test', async (req, res) => {
    try {
        const merchant = await database_1.db.getMerchantById(req.merchantId);
        if (!merchant?.sendcloudApiKey || !merchant?.sendcloudApiSecret) {
            return res.status(400).json({
                success: false,
                error: 'No Sendcloud credentials found'
            });
        }
        const sendcloudService = new sendcloudService_1.SendcloudService(merchant.sendcloudApiKey, merchant.sendcloudApiSecret);
        const carriers = await sendcloudService.getCarrierOptions();
        res.json({
            success: true,
            data: {
                connected: true,
                carrierCount: carriers.length
            }
        });
    }
    catch (error) {
        console.error('Sendcloud test error:', error);
        res.json({
            success: true,
            data: { connected: false }
        });
    }
});
// Widget API routes (used by the embedded widget)
router.get('/widget/carriers/:merchantId', async (req, res) => {
    try {
        const merchantId = req.params.merchantId;
        const merchant = database_1.db.getMerchantById(merchantId);
        if (!merchant || !merchant.sendcloudApiKey || !merchant.sendcloudApiSecret) {
            return res.status(404).json({ error: 'Merchant not found or Sendcloud not configured' });
        }
        const sendcloudService = new sendcloudService_1.SendcloudService(merchant.sendcloudApiKey, merchant.sendcloudApiSecret);
        const carriers = await sendcloudService.getCarrierOptions();
        res.json(carriers);
    }
    catch (error) {
        console.error('Get carriers error:', error);
        res.status(500).json({ error: 'Failed to fetch carriers' });
    }
});
router.post('/widget/find-order/:merchantId', async (req, res) => {
    try {
        const merchantId = req.params.merchantId;
        const { orderNumber, email } = req.body;
        console.log(`Order lookup request for merchant ${merchantId}, order ${orderNumber}, email ${email}`);
        const merchant = database_1.db.getMerchantById(merchantId);
        if (!merchant) {
            console.error(`Merchant ${merchantId} not found`);
            return res.status(404).json({ error: 'Merchant not found' });
        }
        if (!merchant.shopifyApiKey || !merchant.shopifyApiSecret) {
            console.error(`Merchant ${merchantId} missing Shopify credentials:`, {
                hasApiKey: !!merchant.shopifyApiKey,
                hasApiSecret: !!merchant.shopifyApiSecret,
                domain: merchant.shopifyDomain
            });
            return res.status(404).json({ error: 'Shopify not configured for this merchant' });
        }
        console.log(`Creating Shopify service for domain: ${merchant.shopifyDomain}`);
        const shopifyService = new shopifyService_1.ShopifyService(merchant.shopifyDomain, merchant.shopifyApiSecret);
        console.log(`Searching for order ${orderNumber} with email ${email}`);
        const order = await shopifyService.findOrderByNumber(orderNumber, email);
        if (!order) {
            console.log(`Order ${orderNumber} not found for email ${email}`);
            return res.status(404).json({ error: 'Order not found' });
        }
        console.log(`Order found: ${order.id}`);
        res.json({ order });
    }
    catch (error) {
        console.error('Find order error:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({ error: 'Failed to find order' });
    }
});
router.post('/widget/create-return/:merchantId', async (req, res) => {
    try {
        const merchantId = req.params.merchantId;
        const { orderId, orderNumber, customerEmail, returnItems, carrierId } = req.body;
        const merchant = database_1.db.getMerchantById(merchantId);
        if (!merchant) {
            return res.status(404).json({ error: 'Merchant not found' });
        }
        // Create return in Shopify
        const shopifyService = new shopifyService_1.ShopifyService(merchant.shopifyDomain, merchant.shopifyApiSecret);
        const returnId = await shopifyService.createReturn(orderId, returnItems);
        // Generate return label
        const sendcloudService = new sendcloudService_1.SendcloudService(merchant.sendcloudApiKey, merchant.sendcloudApiSecret);
        // Get customer address from the original order
        const order = await shopifyService.findOrderByNumber(orderNumber, customerEmail);
        const shippingAddress = order.shippingAddress;
        const customerAddress = {
            name: `${shippingAddress.firstName} ${shippingAddress.lastName}`,
            email: customerEmail,
            address: shippingAddress.address1,
            city: shippingAddress.city,
            postal_code: shippingAddress.zip,
            country: shippingAddress.country,
            telephone: shippingAddress.phone || ''
        };
        const returnParcelItems = returnItems.map((item) => ({
            description: item.productTitle,
            quantity: item.quantity,
            weight: '0.5', // Default weight
            value: '25.00', // Default value
            hs_code: '000000',
            origin_country: 'GB'
        }));
        const labelUrl = await sendcloudService.createReturnParcel(customerAddress, returnParcelItems, orderNumber, carrierId);
        // Save return record
        const returnRecordId = database_1.db.createReturn({
            merchant_id: merchantId,
            shopify_order_id: orderId,
            order_number: orderNumber,
            customer_email: customerEmail,
            return_items: returnItems,
            status: 'pending',
            tracking_number: undefined,
            label_url: labelUrl
        });
        res.json({
            success: true,
            returnId: returnRecordId,
            labelUrl
        });
    }
    catch (error) {
        console.error('Create return error:', error);
        res.status(500).json({ error: 'Failed to create return' });
    }
});
// Merchant dashboard routes
router.get('/dashboard/returns', async (req, res) => {
    try {
        const returns = database_1.db.getReturnsByMerchant(req.merchantId);
        res.json({
            success: true,
            data: returns
        });
    }
    catch (error) {
        console.error('Get returns error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch returns'
        });
    }
});
router.get('/dashboard/returns/:returnId', async (req, res) => {
    try {
        const { returnId } = req.params;
        const returnRecord = database_1.db.getReturnById(returnId, req.merchantId);
        if (!returnRecord) {
            return res.status(404).json({
                success: false,
                error: 'Return not found'
            });
        }
        res.json({
            success: true,
            data: returnRecord
        });
    }
    catch (error) {
        console.error('Get return details error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch return details'
        });
    }
});
router.put('/dashboard/returns/:returnId/status', async (req, res) => {
    try {
        const { returnId } = req.params;
        const { status } = req.body;
        if (!status || !['pending', 'approved', 'rejected', 'completed'].includes(status)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid status provided'
            });
        }
        const updated = database_1.db.updateReturnStatus(returnId, req.merchantId, status);
        if (!updated) {
            return res.status(404).json({
                success: false,
                error: 'Return not found'
            });
        }
        res.json({
            success: true,
            data: { message: 'Return status updated successfully' }
        });
    }
    catch (error) {
        console.error('Update return status error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update return status'
        });
    }
});
// Dashboard - Create return from order
router.post('/dashboard/returns/create', async (req, res) => {
    try {
        const { orderId, orderNumber, customerEmail, returnItems, notes } = req.body;
        if (!orderId || !orderNumber || !customerEmail || !returnItems || !Array.isArray(returnItems)) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: orderId, orderNumber, customerEmail, returnItems'
            });
        }
        const merchantId = req.merchantId;
        const merchant = database_1.db.getMerchantById(merchantId);
        if (!merchant?.shopifyApiSecret) {
            return res.status(400).json({
                success: false,
                error: 'Shopify credentials not configured'
            });
        }
        // Create return in our database first
        const returnId = (0, utils_1.generateId)();
        const returnData = {
            merchant_id: merchantId,
            shopify_order_id: orderId,
            order_number: orderNumber,
            customer_email: customerEmail,
            status: 'pending',
            return_items: returnItems,
            notes: notes || ''
        };
        const dbReturn = database_1.db.createReturn(returnData);
        if (!dbReturn) {
            return res.status(500).json({
                success: false,
                error: 'Failed to create return in database'
            });
        }
        // Try to create return in Shopify (optional - may not be supported by all stores)
        try {
            const shopifyService = new shopifyService_1.ShopifyService(merchant.shopifyDomain, merchant.shopifyApiSecret);
            const shopifyReturnId = await shopifyService.createReturn(orderId, returnItems);
            // Note: We'd need to add a shopify_return_id field to store this
            console.log('Shopify return created:', shopifyReturnId);
        }
        catch (shopifyError) {
            console.log('Shopify return creation failed (continuing with local return):', shopifyError);
            // Continue - local return is still valid even if Shopify integration fails
        }
        res.json({
            success: true,
            data: {
                returnId: returnId,
                message: 'Return created successfully'
            }
        });
    }
    catch (error) {
        console.error('Create return error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create return'
        });
    }
});
// Dashboard - Search orders
router.get('/dashboard/orders/search', async (req, res) => {
    try {
        const { q } = req.query;
        if (!q || typeof q !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'Search query is required'
            });
        }
        // Get merchant's Shopify credentials
        const merchant = database_1.db.getMerchantById(req.merchantId);
        if (!merchant?.shopifyApiSecret) {
            return res.status(400).json({
                success: false,
                error: 'Shopify credentials not configured'
            });
        }
        const shopifyService = new shopifyService_1.ShopifyService(merchant.shopifyDomain, merchant.shopifyApiSecret);
        // Search orders by order number or customer email
        const orders = await shopifyService.searchOrders(q.trim());
        res.json({
            success: true,
            data: { orders }
        });
    }
    catch (error) {
        console.error('Search orders error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to search orders'
        });
    }
});
router.get('/dashboard/merchant', async (req, res) => {
    try {
        const merchant = req.merchant;
        res.json({
            merchant: {
                id: merchant.id,
                email: merchant.email,
                storeName: merchant.storeName,
                shopifyDomain: merchant.shopifyDomain,
                plan: merchant.plan,
                hasShopifyCredentials: !!(merchant.shopifyApiKey && merchant.shopifyApiSecret),
                hasSendcloudCredentials: !!(merchant.sendcloudApiKey && merchant.sendcloudApiSecret)
            }
        });
    }
    catch (error) {
        console.error('Get merchant error:', error);
        res.status(500).json({ error: 'Failed to fetch merchant data' });
    }
});
exports.default = router;

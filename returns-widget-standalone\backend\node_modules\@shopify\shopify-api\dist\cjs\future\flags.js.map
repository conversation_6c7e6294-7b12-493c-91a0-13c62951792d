{"version": 3, "file": "flags.js", "sources": ["../../../../../future/flags.ts"], "sourcesContent": ["import {type ShopifyLogger} from '../lib/logger';\nimport {type ConfigInterface} from '../lib/base-types';\n\n/**\n * Future flags are used to enable features that are not yet available by default.\n */\nexport interface FutureFlags {\n  /**\n   * Enable line item billing, to make billing configuration more similar to the GraphQL API. Default enabling of this\n   * feature has been moved to v11. Use lineItemBilling instead.\n   */\n  v10_lineItemBilling?: boolean;\n\n  /**\n   * Enable line item billing, to make billing configuration more similar to the GraphQL API.\n   */\n  lineItemBilling?: boolean;\n\n  /**\n   * Enable support for managed pricing, so apps can check for payments without needing a billing config.\n   */\n  unstable_managedPricingSupport?: boolean;\n\n  /**\n   * Change the CustomerAddress classes to expose a `is_default` property instead of `default` when fetching data. This\n   * resolves a conflict with the default() method in that class.\n   */\n  customerAddressDefaultFix?: boolean;\n}\n\n/**\n * Configuration option for future flags.\n */\nexport type FutureFlagOptions = FutureFlags | undefined;\n\nexport type FeatureEnabled<\n  Future extends FutureFlagOptions,\n  Flag extends keyof FutureFlags,\n> = Future extends FutureFlags\n  ? Future[Flag] extends true\n    ? true\n    : false\n  : false;\n\nexport function logDisabledFutureFlags(\n  config: ConfigInterface,\n  logger: ShopifyLogger,\n) {\n  if (!config._logDisabledFutureFlags) {\n    return;\n  }\n\n  const logFlag = (flag: string, message: string) =>\n    logger.info(`Future flag ${flag} is disabled.\\n\\n  ${message}\\n`);\n\n  if (!config.future?.lineItemBilling) {\n    logFlag(\n      'lineItemBilling',\n      'Enable this flag to use the new billing API, that supports multiple line items per plan.',\n    );\n  }\n  if (config.future?.v10_lineItemBilling) {\n    logger.deprecated(\n      '12.0.0',\n      'v10_lineItemBilling will become enabled in v11. Use flag lineItemBilling instead',\n    );\n  }\n\n  if (!config.future?.customerAddressDefaultFix) {\n    logFlag(\n      'customerAddressDefaultFix',\n      \"Enable this flag to change the CustomerAddress classes to expose a 'is_default' property instead of 'default' when fetching data.\",\n    );\n  }\n\n  if (!config.future?.unstable_managedPricingSupport) {\n    logFlag(\n      'unstable_managedPricingSupport',\n      'Enable this flag to support managed pricing, so apps can check for payments without needing a billing config. Learn more at https://shopify.dev/docs/apps/launch/billing/managed-pricing',\n    );\n  }\n}\n"], "names": [], "mappings": ";;AA4CM,SAAU,sBAAsB,CACpC,MAAuB,EACvB,MAAqB,EAAA;AAErB,IAAA,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE;QACnC;IACF;AAEA,IAAA,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,OAAe,KAC5C,MAAM,CAAC,IAAI,CAAC,CAAA,YAAA,EAAe,IAAI,sBAAsB,OAAO,CAAA,EAAA,CAAI,CAAC;AAEnE,IAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE;AACnC,QAAA,OAAO,CACL,iBAAiB,EACjB,0FAA0F,CAC3F;IACH;AACA,IAAA,IAAI,MAAM,CAAC,MAAM,EAAE,mBAAmB,EAAE;AACtC,QAAA,MAAM,CAAC,UAAU,CACf,QAAQ,EACR,kFAAkF,CACnF;IACH;AAEA,IAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,yBAAyB,EAAE;AAC7C,QAAA,OAAO,CACL,2BAA2B,EAC3B,mIAAmI,CACpI;IACH;AAEA,IAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,8BAA8B,EAAE;AAClD,QAAA,OAAO,CACL,gCAAgC,EAChC,0LAA0L,CAC3L;IACH;AACF;;;;"}
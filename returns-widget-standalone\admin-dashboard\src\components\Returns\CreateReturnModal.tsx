import React, { useState } from 'react';
import { X, Package, AlertCircle, CheckCircle } from 'lucide-react';
import { api } from '../../services/api';
import styles from './CreateReturnModal.module.css';

interface ReturnItem {
  id: string;
  title: string;
  quantity: number;
  price: string;
  sku: string;
  selected: boolean;
  returnQuantity: number;
  reason: string;
  note: string;
}

interface Order {
  id: string;
  name: string;
  customer: {
    first_name: string;
    last_name: string;
    email: string;
  };
  line_items: Array<{
    id: string;
    title: string;
    quantity: number;
    price: string;
    sku: string;
  }>;
}

interface CreateReturnModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: Order;
  onReturnCreated: () => void;
}

const RETURN_REASONS = [
  'Defective/Damaged',
  'Wrong Size',
  'Wrong Item',
  'Changed Mind',
  'Not as Described',
  'Arrived Late',
  'No Longer Needed',
  'Other'
];

export const CreateReturnModal: React.FC<CreateReturnModalProps> = ({
  isOpen,
  onClose,
  order,
  onReturnCreated
}) => {
  const [returnItems, setReturnItems] = useState<ReturnItem[]>(
    order.line_items.map(item => ({
      id: item.id,
      title: item.title,
      quantity: item.quantity,
      price: item.price,
      sku: item.sku,
      selected: false,
      returnQuantity: 1,
      reason: 'Defective/Damaged',
      note: ''
    }))
  );
  
  const [generalNotes, setGeneralNotes] = useState('');
  const [autoApprove, setAutoApprove] = useState(true); // Default to auto-approve
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const handleItemSelection = (itemId: string, selected: boolean) => {
    setReturnItems(items =>
      items.map(item =>
        item.id === itemId ? { ...item, selected } : item
      )
    );
  };

  const handleItemUpdate = (itemId: string, field: keyof ReturnItem, value: any) => {
    setReturnItems(items =>
      items.map(item =>
        item.id === itemId ? { ...item, [field]: value } : item
      )
    );
  };

  const selectedItems = returnItems.filter(item => item.selected);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedItems.length === 0) {
      setError('Please select at least one item to return');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const returnData = {
        orderId: order.id,
        orderNumber: order.name,
        customerEmail: order.customer.email,
        returnItems: selectedItems.map(item => ({
          id: item.id,
          title: item.title,
          quantity: item.returnQuantity,
          reason: item.reason,
          note: item.note
        })),
        notes: generalNotes,
        autoApprove
      };

      const response = await api.createReturn(returnData);
      
      setSuccess(true);
      setSuccessMessage(response.message || 'Return processed successfully!');
      setTimeout(() => {
        onReturnCreated();
        onClose();
        setSuccess(false);
        setSuccessMessage('');
      }, 3000);
    } catch (err: any) {
      setError(err.message || 'Failed to create return request');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
      // Reset form state
      setError('');
      setSuccess(false);
      setSuccessMessage('');
      setGeneralNotes('');
      setAutoApprove(true);
      setReturnItems(order.line_items.map(item => ({
        id: item.id,
        title: item.title,
        quantity: item.quantity,
        price: item.price,
        sku: item.sku,
        selected: false,
        returnQuantity: 1,
        reason: 'Defective/Damaged',
        note: ''
      })));
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay} onClick={handleClose}>
      <div className={styles.modalContainer} onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className={styles.modalHeader}>
          <div className={styles.headerContent}>
            <h3>Request Return</h3>
            <p>Order {order.name} • {order.customer.first_name} {order.customer.last_name}</p>
          </div>
          <button
            onClick={handleClose}
            className={styles.closeButton}
            disabled={loading}
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className={styles.modalContent}>
          <form onSubmit={handleSubmit}>
            {/* Success Message */}
            {success && (
              <div className={styles.summaryAlert}>
                <CheckCircle size={20} />
                <div>
                  <h5 className={styles.summaryTitle}>
                    {autoApprove ? 'Return Approved!' : 'Return Request Submitted!'}
                  </h5>
                  <p className={styles.summaryText}>
                    {successMessage}
                  </p>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className={styles.errorAlert}>
                <AlertCircle size={20} />
                {error}
              </div>
            )}

            {/* Items Selection */}
            <div>
              <h4 className={styles.sectionTitle}>Select Items to Return</h4>
              <div className={styles.itemsGrid}>
                {returnItems.map((item) => (
                  <div 
                    key={item.id} 
                    className={`${styles.itemCard} ${item.selected ? styles.selected : ''}`}
                  >
                    <div className={styles.itemCardHeader}>
                      <input
                        type="checkbox"
                        checked={item.selected}
                        onChange={(e) => handleItemSelection(item.id, e.target.checked)}
                        className={styles.itemCheckbox}
                        disabled={loading || success}
                      />
                      <Package size={20} className={styles.itemIcon} />
                      <div className={styles.itemDetails}>
                        <div className={styles.itemDetailsHeader}>
                          <h5 className={styles.itemTitle}>{item.title}</h5>
                          <span className={styles.itemPrice}>${item.price}</span>
                        </div>
                        <p className={styles.itemMeta}>SKU: {item.sku || 'N/A'}</p>
                        <p className={styles.itemMeta}>Ordered Quantity: {item.quantity}</p>
                      </div>
                    </div>

                    {item.selected && (
                      <div className={styles.itemControls}>
                        <div className={styles.formGroup}>
                          <label className={styles.formLabel}>Return Quantity</label>
                          <select
                            value={item.returnQuantity}
                            onChange={(e) => handleItemUpdate(item.id, 'returnQuantity', parseInt(e.target.value))}
                            className={styles.formSelect}
                            disabled={loading || success}
                          >
                            {Array.from({ length: item.quantity }, (_, i) => i + 1).map(num => (
                              <option key={num} value={num}>{num}</option>
                            ))}
                          </select>
                        </div>
                        <div className={styles.formGroup}>
                          <label className={styles.formLabel}>Return Reason</label>
                          <select
                            value={item.reason}
                            onChange={(e) => handleItemUpdate(item.id, 'reason', e.target.value)}
                            className={styles.formSelect}
                            disabled={loading || success}
                          >
                            {RETURN_REASONS.map(reason => (
                              <option key={reason} value={reason}>{reason}</option>
                            ))}
                          </select>
                        </div>
                        <div className={styles.formGroup}>
                          <label className={styles.formLabel}>Additional Details (Optional)</label>
                          <input
                            type="text"
                            value={item.note}
                            onChange={(e) => handleItemUpdate(item.id, 'note', e.target.value)}
                            placeholder="Describe the issue..."
                            className={styles.formInput}
                            disabled={loading || success}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Return Processing Options */}
            <div className={styles.notesSection}>
              <div className={styles.formGroup}>
                <label className={styles.formLabel}>
                  <input
                    type="checkbox"
                    checked={autoApprove}
                    onChange={(e) => setAutoApprove(e.target.checked)}
                    className={styles.itemCheckbox}
                    disabled={loading || success}
                    style={{ marginRight: '0.5rem', marginTop: 0 }}
                  />
                  Auto-approve return (recommended for customer self-service)
                </label>
                <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.5rem 0 0 0' }}>
                  {autoApprove 
                    ? '✅ Return will be automatically approved and customer can ship items back immediately'
                    : '⏳ Return will require manual merchant approval before customer can ship'
                  }
                </p>
              </div>
            </div>

            {/* General Notes */}
            <div className={styles.notesSection}>
              <label className={styles.formLabel}>
                Additional Notes (Optional)
              </label>
              <textarea
                value={generalNotes}
                onChange={(e) => setGeneralNotes(e.target.value)}
                placeholder="Add any additional information about this return request..."
                className={styles.notesTextarea}
                disabled={loading || success}
              />
            </div>

            {/* Summary */}
            {selectedItems.length > 0 && !success && (
              <div className={styles.summaryAlert}>
                <div>
                  <h5 className={styles.summaryTitle}>Return Processing Summary</h5>
                  <p className={styles.summaryText}>
                    {selectedItems.length} item(s) selected • {autoApprove ? 'Will be auto-approved' : 'Requires manual approval'}
                  </p>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        {!success && (
          <div className={styles.modalFooter}>
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className={styles.cancelButton}
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={loading || selectedItems.length === 0}
              className={styles.submitButton}
            >
              {loading && <span className={styles.loadingSpinner} />}
              {loading 
                ? (autoApprove ? 'Approving Return...' : 'Submitting Request...') 
                : (autoApprove ? 'Approve & Create Return' : 'Submit Return Request')
              }
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

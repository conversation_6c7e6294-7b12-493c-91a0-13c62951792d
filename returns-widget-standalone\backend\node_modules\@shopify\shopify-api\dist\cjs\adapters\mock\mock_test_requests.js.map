{"version": 3, "file": "mock_test_requests.js", "sources": ["../../../../../../adapters/mock/mock_test_requests.ts"], "sourcesContent": ["import {NormalizedRequest, NormalizedResponse} from '../../runtime/http';\n\nimport {mockRequestCapture} from './mock_request_capture';\n\ntype RequestListEntry = NormalizedRequest;\ntype ResponseListEntry = NormalizedResponse | Error;\n\ninterface MockedAdapter {\n  requestList: RequestListEntry[];\n  responseList: ResponseListEntry[];\n  queueResponse: (response: NormalizedResponse) => void;\n  queueError: (error: Error) => void;\n  getRequest: () => RequestListEntry | undefined;\n  getResponses: () => ResponseListEntry[];\n  reset: () => void;\n}\n\nexport const mockTestRequests: MockedAdapter = {\n  requestList: [],\n  responseList: [],\n\n  queueResponse(response: NormalizedResponse): void {\n    this.responseList.push(response);\n  },\n\n  queueError(error: Error): void {\n    this.responseList.push(error);\n  },\n\n  getRequest(): RequestListEntry | undefined {\n    return this.requestList.shift();\n  },\n\n  getResponses(): ResponseListEntry[] {\n    return this.responseList;\n  },\n\n  reset() {\n    this.requestList = [];\n    this.responseList = [];\n    // Also reset the request capture\n    mockRequestCapture.reset();\n  },\n};\n"], "names": [], "mappings": ";;AAiBO,MAAM,gBAAgB,GAAkB;AAC7C,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,YAAY,EAAE;;;;"}
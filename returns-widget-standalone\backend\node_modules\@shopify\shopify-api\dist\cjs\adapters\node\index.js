'use strict';

var crypto$1 = require('crypto');
var fetch = require('node-fetch');
var index = require('../../runtime/http/index.js');
require('../../runtime/crypto/types.js');
var crypto = require('../../runtime/crypto/crypto.js');
var runtimeString = require('../../runtime/platform/runtime-string.js');
var adapter = require('./adapter.js');

// For the purposes of this package, fetch correctly implements everything we need
index.setAbstractFetchFunc(fetch);
index.setAbstractConvertRequestFunc(adapter.nodeConvertRequest);
index.setAbstractConvertIncomingResponseFunc(adapter.nodeConvertIncomingResponse);
index.setAbstractConvertResponseFunc(adapter.nodeConvertAndSendResponse);
index.setAbstractConvertHeadersFunc(adapter.nodeConvertAndSetHeaders);
runtimeString.setAbstractRuntimeString(adapter.nodeRuntimeString);
crypto.setCrypto(crypto$1);
//# sourceMappingURL=index.js.map

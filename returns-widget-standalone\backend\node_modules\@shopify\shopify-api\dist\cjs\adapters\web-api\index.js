'use strict';

var index = require('../../runtime/http/index.js');
require('../../runtime/crypto/types.js');
require('../../runtime/crypto/crypto.js');
var runtimeString = require('../../runtime/platform/runtime-string.js');
var adapter = require('./adapter.js');

index.setAbstractFetchFunc(fetch);
index.setAbstractConvertRequestFunc(adapter.webApiConvertRequest);
index.setAbstractConvertResponseFunc(adapter.webApiConvertResponse);
index.setAbstractConvertHeadersFunc(adapter.webApiConvertHeaders);
runtimeString.setAbstractRuntimeString(adapter.webApiRuntimeString);
//# sourceMappingURL=index.js.map

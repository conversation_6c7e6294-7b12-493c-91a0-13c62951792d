'use strict';

var adminApiClient = require('@shopify/admin-api-client');
var index = require('../../../logger/index.js');
var error = require('../../../error.js');
var index$1 = require('../../../../runtime/http/index.js');
require('../../../../runtime/crypto/types.js');
require('../../../../runtime/crypto/crypto.js');
var common = require('../../common.js');
var headers = require('../../../../runtime/http/headers.js');

class GraphqlClient {
    static config;
    session;
    client;
    apiVersion;
    constructor(params) {
        const config = this.graphqlClass().config;
        if (!config.isCustomStoreApp && !params.session.accessToken) {
            throw new error.MissingRequiredArgument('Missing access token when creating GraphQL client');
        }
        if (params.apiVersion) {
            const message = params.apiVersion === config.apiVersion
                ? `Admin client has a redundant API version override to the default ${params.apiVersion}`
                : `Admin client overriding default API version ${config.apiVersion} with ${params.apiVersion}`;
            index.logger(config).debug(message);
        }
        this.session = params.session;
        this.apiVersion = params.apiVersion;
        this.client = adminApiClient.createAdminApiClient({
            accessToken: config.adminApiAccessToken ?? this.session.accessToken,
            apiVersion: this.apiVersion ?? config.apiVersion,
            storeDomain: this.session.shop,
            customFetchApi: index$1.abstractFetch,
            logger: common.clientLoggerFactory(config),
            userAgentPrefix: common.getUserAgent(config),
            isTesting: config.isTesting,
        });
    }
    async query(params) {
        index.logger(this.graphqlClass().config).deprecated('12.0.0', 'The query method is deprecated, and was replaced with the request method.\n' +
            'See the migration guide: https://github.com/Shopify/shopify-app-js/blob/main/packages/apps/shopify-api/docs/migrating-to-v9.md#using-the-new-clients.');
        if ((typeof params.data === 'string' && params.data.length === 0) ||
            Object.entries(params.data).length === 0) {
            throw new error.MissingRequiredArgument('Query missing.');
        }
        let operation;
        let variables;
        if (typeof params.data === 'string') {
            operation = params.data;
        }
        else {
            operation = params.data.query;
            variables = params.data.variables;
        }
        const headers = Object.fromEntries(Object.entries(params?.extraHeaders ?? {}).map(([key, value]) => [
            key,
            Array.isArray(value) ? value.join(', ') : value.toString(),
        ]));
        const response = await this.request(operation, {
            headers,
            retries: params.tries ? params.tries - 1 : undefined,
            variables,
        });
        return { body: response, headers: {} };
    }
    async request(operation, options) {
        const response = await this.client.request(operation, {
            apiVersion: this.apiVersion || this.graphqlClass().config.apiVersion,
            ...options,
        });
        if (response.errors) {
            const fetchResponse = response.errors.response;
            common.throwFailedRequest(response, (options?.retries ?? 0) > 0, fetchResponse);
        }
        const headerObject = Object.fromEntries(response.headers ? response.headers.entries() : []);
        return {
            ...response,
            headers: headers.canonicalizeHeaders(headerObject ?? {}),
        };
    }
    graphqlClass() {
        return this.constructor;
    }
}
function graphqlClientClass({ config, }) {
    class NewGraphqlClient extends GraphqlClient {
        static config = config;
    }
    Reflect.defineProperty(NewGraphqlClient, 'name', {
        value: 'GraphqlClient',
    });
    return NewGraphqlClient;
}

exports.GraphqlClient = GraphqlClient;
exports.graphqlClientClass = graphqlClientClass;
//# sourceMappingURL=client.js.map

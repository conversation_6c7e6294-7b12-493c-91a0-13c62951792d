"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShopifyService = void 0;
const shopify_api_1 = require("@shopify/shopify-api");
require("@shopify/shopify-api/adapters/node");
class ShopifyService {
    constructor(shopifyDomain, accessToken) {
        this.shopifyDomain = shopifyDomain;
        this.accessToken = accessToken;
        this.shopify = (0, shopify_api_1.shopifyApi)({
            apiKey: process.env.SHOPIFY_API_KEY || 'dummy_key',
            apiSecretKey: process.env.SHOPIFY_API_SECRET || 'dummy_secret',
            scopes: [
                'read_orders',
                'write_orders',
                'read_returns',
                'write_returns',
                'read_customers',
                'write_customers',
                'read_products',
                'read_inventory',
                'write_inventory',
                'read_fulfillments',
                'write_fulfillments'
            ],
            hostName: shopifyDomain,
            apiVersion: shopify_api_1.ApiVersion.July25,
            isEmbeddedApp: false,
            future: {
                lineItemBilling: true,
                customerAddressDefaultFix: true,
                unstable_managedPricingSupport: true,
            },
        });
    }
    createGraphQLClient() {
        const session = new shopify_api_1.Session({
            id: 'custom_app_session',
            shop: this.shopifyDomain,
            state: 'offline',
            isOnline: false,
            scope: 'read_orders,write_orders,read_returns,write_returns,read_customers,write_customers,read_products,read_inventory,write_inventory,read_fulfillments,write_fulfillments',
            expires: undefined,
            accessToken: this.accessToken,
        });
        return new this.shopify.clients.Graphql({ session });
    }
    async findOrderByNumber(orderNumber, customerEmail) {
        const client = this.createGraphQLClient();
        const query = `
      query findOrder($query: String!) {
        orders(first: 1, query: $query) {
          edges {
            node {
              id
              name
              email
              createdAt
              totalPriceSet {
                presentmentMoney {
                  amount
                  currencyCode
                }
              }
              shippingAddress {
                firstName
                lastName
                address1
                address2
                city
                province
                country
                zip
                phone
              }
              lineItems(first: 10) {
                edges {
                  node {
                    id
                    title
                    quantity
                    variant {
                      id
                      title
                      image {
                        url
                      }
                    }
                  }
                }
              }
              fulfillments(first: 5) {
                edges {
                  node {
                    id
                    status
                    trackingCompany
                    trackingNumbers
                    trackingUrls
                  }
                }
              }
            }
          }
        }
      }
    `;
        try {
            console.log(`Shopify API: Searching for order ${orderNumber} with email ${customerEmail}`);
            console.log(`Shopify domain: ${this.shopifyDomain}`);
            console.log(`Access token length: ${this.accessToken?.length || 0}`);
            const response = await client.query({
                data: {
                    query,
                    variables: {
                        query: `name:${orderNumber} AND email:${customerEmail}`
                    }
                }
            });
            console.log('Shopify API response status:', response.status);
            console.log('Shopify API response body keys:', Object.keys(response.body || {}));
            const orders = response.body?.data?.orders?.edges;
            if (!orders || orders.length === 0) {
                console.log('No orders found in Shopify response');
                return null;
            }
            console.log(`Found ${orders.length} matching orders`);
            return orders[0].node;
        }
        catch (error) {
            console.error('Shopify API error details:', {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                responseBody: error.response?.body
            });
            throw error;
        }
    }
    async searchOrders(searchTerm) {
        const client = this.createGraphQLClient();
        // Determine if search term is an order number or email
        const isOrderNumber = searchTerm.startsWith('#') || /^\d+$/.test(searchTerm);
        const searchQuery = isOrderNumber
            ? `name:${searchTerm.replace('#', '')}`
            : `email:${searchTerm}`;
        const query = `
      query searchOrders($query: String!) {
        orders(first: 10, query: $query) {
          edges {
            node {
              id
              name
              email
              createdAt
              displayFinancialStatus
              displayFulfillmentStatus
              totalPriceSet {
                presentmentMoney {
                  amount
                  currencyCode
                }
              }
              customer {
                firstName
                lastName
                email
              }
              lineItems(first: 20) {
                edges {
                  node {
                    id
                    title
                    quantity
                    originalTotalSet {
                      presentmentMoney {
                        amount
                        currencyCode
                      }
                    }
                    sku
                    product {
                      id
                    }
                    variant {
                      id
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;
        try {
            const response = await client.query({
                data: {
                    query,
                    variables: {
                        query: searchQuery
                    }
                }
            });
            const orders = response.body?.data?.orders?.edges || [];
            // Transform the data to a more usable format
            return orders.map((edge) => {
                const node = edge.node;
                return {
                    id: node.id,
                    name: node.name,
                    created_at: node.createdAt,
                    financial_status: node.displayFinancialStatus?.toLowerCase() || 'unknown',
                    fulfillment_status: node.displayFulfillmentStatus?.toLowerCase() || 'unfulfilled',
                    total_price: node.totalPriceSet?.presentmentMoney?.amount || '0',
                    currency: node.totalPriceSet?.presentmentMoney?.currencyCode || 'USD',
                    customer: {
                        first_name: node.customer?.firstName || '',
                        last_name: node.customer?.lastName || '',
                        email: node.customer?.email || node.email || ''
                    },
                    line_items: node.lineItems?.edges?.map((lineItem) => ({
                        id: lineItem.node.id,
                        title: lineItem.node.title,
                        quantity: lineItem.node.quantity,
                        price: lineItem.node.originalTotalSet?.presentmentMoney?.amount || '0',
                        sku: lineItem.node.sku || '',
                        product_id: lineItem.node.product?.id || '',
                        variant_id: lineItem.node.variant?.id || ''
                    })) || []
                };
            });
        }
        catch (error) {
            console.error('Error searching orders:', error);
            throw error;
        }
    }
    async createReturn(orderId, returnItems) {
        const client = this.createGraphQLClient();
        // First, get the order details including fulfillment line items
        const orderQuery = `
      query getOrder($id: ID!) {
        order(id: $id) {
          id
          fulfillments(first: 10) {
            id
            fulfillmentLineItems(first: 50) {
              edges {
                node {
                  id
                  lineItem {
                    id
                    title
                    quantity
                  }
                }
              }
            }
          }
        }
      }
    `;
        try {
            // Get order details first
            const orderResponse = await client.query({
                data: {
                    query: orderQuery,
                    variables: {
                        id: orderId
                    }
                }
            });
            const order = orderResponse.body?.data?.order;
            if (!order) {
                throw new Error('Order not found');
            }
            // Get all fulfillment line items
            const fulfillmentLineItems = [];
            order.fulfillments?.forEach((fulfillment) => {
                fulfillment.fulfillmentLineItems?.edges?.forEach((edge) => {
                    fulfillmentLineItems.push(edge.node);
                });
            });
            if (fulfillmentLineItems.length === 0) {
                throw new Error('No fulfilled items found for this order. Items must be fulfilled before they can be returned.');
            }
            // Map return reasons to valid Shopify values
            const mapReturnReason = (reason) => {
                const reasonMap = {
                    'Changed Mind': 'UNWANTED',
                    'Defective': 'DEFECTIVE',
                    'Wrong Item': 'WRONG_ITEM',
                    'Not as Described': 'NOT_AS_DESCRIBED',
                    'Size Too Small': 'SIZE_TOO_SMALL',
                    'Size Too Large': 'SIZE_TOO_LARGE',
                    'Style': 'STYLE',
                    'Color': 'COLOR',
                    'Other': 'OTHER'
                };
                return reasonMap[reason] || 'OTHER';
            };
            // Create return line items using fulfillment line item IDs
            const returnLineItems = returnItems.map(item => {
                // Find the corresponding fulfillment line item
                const fulfillmentItem = fulfillmentLineItems.find(fli => fli.lineItem.title === item.title || fli.lineItem.id === item.lineItemId);
                if (!fulfillmentItem) {
                    throw new Error(`No fulfillment found for item: ${item.title}`);
                }
                const mappedReason = mapReturnReason(item.reason || 'Other');
                const returnLineItem = {
                    fulfillmentLineItemId: fulfillmentItem.id,
                    quantity: item.quantity,
                    returnReason: mappedReason,
                };
                // Add returnReasonNote when reason is OTHER
                if (mappedReason === 'OTHER') {
                    returnLineItem.returnReasonNote = item.note || item.reason || 'Customer requested return';
                }
                return returnLineItem;
            });
            // Create the return
            const mutation = `
        mutation returnCreate($returnInput: ReturnInput!) {
          returnCreate(returnInput: $returnInput) {
            return {
              id
              name
              status
            }
            userErrors {
              field
              message
            }
          }
        }
      `;
            const response = await client.query({
                data: {
                    query: mutation,
                    variables: {
                        returnInput: {
                            orderId: orderId,
                            returnLineItems: returnLineItems,
                            requestedAt: new Date().toISOString(),
                            notifyCustomer: false
                        }
                    }
                }
            });
            const returnData = response.body?.data?.returnCreate;
            if (returnData?.userErrors?.length > 0) {
                console.error('Shopify return creation errors:', returnData.userErrors);
                throw new Error(`Return creation failed: ${returnData.userErrors[0].message}`);
            }
            return returnData?.return?.id;
        }
        catch (error) {
            console.error('Error creating return:', error);
            throw error;
        }
    }
    async testConnection() {
        try {
            const client = this.createGraphQLClient();
            const query = `
        query {
          shop {
            id
            name
          }
        }
      `;
            await client.query({
                data: { query }
            });
            return true;
        }
        catch (error) {
            console.error('Shopify connection test failed:', error);
            return false;
        }
    }
    async getShopInfo() {
        try {
            const client = this.createGraphQLClient();
            const query = `
        query {
          shop {
            id
            name
          }
        }
      `;
            const response = await client.query({
                data: { query }
            });
            return response.body?.data?.shop || null;
        }
        catch (error) {
            console.error('Failed to get shop info:', error);
            return null;
        }
    }
}
exports.ShopifyService = ShopifyService;

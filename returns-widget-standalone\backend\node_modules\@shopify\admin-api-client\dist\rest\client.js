'use strict';

var graphqlClient = require('@shopify/graphql-client');
var validations = require('../validations.js');
var constants = require('../constants.js');
var types = require('./types.js');

function createAdminRestApiClient({ storeDomain, apiVersion, accessToken, userAgentPrefix, logger, customFetchApi = fetch, retries: clientRetries = 0, scheme = 'https', defaultRetryTime = constants.DEFAULT_RETRY_WAIT_TIME, formatPaths = true, isTesting, }) {
    const currentSupportedApiVersions = graphqlClient.getCurrentSupportedApiVersions();
    const storeUrl = graphqlClient.validateDomainAndGetStoreUrl({
        client: constants.CLIENT,
        storeDomain,
    }).replace('https://', `${scheme}://`);
    const baseApiVersionValidationParams = {
        client: constants.CLIENT,
        currentSupportedApiVersions,
        logger,
    };
    validations.validateServerSideUsage(isTesting);
    graphqlClient.validateApiVersion({
        client: constants.CLIENT,
        currentSupportedApiVersions,
        apiVersion,
        logger,
    });
    validations.validateRequiredAccessToken(accessToken);
    graphqlClient.validateRetries({ client: constants.CLIENT, retries: clientRetries });
    const apiUrlFormatter = generateApiUrlFormatter(storeUrl, apiVersion, baseApiVersionValidationParams, formatPaths);
    const clientLogger = generateClientLogger(logger);
    const httpFetch = graphqlClient.generateHttpFetch({
        customFetchApi,
        clientLogger,
        defaultRetryWaitTime: defaultRetryTime,
        client: constants.CLIENT,
        retriableCodes: constants.RETRIABLE_STATUS_CODES,
    });
    const request = async (path, { method, data, headers: requestHeadersObj, searchParams, retries = 0, apiVersion, }) => {
        graphqlClient.validateRetries({ client: constants.CLIENT, retries });
        const url = apiUrlFormatter(path, searchParams ?? {}, apiVersion);
        const requestHeaders = normalizedHeaders(requestHeadersObj ?? {});
        const userAgent = [
            ...(requestHeaders['user-agent'] ? [requestHeaders['user-agent']] : []),
            ...(userAgentPrefix ? [userAgentPrefix] : []),
            `${constants.CLIENT} v${constants.DEFAULT_CLIENT_VERSION}`,
        ].join(' | ');
        const headers = normalizedHeaders({
            'Content-Type': constants.DEFAULT_CONTENT_TYPE,
            ...requestHeaders,
            Accept: constants.DEFAULT_CONTENT_TYPE,
            [constants.ACCESS_TOKEN_HEADER]: accessToken,
            'User-Agent': userAgent,
        });
        const body = data && typeof data !== 'string' ? JSON.stringify(data) : data;
        return httpFetch([url, { method, headers, ...(body ? { body } : undefined) }], 1, retries ?? clientRetries);
    };
    return {
        get: (path, options) => request(path, { method: types.Method.Get, ...options }),
        put: (path, options) => request(path, { method: types.Method.Put, ...options }),
        post: (path, options) => request(path, { method: types.Method.Post, ...options }),
        delete: (path, options) => request(path, { method: types.Method.Delete, ...options }),
    };
}
function generateApiUrlFormatter(storeUrl, defaultApiVersion, baseApiVersionValidationParams, formatPaths = true) {
    return (path, searchParams, apiVersion) => {
        if (apiVersion) {
            graphqlClient.validateApiVersion({
                ...baseApiVersionValidationParams,
                apiVersion,
            });
        }
        function convertValue(params, key, value) {
            if (Array.isArray(value)) {
                value.forEach((arrayValue) => convertValue(params, `${key}[]`, arrayValue));
                return;
            }
            else if (typeof value === 'object') {
                Object.entries(value).forEach(([objKey, objValue]) => convertValue(params, `${key}[${objKey}]`, objValue));
                return;
            }
            params.append(key, String(value));
        }
        const urlApiVersion = (apiVersion ?? defaultApiVersion).trim();
        let cleanPath = path.replace(/^\//, '');
        if (formatPaths) {
            if (!cleanPath.startsWith('admin')) {
                cleanPath = `admin/api/${urlApiVersion}/${cleanPath}`;
            }
            if (!cleanPath.endsWith('.json')) {
                cleanPath = `${cleanPath}.json`;
            }
        }
        const params = new URLSearchParams();
        if (searchParams) {
            for (const [key, value] of Object.entries(searchParams)) {
                convertValue(params, key, value);
            }
        }
        const queryString = params.toString() ? `?${params.toString()}` : '';
        return `${storeUrl}/${cleanPath}${queryString}`;
    };
}
function generateClientLogger(logger) {
    return (logContent) => {
        if (logger) {
            logger(logContent);
        }
    };
}
function normalizedHeaders(headersObj) {
    const normalizedHeaders = {};
    for (const [key, value] of Object.entries(headersObj)) {
        normalizedHeaders[key.toLowerCase()] = Array.isArray(value)
            ? value.join(', ')
            : String(value);
    }
    return normalizedHeaders;
}

exports.createAdminRestApiClient = createAdminRestApiClient;
//# sourceMappingURL=client.js.map

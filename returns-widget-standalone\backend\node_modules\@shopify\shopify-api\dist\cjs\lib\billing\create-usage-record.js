'use strict';

var error = require('../error.js');
var client = require('../clients/admin/graphql/client.js');
require('@shopify/admin-api-client');
require('@shopify/network');
require('../types.js');
require('../../runtime/crypto/crypto.js');
require('../../runtime/crypto/types.js');
require('compare-versions');
var check = require('./check.js');
var utils = require('./utils.js');

const CREATE_USAGE_RECORD_MUTATION = `
mutation appUsageRecordCreate($description: String!, $price: MoneyInput!, $subscriptionLineItemId: ID!) {
  appUsageRecordCreate(description: $description, price: $price, subscriptionLineItemId: $subscriptionLineItemId) {
    userErrors {
      field
      message
    }
    appUsageRecord {
      id
      description
      idempotencyKey
      price {
        amount
        currencyCode
      }
      subscriptionLineItem {
        id
        plan {
          pricingDetails {
            ... on AppUsagePricing {
              balanceUsed {
                amount
                currencyCode
              }
              cappedAmount {
                amount
                currencyCode
              }
              terms
            }
          }
        }
      }
    }
  }
}
`;
function createUsageRecord(config) {
    return async function createUsageRecord(usageRecordInfo) {
        const { session, subscriptionLineItemId, description, price, idempotencyKey, isTest = true, } = usageRecordInfo;
        const GraphqlClient = client.graphqlClientClass({ config });
        const client$1 = new GraphqlClient({ session });
        // If a subscription line item ID is not passed, we will query Shopify
        // for an active usage subscription line item ID
        const usageSubscriptionLineItemId = subscriptionLineItemId
            ? subscriptionLineItemId
            : await getUsageRecordSubscriptionLineItemId({ client: client$1, isTest });
        const variables = {
            description,
            price,
            subscriptionLineItemId: usageSubscriptionLineItemId,
        };
        if (idempotencyKey) {
            variables.idempotencyKey = idempotencyKey;
        }
        try {
            const response = await client$1.request(CREATE_USAGE_RECORD_MUTATION, {
                variables,
            });
            if (response.data?.appUsageRecordCreate?.userErrors.length) {
                throw new error.BillingError({
                    message: 'Error while creating a usage record',
                    errorData: response.data?.appUsageRecordCreate?.userErrors,
                });
            }
            const appUsageRecord = response.data?.appUsageRecordCreate?.appUsageRecord;
            utils.convertAppRecurringPricingMoney(appUsageRecord.price);
            utils.convertAppUsagePricingMoney(appUsageRecord.subscriptionLineItem.plan.pricingDetails);
            return appUsageRecord;
        }
        catch (error$1) {
            if (error$1 instanceof error.GraphqlQueryError) {
                throw new error.BillingError({
                    message: error$1.message,
                    errorData: error$1.response?.errors,
                });
            }
            else {
                throw error$1;
            }
        }
    };
}
async function getUsageRecordSubscriptionLineItemId({ client, isTest, }) {
    const payments = await check.assessPayments({ client, isTest });
    if (!payments.hasActivePayment) {
        throw new error.BillingError({
            message: 'No active payment found',
            errorData: [],
        });
    }
    if (!payments.appSubscriptions.length) {
        throw new error.BillingError({
            message: 'No active subscriptions found',
            errorData: [],
        });
    }
    if (payments.appSubscriptions) {
        const usageSubscriptionLineItemId = getUsageLineItemId(payments.appSubscriptions);
        return usageSubscriptionLineItemId;
    }
    throw new error.BillingError({
        message: 'Unable to find active subscription line item',
        errorData: [],
    });
}
function getUsageLineItemId(subscriptions) {
    for (const subscription of subscriptions) {
        // An app can have only one active subscription
        if (subscription.status === 'ACTIVE' && subscription.lineItems) {
            // An app can have only one usage subscription line item
            for (const lineItem of subscription.lineItems) {
                if ('balanceUsed' in lineItem.plan.pricingDetails) {
                    return lineItem.id;
                }
            }
        }
    }
    throw new error.BillingError({
        message: 'No active usage subscription found',
        errorData: [],
    });
}

exports.createUsageRecord = createUsageRecord;
//# sourceMappingURL=create-usage-record.js.map

{"version": 3, "file": "processed-query.js", "sources": ["../../../../../../lib/utils/processed-query.ts"], "sourcesContent": ["export default class ProcessedQuery {\n  static stringify(keyValuePairs?: Record<string, any>): string {\n    if (!keyValuePairs || Object.keys(keyValuePairs).length === 0) return '';\n\n    return new ProcessedQuery().putAll(keyValuePairs).stringify();\n  }\n\n  processedQuery: URLSearchParams;\n\n  constructor() {\n    this.processedQuery = new URLSearchParams();\n  }\n\n  putAll(keyValuePairs: Record<string, any>): ProcessedQuery {\n    Object.entries(keyValuePairs).forEach(([key, value]: [string, any]) =>\n      this.put(key, value),\n    );\n    return this;\n  }\n\n  put(key: string, value: any): void {\n    if (Array.isArray(value)) {\n      this.putArray(key, value);\n    } else if (value?.constructor === Object) {\n      this.putObject(key, value);\n    } else {\n      this.putSimple(key, value);\n    }\n  }\n\n  putArray(key: string, value: (string | number)[]): void {\n    value.forEach((arrayValue) =>\n      this.processedQuery.append(`${key}[]`, `${arrayValue}`),\n    );\n  }\n\n  putObject(key: string, value: object): void {\n    Object.entries(value).forEach(\n      ([entry, entryValue]: [string, string | number]) => {\n        this.processedQuery.append(`${key}[${entry}]`, `${entryValue}`);\n      },\n    );\n  }\n\n  putSimple(key: string, value: string | number): void {\n    this.processedQuery.append(key, `${value}`);\n  }\n\n  stringify(omitQuestionMark = false): string {\n    const queryString = this.processedQuery.toString();\n    return omitQuestionMark ? queryString : `?${queryString}`;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAc,MAAO,cAAc,CAAA;IACjC,OAAO,SAAS,CAAC,aAAmC,EAAA;AAClD,QAAA,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,KAAK,CAAC;AAAE,YAAA,OAAO,EAAE;QAExE,OAAO,IAAI,cAAc,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE;IAC/D;AAEA,IAAA,cAAc;AAEd,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,eAAe,EAAE;IAC7C;AAEA,IAAA,MAAM,CAAC,aAAkC,EAAA;QACvC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAgB,KAChE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CACrB;AACD,QAAA,OAAO,IAAI;IACb;IAEA,GAAG,CAAC,GAAW,EAAE,KAAU,EAAA;AACzB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC;QAC3B;AAAO,aAAA,IAAI,KAAK,EAAE,WAAW,KAAK,MAAM,EAAE;AACxC,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC;QAC5B;aAAO;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC;QAC5B;IACF;IAEA,QAAQ,CAAC,GAAW,EAAE,KAA0B,EAAA;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,KACvB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,EAAG,GAAG,CAAA,EAAA,CAAI,EAAE,GAAG,UAAU,CAAA,CAAE,CAAC,CACxD;IACH;IAEA,SAAS,CAAC,GAAW,EAAE,KAAa,EAAA;AAClC,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAC3B,CAAC,CAAC,KAAK,EAAE,UAAU,CAA4B,KAAI;AACjD,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,KAAK,GAAG,EAAE,CAAA,EAAG,UAAU,CAAA,CAAE,CAAC;AACjE,QAAA,CAAC,CACF;IACH;IAEA,SAAS,CAAC,GAAW,EAAE,KAAsB,EAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAA,EAAG,KAAK,CAAA,CAAE,CAAC;IAC7C;IAEA,SAAS,CAAC,gBAAgB,GAAG,KAAK,EAAA;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QAClD,OAAO,gBAAgB,GAAG,WAAW,GAAG,CAAA,CAAA,EAAI,WAAW,CAAA,CAAE;IAC3D;AACD;;;;"}
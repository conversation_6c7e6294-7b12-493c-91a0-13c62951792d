import { shopifyApi, ApiVersion, Session } from '@shopify/shopify-api';
import '@shopify/shopify-api/adapters/node';

export class ShopifyService {
  private shopifyDomain: string;
  private accessToken: string;
  private shopify: any;
  private client: any;

  constructor(shopifyDomain: string, accessToken: string) {
    this.shopifyDomain = shopifyDomain;
    this.accessToken = accessToken;

    console.log('ShopifyService initialized with domain:', shopifyDomain);
    console.log('Access token length:', accessToken?.length || 0);
    
    this.shopify = shopifyApi({
      apiKey: process.env.SHOPIFY_API_KEY || 'dummy_key',
      apiSecretKey: process.env.SHOPIFY_API_SECRET || 'dummy_secret',
      scopes: [
        'read_orders', 
        'write_orders', 
        'read_returns', 
        'write_returns', 
        'read_customers',
        'write_customers',
        'read_products',
        'read_inventory',
        'write_inventory',
        'read_fulfillments',
        'write_fulfillments'
      ],
      hostName: shopifyDomain,
      apiVersion: ApiVersion.July25,
      isEmbeddedApp: false,
      future: {
        lineItemBilling: true,
        customerAddressDefaultFix: true,
        unstable_managedPricingSupport: true,
      },
    });

    this.client = this.createGraphQLClient();
  }

  private createGraphQLClient() {
    const session = new Session({
      id: 'custom_app_session',
      shop: this.shopifyDomain,
      state: 'offline',
      isOnline: false,
      scope: 'read_orders,write_orders,read_returns,write_returns,read_customers,write_customers,read_products,read_inventory,write_inventory,read_fulfillments,write_fulfillments',
      expires: undefined,
      accessToken: this.accessToken,
    });

    return new this.shopify.clients.Graphql({ session });
  }

  async searchOrders(query: string, customerEmail?: string, customerPhone?: string): Promise<any> {
    try {
      // Handle different order number formats
      let searchQuery = '';

      // If query looks like a number, try both formats
      if (/^\d+$/.test(query)) {
        searchQuery = `name:#${query} OR name:${query}`;
      } else {
        searchQuery = `name:${query}`;
      }

      if (customerEmail) {
        searchQuery += ` email:${customerEmail}`;
      }

      if (customerPhone) {
        searchQuery += ` phone:${customerPhone}`;
      }

      const ordersQuery = `
        query searchOrders($query: String!) {
          orders(first: 5, query: $query) {
            nodes {
              id
              name
              orderNumber
              createdAt
              customer {
                email
                firstName
                lastName
                phone
              }
              lineItems(first: 20) {
                nodes {
                  id
                  title
                  variantTitle
                  quantity
                  fulfillmentStatus
                  originalUnitPriceSet {
                    shopMoney {
                      amount
                      currencyCode
                    }
                  }
                  image {
                    url
                  }
                }
              }
            }
          }
        }
      `;

      console.log('Searching orders with query:', searchQuery);

      const response = await this.client.query({
        data: {
          query: ordersQuery,
          variables: { query: searchQuery }
        }
      });

      console.log('Shopify API response:', JSON.stringify(response?.body, null, 2));

      const orders = response?.body?.data?.orders?.nodes || [];
      console.log('Found orders:', orders.length);

      return orders.length > 0 ? orders[0] : null;

    } catch (error) {
      console.error('Error searching orders:', error);
      if (error.body?.errors) {
        console.error('GraphQL errors:', error.body.errors);
      }
      throw new Error('Failed to search orders');
    }
  }

  async createReturn(orderNumber: string, items: any[], reason: string, autoApprove: boolean = false): Promise<any> {
    try {
      console.log(`Creating return for order ${orderNumber}:`, { items, reason, autoApprove });

      // For now, return a mock success response since Shopify Returns API is complex
      const mockReturn = {
        id: `gid://shopify/Return/${Date.now()}`,
        name: `R${Date.now()}`,
        status: autoApprove ? 'APPROVED' : 'PENDING'
      };

      console.log('Mock return created:', mockReturn);
      return mockReturn;

    } catch (error) {
      console.error('Error creating return:', error);
      throw error;
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const query = `
        query {
          shop {
            id
            name
          }
        }
      `;

      await this.client.query({
        data: { query }
      });

      return true;
    } catch (error) {
      console.error('Shopify connection test failed:', error);
      return false;
    }
  }

  async getShopInfo(): Promise<{ id: string; name: string } | null> {
    try {
      const query = `
        query {
          shop {
            id
            name
          }
        }
      `;

      const response = await this.client.query({
        data: { query }
      });

      return response.body?.data?.shop || null;
    } catch (error) {
      console.error('Failed to get shop info:', error);
      return null;
    }
  }
}

import React, { useState, useEffect } from 'react'
import { ArrowLeft, Package, User, Calendar, Truck, Download, Check, X, AlertCircle } from 'lucide-react'
import { api } from '../../services/api'
import type { ReturnRecord } from '../../services/types'
import styles from './ReturnDetails.module.css'

interface ReturnDetailsProps {
  returnId: string
  onBack: () => void
}

export const ReturnDetails: React.FC<ReturnDetailsProps> = ({ returnId, onBack }) => {
  const [returnData, setReturnData] = useState<ReturnRecord | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    loadReturnDetails()
  }, [returnId])

  const loadReturnDetails = async () => {
    try {
      setLoading(true)
      // Try to get specific return details first, fall back to list search
      try {
        const data = await api.getReturnDetails(returnId)
        setReturnData(data)
      } catch {
        // If specific endpoint doesn't exist yet, fall back to searching the list
        const allReturns = await api.getReturns()
        const returnRecord = allReturns.find(r => r.id === returnId)
        
        if (!returnRecord) {
          throw new Error('Return not found')
        }
        
        setReturnData(returnRecord)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load return details')
    } finally {
      setLoading(false)
    }
  }

  const updateReturnStatus = async (newStatus: ReturnRecord['status']) => {
    if (!returnData) return

    try {
      setUpdating(true)
      await api.updateReturnStatus(returnId, newStatus)
      setReturnData({ ...returnData, status: newStatus, updated_at: new Date().toISOString() })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update return status')
    } finally {
      setUpdating(false)
    }
  }

  const getStatusColor = (status: ReturnRecord['status']) => {
    switch (status) {
      case 'pending': return '#d97706'
      case 'approved': return '#059669'
      case 'rejected': return '#dc2626'
      case 'completed': return '#2563eb'
      default: return '#6b7280'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getReasonLabel = (reason: string) => {
    const reasons = {
      'defective': 'Defective',
      'wrong_item': 'Wrong Item',
      'not_as_described': 'Not as Described',
      'size_issue': 'Size Issue',
      'changed_mind': 'Changed Mind',
      'other': 'Other'
    }
    return reasons[reason as keyof typeof reasons] || reason
  }

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading return details...</p>
        </div>
      </div>
    )
  }

  if (error || !returnData) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <AlertCircle size={24} />
          <p>{error || 'Return not found'}</p>
          <div className={styles.errorActions}>
            <button onClick={onBack} className={styles.backButton}>
              Go Back
            </button>
            <button onClick={loadReturnDetails} className={styles.retryButton}>
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <button onClick={onBack} className={styles.backButton}>
          <ArrowLeft size={20} />
          Back to Returns
        </button>
        
        <div className={styles.headerInfo}>
          <h1>Return #{returnData.id.slice(-8)}</h1>
          <div 
            className={styles.status}
            style={{ 
              backgroundColor: `${getStatusColor(returnData.status)}20`,
              color: getStatusColor(returnData.status)
            }}
          >
            {returnData.status.charAt(0).toUpperCase() + returnData.status.slice(1)}
          </div>
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.mainSection}>
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <Package size={20} />
              <h2>Return Items</h2>
            </div>
            <div className={styles.items}>
              {returnData.return_items.map((item, index) => (
                <div key={index} className={styles.item}>
                  <div className={styles.itemInfo}>
                    <h4>Line Item: {item.lineItemId}</h4>
                    <p><strong>Quantity:</strong> {item.quantity}</p>
                    <p><strong>Reason:</strong> {getReasonLabel(item.reason)}</p>
                    <p><strong>Condition:</strong> {item.condition}</p>
                    {item.customerNote && (
                      <p><strong>Customer Note:</strong> {item.customerNote}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <User size={20} />
              <h2>Customer Information</h2>
            </div>
            <div className={styles.customerInfo}>
              <p><strong>Email:</strong> {returnData.customer_email}</p>
              <p><strong>Order ID:</strong> {returnData.shopify_order_id}</p>
            </div>
          </div>
        </div>

        <div className={styles.sidebar}>
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <Calendar size={20} />
              <h2>Timeline</h2>
            </div>
            <div className={styles.timeline}>
              <div className={styles.timelineItem}>
                <div className={styles.timelineIcon}>
                  <Package size={16} />
                </div>
                <div className={styles.timelineContent}>
                  <h4>Return Created</h4>
                  <p>{formatDate(returnData.created_at)}</p>
                </div>
              </div>
              
              {returnData.updated_at !== returnData.created_at && (
                <div className={styles.timelineItem}>
                  <div className={styles.timelineIcon}>
                    <AlertCircle size={16} />
                  </div>
                  <div className={styles.timelineContent}>
                    <h4>Status Updated</h4>
                    <p>{formatDate(returnData.updated_at)}</p>
                  </div>
                </div>
              )}

              {returnData.tracking_number && (
                <div className={styles.timelineItem}>
                  <div className={styles.timelineIcon}>
                    <Truck size={16} />
                  </div>
                  <div className={styles.timelineContent}>
                    <h4>Tracking Generated</h4>
                    <p>Tracking: {returnData.tracking_number}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {returnData.status === 'pending' && (
            <div className={styles.card}>
              <div className={styles.cardHeader}>
                <AlertCircle size={20} />
                <h2>Actions</h2>
              </div>
              <div className={styles.actions}>
                <button
                  onClick={() => updateReturnStatus('approved')}
                  disabled={updating}
                  className={`${styles.actionButton} ${styles.approveButton}`}
                >
                  <Check size={16} />
                  Approve Return
                </button>
                <button
                  onClick={() => updateReturnStatus('rejected')}
                  disabled={updating}
                  className={`${styles.actionButton} ${styles.rejectButton}`}
                >
                  <X size={16} />
                  Reject Return
                </button>
              </div>
            </div>
          )}

          {returnData.label_url && (
            <div className={styles.card}>
              <div className={styles.cardHeader}>
                <Download size={20} />
                <h2>Shipping Label</h2>
              </div>
              <div className={styles.labelInfo}>
                <p>Return shipping label is available for download.</p>
                <a
                  href={returnData.label_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.downloadButton}
                >
                  <Download size={16} />
                  Download Label
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

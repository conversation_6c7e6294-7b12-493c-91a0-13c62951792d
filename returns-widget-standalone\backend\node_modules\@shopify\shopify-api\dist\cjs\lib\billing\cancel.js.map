{"version": 3, "file": "cancel.js", "sources": ["../../../../../../lib/billing/cancel.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\nimport {graphqlClientClass} from '../clients/admin';\nimport {BillingError, GraphqlQueryError} from '../error';\n\nimport {\n  AppSubscription,\n  BillingCancel,\n  BillingCancelParams,\n  CancelResponse,\n  APP_SUBSCRIPTION_FRAGMENT,\n} from './types';\n\nconst CANCEL_MUTATION = `\n  ${APP_SUBSCRIPTION_FRAGMENT}\n  mutation appSubscriptionCancel($id: ID!, $prorate: Boolean) {\n    appSubscriptionCancel(id: $id, prorate: $prorate) {\n      appSubscription {\n        ...AppSubscriptionFragment\n      }\n      userErrors {\n        field\n        message\n      }\n    }\n  }\n`;\n\nexport function cancel(config: ConfigInterface): BillingCancel {\n  return async function (\n    subscriptionInfo: BillingCancelParams,\n  ): Promise<AppSubscription> {\n    const {session, subscriptionId, prorate = true} = subscriptionInfo;\n\n    const GraphqlClient = graphqlClientClass({config});\n    const client = new GraphqlClient({session});\n\n    try {\n      const response = await client.request<CancelResponse>(CANCEL_MUTATION, {\n        variables: {id: subscriptionId, prorate},\n      });\n\n      if (response.data?.appSubscriptionCancel?.userErrors.length) {\n        throw new BillingError({\n          message: 'Error while canceling a subscription',\n          errorData: response.data?.appSubscriptionCancel?.userErrors,\n        });\n      }\n\n      return response.data?.appSubscriptionCancel?.appSubscription!;\n    } catch (error) {\n      if (error instanceof GraphqlQueryError) {\n        throw new BillingError({\n          message: error.message,\n          errorData: error.response?.errors,\n        });\n      } else {\n        throw error;\n      }\n    }\n  };\n}\n"], "names": ["APP_SUBSCRIPTION_FRAGMENT", "graphqlClientClass", "client", "BillingError", "error", "GraphqlQueryError"], "mappings": ";;;;;;;;;;;;AAYA,MAAM,eAAe,GAAG;IACpBA,+BAAyB;;;;;;;;;;;;CAY5B;AAEK,SAAU,MAAM,CAAC,MAAuB,EAAA;IAC5C,OAAO,gBACL,gBAAqC,EAAA;QAErC,MAAM,EAAC,OAAO,EAAE,cAAc,EAAE,OAAO,GAAG,IAAI,EAAC,GAAG,gBAAgB;QAElE,MAAM,aAAa,GAAGC,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;QAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;AAE3C,QAAA,IAAI;YACF,MAAM,QAAQ,GAAG,MAAMA,QAAM,CAAC,OAAO,CAAiB,eAAe,EAAE;AACrE,gBAAA,SAAS,EAAE,EAAC,EAAE,EAAE,cAAc,EAAE,OAAO,EAAC;AACzC,aAAA,CAAC;YAEF,IAAI,QAAQ,CAAC,IAAI,EAAE,qBAAqB,EAAE,UAAU,CAAC,MAAM,EAAE;gBAC3D,MAAM,IAAIC,kBAAY,CAAC;AACrB,oBAAA,OAAO,EAAE,sCAAsC;AAC/C,oBAAA,SAAS,EAAE,QAAQ,CAAC,IAAI,EAAE,qBAAqB,EAAE,UAAU;AAC5D,iBAAA,CAAC;YACJ;AAEA,YAAA,OAAO,QAAQ,CAAC,IAAI,EAAE,qBAAqB,EAAE,eAAgB;QAC/D;QAAE,OAAOC,OAAK,EAAE;AACd,YAAA,IAAIA,OAAK,YAAYC,uBAAiB,EAAE;gBACtC,MAAM,IAAIF,kBAAY,CAAC;oBACrB,OAAO,EAAEC,OAAK,CAAC,OAAO;AACtB,oBAAA,SAAS,EAAEA,OAAK,CAAC,QAAQ,EAAE,MAAM;AAClC,iBAAA,CAAC;YACJ;iBAAO;AACL,gBAAA,MAAMA,OAAK;YACb;QACF;AACF,IAAA,CAAC;AACH;;;;"}
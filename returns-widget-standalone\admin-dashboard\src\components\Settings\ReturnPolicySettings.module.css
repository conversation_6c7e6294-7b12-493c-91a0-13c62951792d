.container {
  max-width: 800px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.header p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.field label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.input, .textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:disabled, .textarea:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}

.textarea {
  resize: vertical;
  min-height: 120px;
}

.hint {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.checkboxField {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 0.75rem;
}

.checkboxField input[type="checkbox"] {
  margin: 0;
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.checkboxField label {
  flex: 1;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.checkboxField label small {
  font-weight: 400;
  color: #6b7280;
  font-size: 0.75rem;
}

.inline {
  display: inline;
  margin-right: 0.25rem;
}

.reasonsList {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.reasonItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.removeButton {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 1.25rem;
  line-height: 1;
  padding: 0;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.removeButton:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.addReason {
  display: flex;
  gap: 0.5rem;
}

.addReason .input {
  flex: 1;
}

.addButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.addButton:hover:not(:disabled) {
  background-color: #2563eb;
}

.addButton:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.colorField {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.colorInput {
  width: 3rem;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  padding: 0.25rem;
}

.colorField .input {
  flex: 1;
  font-family: monospace;
}

.errorMessage, .successMessage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
}

.errorMessage {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.successMessage {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

.actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.saveButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saveButton:hover:not(:disabled) {
  background-color: #2563eb;
}

.saveButton:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .section {
    padding: 1rem;
  }
  
  .addReason {
    flex-direction: column;
  }
  
  .colorField {
    flex-direction: column;
    align-items: stretch;
  }
}

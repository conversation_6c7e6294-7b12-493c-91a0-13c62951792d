{"version": 3, "file": "refund.js", "sources": ["../../../../../../../rest/admin/2022-10/refund.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\nimport {Transaction} from './transaction';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  order_id?: number | string | null;\n  fields?: unknown;\n  in_shop_currency?: unknown;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  order_id?: number | string | null;\n  limit?: unknown;\n  fields?: unknown;\n  in_shop_currency?: unknown;\n}\ninterface CalculateArgs {\n  [key: string]: unknown;\n  shipping?: unknown;\n  refund_line_items?: unknown;\n  currency?: unknown;\n  body?: {[key: string]: unknown} | null;\n}\n\nexport class Refund extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {\n    \"transactions\": Transaction\n  };\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"order_id\"], \"path\": \"orders/<order_id>/refunds.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"order_id\", \"id\"], \"path\": \"orders/<order_id>/refunds/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"calculate\", \"ids\": [\"order_id\"], \"path\": \"orders/<order_id>/refunds/calculate.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [\"order_id\"], \"path\": \"orders/<order_id>/refunds.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"refund\",\n      \"plural\": \"refunds\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      order_id = null,\n      fields = null,\n      in_shop_currency = null\n    }: FindArgs\n  ): Promise<Refund | null> {\n    const result = await this.baseFind<Refund>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id, \"order_id\": order_id},\n      params: {\"fields\": fields, \"in_shop_currency\": in_shop_currency},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async all(\n    {\n      session,\n      order_id = null,\n      limit = null,\n      fields = null,\n      in_shop_currency = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Refund>> {\n    const response = await this.baseFind<Refund>({\n      session: session,\n      urlIds: {\"order_id\": order_id},\n      params: {\"limit\": limit, \"fields\": fields, \"in_shop_currency\": in_shop_currency, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public async calculate(\n    {\n      shipping = null,\n      refund_line_items = null,\n      currency = null,\n      body = null,\n      ...otherArgs\n    }: CalculateArgs\n  ): Promise<unknown> {\n    const response = await this.request<Refund>({\n      http_method: \"post\",\n      operation: \"calculate\",\n      session: this.session,\n      urlIds: {\"order_id\": this.order_id},\n      params: {\"shipping\": shipping, \"refund_line_items\": refund_line_items, \"currency\": currency, ...otherArgs},\n      body: body,\n      entity: this,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public created_at: string | null;\n  public duties: {[key: string]: unknown}[] | null;\n  public id: number | null;\n  public note: string | null;\n  public order_adjustments: {[key: string]: unknown}[] | null;\n  public order_id: number | null;\n  public processed_at: string | null;\n  public refund_duties: {[key: string]: unknown}[] | null;\n  public refund_line_items: {[key: string]: unknown}[] | null;\n  public restock: boolean | null;\n  public transactions: Transaction[] | null | {[key: string]: any};\n  public user_id: number | null;\n}\n"], "names": ["Base", "ApiVersion", "Transaction"], "mappings": ";;;;;;AAAA;;AAEwH;AAgClH,MAAO,MAAO,SAAQA,SAAI,CAAA;AACvB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;IAChD,OAAO,OAAO,GAAiC;AACvD,QAAA,cAAc,EAAEC;KACjB;IACS,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,gCAAgC,EAAC;AACzG,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,qCAAqC,EAAC;AACpH,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,0CAA0C,EAAC;AAC1H,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,gCAAgC;KAC3G;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,QAAQ,GAAG,IAAI,EACf,MAAM,GAAG,IAAI,EACb,gBAAgB,GAAG,IAAI,EACd,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAS;AACzC,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAC;YACxC,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,EAAC;AACjE,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,GAAG,CACrB,EACE,OAAO,EACP,QAAQ,GAAG,IAAI,EACf,KAAK,GAAG,IAAI,EACZ,MAAM,GAAG,IAAI,EACb,gBAAgB,GAAG,IAAI,EACvB,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAS;AAC3C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAC;AAC9B,YAAA,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,SAAS,EAAC;AAC/F,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;IAEO,MAAM,SAAS,CACpB,EACE,QAAQ,GAAG,IAAI,EACf,iBAAiB,GAAG,IAAI,EACxB,QAAQ,GAAG,IAAI,EACf,IAAI,GAAG,IAAI,EACX,GAAG,SAAS,EACE,EAAA;AAEhB,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAS;AAC1C,YAAA,WAAW,EAAE,MAAM;AACnB,YAAA,SAAS,EAAE,WAAW;YACtB,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAC;AACnC,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAC;AAC1G,YAAA,IAAI,EAAE,IAAI;AACV,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,EAAE;AACF,IAAA,IAAI;AACJ,IAAA,iBAAiB;AACjB,IAAA,QAAQ;AACR,IAAA,YAAY;AACZ,IAAA,aAAa;AACb,IAAA,iBAAiB;AACjB,IAAA,OAAO;AACP,IAAA,YAAY;AACZ,IAAA,OAAO;;;;;"}
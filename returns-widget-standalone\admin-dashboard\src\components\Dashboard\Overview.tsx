import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { api } from '../../services/api'
import type { DashboardMetrics } from '../../services/types'
import { Package, Clock, CheckCircle, XCircle, Settings, Plus, BarChart3 } from 'lucide-react'
import styles from './Overview.module.css'

export const Overview = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadMetrics = async () => {
      try {
        console.log('Loading dashboard metrics...')
        const data = await api.getDashboardMetrics()
        console.log('Dashboard metrics loaded:', data)
        setMetrics(data)
      } catch (err) {
        console.error('Dashboard metrics error:', err)
        setError(err instanceof Error ? err.message : 'Failed to load metrics')
      } finally {
        setLoading(false)
      }
    }

    loadMetrics()
  }, [])

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading dashboard...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>Error: {error}</div>
      </div>
    )
  }

  if (!metrics) {
    return null
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Dashboard Overview</h1>
        <p>Welcome back! Here's what's happening with your returns.</p>
      </div>

      <div className={styles.metrics}>
        <div className={styles.metric}>
          <div className={styles.metricIcon}>
            <Package size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Total Returns</h3>
            <p className={styles.metricValue}>{metrics.totalReturns}</p>
          </div>
        </div>

        <div className={styles.metric}>
          <div className={styles.metricIcon}>
            <Clock size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Pending</h3>
            <p className={styles.metricValue}>{metrics.pendingReturns}</p>
          </div>
        </div>

        <div className={styles.metric}>
          <div className={styles.metricIcon}>
            <CheckCircle size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Approved</h3>
            <p className={styles.metricValue}>{metrics.approvedReturns}</p>
          </div>
        </div>

        <div className={styles.metric}>
          <div className={styles.metricIcon}>
            <XCircle size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Rejected</h3>
            <p className={styles.metricValue}>{metrics.rejectedReturns}</p>
          </div>
        </div>
      </div>

      <div className={styles.quickActions}>
        <h2>Quick Actions</h2>
        <div className={styles.actionButtons}>
          <Link to="/settings" className={styles.actionButton}>
            <Settings size={20} />
            <span>Configure Settings</span>
          </Link>
          <Link to="/returns" className={styles.actionButton}>
            <Package size={20} />
            <span>View All Returns</span>
          </Link>
          <Link to="/analytics" className={styles.actionButton}>
            <BarChart3 size={20} />
            <span>View Analytics</span>
          </Link>
        </div>
      </div>

      <div className={styles.recentReturns}>
        <h2>Recent Returns</h2>
        {!metrics.recentReturns || metrics.recentReturns.length === 0 ? (
          <div className={styles.emptyState}>
            <Package size={48} />
            <h3>No returns yet</h3>
            <p>Returns will appear here once customers start using your widget.</p>
            <p className={styles.setupHint}>
              Make sure to configure your Shopify and Sendcloud credentials in Settings to start processing returns.
            </p>
          </div>
        ) : (
          <div className={styles.returnsList}>
            {metrics.recentReturns.map((returnRecord) => (
              <div key={returnRecord.id} className={styles.returnItem}>
                <div className={styles.returnInfo}>
                  <div className={styles.returnEmail}>{returnRecord.customer_email}</div>
                  <div className={styles.returnOrder}>
                    Order: {returnRecord.shopify_order_id?.replace('gid://shopify/Order/', '') || 'N/A'}
                  </div>
                </div>
                <div className={styles.returnStatus}>
                  <span className={`${styles.statusBadge} ${styles[returnRecord.status]}`}>
                    {returnRecord.status}
                  </span>
                </div>
                <div className={styles.returnDate}>
                  {new Date(returnRecord.created_at).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

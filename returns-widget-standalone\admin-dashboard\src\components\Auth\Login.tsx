import { useState } from 'react'
import { useAuth } from '../../hooks/useAuth'
import styles from './Login.module.css'

export const Login = () => {
  const { login, loading, error, clearError } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showRegister, setShowRegister] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()
    
    try {
      await login({ email, password })
    } catch (err) {
      // Error is handled by useAuth
    }
  }

  if (showRegister) {
    return <Register onBackToLogin={() => setShowRegister(false)} />
  }

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <div className={styles.header}>
          <h1>Returns Widget</h1>
          <p>Sign in to your merchant dashboard</p>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.field}>
            <label htmlFor="email">Email</label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={loading}
            />
          </div>

          <div className={styles.field}>
            <label htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
            />
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          <button 
            type="submit" 
            disabled={loading}
            className={styles.submitButton}
          >
            {loading ? 'Signing in...' : 'Sign In'}
          </button>
        </form>

        <div className={styles.footer}>
          <p>
            Don't have an account?{' '}
            <button 
              type="button"
              onClick={() => setShowRegister(true)}
              className={styles.linkButton}
            >
              Sign up
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}

// Simple Register component placeholder
const Register = ({ onBackToLogin }: { onBackToLogin: () => void }) => {
  const { register, loading, error, clearError } = useAuth()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    storeName: '',
    shopifyDomain: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()
    
    try {
      await register(formData)
    } catch (err) {
      // Error is handled by useAuth
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <div className={styles.header}>
          <h1>Create Account</h1>
          <p>Set up your returns management dashboard</p>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.field}>
            <label htmlFor="email">Email</label>
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              required
              disabled={loading}
            />
          </div>

          <div className={styles.field}>
            <label htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
              required
              disabled={loading}
            />
          </div>

          <div className={styles.field}>
            <label htmlFor="storeName">Store Name</label>
            <input
              id="storeName"
              type="text"
              value={formData.storeName}
              onChange={(e) => setFormData(prev => ({ ...prev, storeName: e.target.value }))}
              required
              disabled={loading}
            />
          </div>

          <div className={styles.field}>
            <label htmlFor="shopifyDomain">Shopify Domain</label>
            <input
              id="shopifyDomain"
              type="text"
              placeholder="mystore.myshopify.com"
              value={formData.shopifyDomain}
              onChange={(e) => setFormData(prev => ({ ...prev, shopifyDomain: e.target.value }))}
              required
              disabled={loading}
            />
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          <button 
            type="submit" 
            disabled={loading}
            className={styles.submitButton}
          >
            {loading ? 'Creating account...' : 'Create Account'}
          </button>
        </form>

        <div className={styles.footer}>
          <p>
            Already have an account?{' '}
            <button 
              type="button"
              onClick={onBackToLogin}
              className={styles.linkButton}
            >
              Sign in
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}

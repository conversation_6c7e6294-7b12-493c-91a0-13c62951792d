import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart, Line, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer 
} from 'recharts';
import { 
  TrendingUp, Package, Clock, RefreshCw, Download
} from 'lucide-react';
import { format, subDays } from 'date-fns';
import { api } from '../services/api';
import styles from './AnalyticsPage.module.css';

interface AnalyticsData {
  totalReturns: number;
  pendingReturns: number;
  approvedReturns: number;
  rejectedReturns: number;
  averageProcessingTime: number;
  returnsOverTime: Array<{
    date: string;
    count: number;
  }>;
  statusDistribution: Array<{
    name: string;
    value: number;
  }>;
}

const COLORS = ['#3b82f6', '#ef4444', '#f59e0b', '#10b981'];

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<{start: string; end: string}>({
    start: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    end: format(new Date(), 'yyyy-MM-dd')
  });

  useEffect(() => {
    loadAnalytics();
  }, [dateRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // For now, generate mock data since our backend analytics is still basic
      const mockData: AnalyticsData = {
        totalReturns: 157,
        pendingReturns: 23,
        approvedReturns: 98,
        rejectedReturns: 36,
        averageProcessingTime: 4.2,
        returnsOverTime: [
          { date: '2025-07-20', count: 12 },
          { date: '2025-07-21', count: 18 },
          { date: '2025-07-22', count: 15 },
          { date: '2025-07-23', count: 22 },
          { date: '2025-07-24', count: 19 },
          { date: '2025-07-25', count: 25 },
          { date: '2025-07-26', count: 31 }
        ],
        statusDistribution: [
          { name: 'Approved', value: 98 },
          { name: 'Pending', value: 23 },
          { name: 'Rejected', value: 36 }
        ]
      };
      
      setAnalytics(mockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics');
    } finally {
      setLoading(false);
    }
  };

  const setQuickDateRange = (days: number) => {
    setDateRange({
      start: format(subDays(new Date(), days), 'yyyy-MM-dd'),
      end: format(new Date(), 'yyyy-MM-dd')
    });
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <RefreshCw className={styles.spinner} size={24} />
          Loading analytics...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <Package size={24} />
          <h3>Failed to load analytics</h3>
          <p>{error}</p>
          <button onClick={loadAnalytics} className={styles.retryButton}>
            <RefreshCw size={16} />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!analytics) return null;

  const approvalRate = analytics.totalReturns > 0 
    ? Math.round((analytics.approvedReturns / analytics.totalReturns) * 100) 
    : 0;

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div>
          <h1>Analytics Dashboard</h1>
          <p>Overview of your returns performance and trends</p>
        </div>
        <div className={styles.headerActions}>
          <button onClick={loadAnalytics} className={styles.refreshButton}>
            <RefreshCw size={16} />
            Refresh
          </button>
        </div>
      </div>

      {/* Date Range Controls */}
      <div className={styles.dateControls}>
        <div className={styles.quickFilters}>
          <button onClick={() => setQuickDateRange(7)} className={styles.quickFilter}>
            Last 7 days
          </button>
          <button onClick={() => setQuickDateRange(30)} className={styles.quickFilter}>
            Last 30 days
          </button>
          <button onClick={() => setQuickDateRange(90)} className={styles.quickFilter}>
            Last 90 days
          </button>
        </div>
        <div className={styles.dateInputs}>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) => setDateRange(prev => ({...prev, start: e.target.value}))}
            className={styles.dateInput}
          />
          <span>to</span>
          <input
            type="date"
            value={dateRange.end}
            onChange={(e) => setDateRange(prev => ({...prev, end: e.target.value}))}
            className={styles.dateInput}
          />
        </div>
      </div>

      {/* Key Metrics */}
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricIcon}>
            <Package size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Total Returns</h3>
            <p className={styles.metricValue}>{analytics.totalReturns}</p>
            <span className={styles.metricSubtext}>All time returns</span>
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricIcon}>
            <TrendingUp size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Approval Rate</h3>
            <p className={styles.metricValue}>{approvalRate}%</p>
            <span className={styles.metricSubtext}>Returns approved</span>
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricIcon}>
            <Clock size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Avg Processing Time</h3>
            <p className={styles.metricValue}>{analytics.averageProcessingTime}h</p>
            <span className={styles.metricSubtext}>Hours to approval</span>
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricIcon}>
            <Package size={24} />
          </div>
          <div className={styles.metricContent}>
            <h3>Pending Returns</h3>
            <p className={styles.metricValue}>{analytics.pendingReturns}</p>
            <span className={styles.metricSubtext}>Awaiting review</span>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className={styles.chartsGrid}>
        {/* Returns Over Time */}
        <div className={styles.chartCard}>
          <h3>Returns Over Time</h3>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={analytics.returnsOverTime}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="count" stroke="#3b82f6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Status Distribution */}
        <div className={styles.chartCard}>
          <h3>Return Status Distribution</h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={analytics.statusDistribution}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                nameKey="name"
              >
                {analytics.statusDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Monthly Breakdown */}
        <div className={styles.chartCard}>
          <h3>Daily Returns (Last 7 Days)</h3>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={analytics.returnsOverTime}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Status Summary */}
        <div className={styles.tableCard}>
          <h3>Status Summary</h3>
          <div className={styles.statusSummary}>
            <div className={styles.statusItem}>
              <div className={styles.statusColor} style={{backgroundColor: '#10b981'}}></div>
              <span>Approved: {analytics.approvedReturns}</span>
            </div>
            <div className={styles.statusItem}>
              <div className={styles.statusColor} style={{backgroundColor: '#f59e0b'}}></div>
              <span>Pending: {analytics.pendingReturns}</span>
            </div>
            <div className={styles.statusItem}>
              <div className={styles.statusColor} style={{backgroundColor: '#ef4444'}}></div>
              <span>Rejected: {analytics.rejectedReturns}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

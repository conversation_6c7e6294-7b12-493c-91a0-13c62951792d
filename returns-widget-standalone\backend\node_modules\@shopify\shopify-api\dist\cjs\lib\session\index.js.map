{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/session/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\n\nimport {decodeSessionToken} from './decode-session-token';\nimport {\n  customAppSession,\n  getCurrentSessionId,\n  getJwtSessionId,\n  getOfflineId,\n} from './session-utils';\n\nexport function shopifySession(config: ConfigInterface) {\n  return {\n    customAppSession: customAppSession(config),\n    getCurrentId: getCurrentSessionId(config),\n    getOfflineId: getOfflineId(config),\n    getJwtSessionId: getJwtSessionId(config),\n    decodeSessionToken: decodeSessionToken(config),\n  };\n}\n\nexport type ShopifySession = ReturnType<typeof shopifySession>;\n"], "names": ["customAppSession", "getCurrentSessionId", "getOfflineId", "getJwtSessionId", "decodeSessionToken"], "mappings": ";;;;;AAUM,SAAU,cAAc,CAAC,MAAuB,EAAA;IACpD,OAAO;AACL,QAAA,gBAAgB,EAAEA,6BAAgB,CAAC,MAAM,CAAC;AAC1C,QAAA,YAAY,EAAEC,gCAAmB,CAAC,MAAM,CAAC;AACzC,QAAA,YAAY,EAAEC,yBAAY,CAAC,MAAM,CAAC;AAClC,QAAA,eAAe,EAAEC,4BAAe,CAAC,MAAM,CAAC;AACxC,QAAA,kBAAkB,EAAEC,qCAAkB,CAAC,MAAM,CAAC;KAC/C;AACH;;;;"}
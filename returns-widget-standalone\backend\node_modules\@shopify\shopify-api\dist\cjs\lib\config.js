'use strict';

var error = require('./error.js');
var types = require('./types.js');
var index = require('./auth/scopes/index.js');
var index$1 = require('./logger/index.js');

function validateConfig(params) {
    const config = {
        apiKey: '',
        apiSecretKey: '',
        hostName: '',
        hostScheme: 'https',
        apiVersion: types.LATEST_API_VERSION,
        isEmbeddedApp: true,
        isCustomStoreApp: false,
        logger: {
            log: defaultLogFunction,
            level: types.LogSeverity.Info,
            httpRequests: false,
            timestamps: false,
        },
        future: {},
        _logDisabledFutureFlags: true,
    };
    // Make sure that the essential params actually have content in them
    const mandatory = ['apiSecretKey', 'hostName'];
    if (!('isCustomStoreApp' in params) || !params.isCustomStoreApp) {
        mandatory.push('apiKey');
    }
    if ('isCustomStoreApp' in params && params.isCustomStoreApp) {
        if (!('adminApiAccessToken' in params) ||
            params.adminApiAccessToken?.length === 0) {
            mandatory.push('adminApiAccessToken');
        }
    }
    const missing = [];
    mandatory.forEach((key) => {
        if (!notEmpty(params[key])) {
            missing.push(key);
        }
    });
    if (missing.length) {
        throw new error.ShopifyError(`Cannot initialize Shopify API Library. Missing values for: ${missing.join(', ')}`);
    }
    // Alias the v10_lineItemBilling flag to lineItemBilling because we aren't releasing in v10
    const future = params.future?.v10_lineItemBilling
        ? {
            lineItemBilling: params.future?.v10_lineItemBilling,
            ...params.future,
        }
        : params.future;
    const { hostScheme, isCustomStoreApp, adminApiAccessToken, userAgentPrefix, logger, privateAppStorefrontAccessToken, customShopDomains, billing, ...mandatoryParams } = params;
    let scopes;
    if (params.scopes === undefined) {
        scopes = undefined;
    }
    else if (params.scopes instanceof index.AuthScopes) {
        scopes = params.scopes;
    }
    else {
        scopes = new index.AuthScopes(params.scopes);
    }
    Object.assign(config, mandatoryParams, {
        hostName: params.hostName.replace(/\/$/, ''),
        scopes,
        hostScheme: hostScheme ?? config.hostScheme,
        isCustomStoreApp: isCustomStoreApp ?? config.isCustomStoreApp,
        adminApiAccessToken: adminApiAccessToken ?? config.adminApiAccessToken,
        userAgentPrefix: userAgentPrefix ?? config.userAgentPrefix,
        logger: { ...config.logger, ...(logger || {}) },
        privateAppStorefrontAccessToken: privateAppStorefrontAccessToken ?? config.privateAppStorefrontAccessToken,
        customShopDomains: customShopDomains ?? config.customShopDomains,
        billing: billing ?? config.billing,
        future: future ?? config.future,
    });
    if (config.isCustomStoreApp &&
        params.adminApiAccessToken === params.apiSecretKey) {
        index$1.logger(config).warning("adminApiAccessToken is set to the same value as apiSecretKey. adminApiAccessToken should be set to the Admin API access token for custom store apps; apiSecretKey should be set to the custom store app's API secret key.");
    }
    return config;
}
function notEmpty(value) {
    if (value == null) {
        return false;
    }
    return typeof value === 'string' || Array.isArray(value)
        ? value.length > 0
        : true;
}
function defaultLogFunction(severity, message) {
    switch (severity) {
        case types.LogSeverity.Debug:
            console.debug(message);
            break;
        case types.LogSeverity.Info:
            console.log(message);
            break;
        case types.LogSeverity.Warning:
            console.warn(message);
            break;
        case types.LogSeverity.Error:
            console.error(message);
            break;
    }
}

exports.validateConfig = validateConfig;
//# sourceMappingURL=config.js.map

{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/billing/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\nimport {FutureFlagOptions} from '../../future/flags';\n\nimport {check} from './check';\nimport {request} from './request';\nimport {cancel} from './cancel';\nimport {subscriptions} from './subscriptions';\nimport {createUsageRecord} from './create-usage-record';\nimport {updateUsageCappedAmount} from './update-usage-subscription-capped-amount';\nimport {ShopifyBilling} from './types';\n\nexport type {ShopifyBilling} from './types';\n\nexport function shopifyBilling<Future extends FutureFlagOptions>(\n  config: ConfigInterface,\n): ShopifyBilling<Future> {\n  return {\n    check: check(config),\n    request: request(config),\n    cancel: cancel(config),\n    subscriptions: subscriptions(config),\n    createUsageRecord: createUsageRecord(config),\n    updateUsageCappedAmount: updateUsageCappedAmount(config),\n  };\n}\n"], "names": ["check", "request", "cancel", "subscriptions", "createUsageRecord", "updateUsageCappedAmount"], "mappings": ";;;;;;;;;AAaM,SAAU,cAAc,CAC5B,MAAuB,EAAA;IAEvB,OAAO;AACL,QAAA,KAAK,EAAEA,WAAK,CAAC,MAAM,CAAC;AACpB,QAAA,OAAO,EAAEC,eAAO,CAAC,MAAM,CAAC;AACxB,QAAA,MAAM,EAAEC,aAAM,CAAC,MAAM,CAAC;AACtB,QAAA,aAAa,EAAEC,2BAAa,CAAC,MAAM,CAAC;AACpC,QAAA,iBAAiB,EAAEC,mCAAiB,CAAC,MAAM,CAAC;AAC5C,QAAA,uBAAuB,EAAEC,2DAAuB,CAAC,MAAM,CAAC;KACzD;AACH;;;;"}
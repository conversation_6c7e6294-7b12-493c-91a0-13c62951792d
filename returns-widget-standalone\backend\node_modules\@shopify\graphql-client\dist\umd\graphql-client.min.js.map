{"version": 3, "file": "graphql-client.min.js", "sources": ["../../../src/graphql-client/constants.ts", "../../../src/graphql-client/utilities.ts", "../../../src/graphql-client/http-fetch.ts", "../../../src/graphql-client/graphql-client.ts"], "sourcesContent": ["export const CLIENT = 'GraphQL Client';\nexport const MIN_RETRIES = 0;\nexport const MAX_RETRIES = 3;\n\nexport const GQL_API_ERROR =\n  \"An error occurred while fetching from the API. Review 'graphQLErrors' for details.\";\nexport const UNEXPECTED_CONTENT_TYPE_ERROR =\n  'Response returned unexpected Content-Type:';\nexport const NO_DATA_OR_ERRORS_ERROR =\n  'An unknown error has occurred. The API did not return a data object or any errors in its response.';\n\nexport const CONTENT_TYPES = {\n  json: 'application/json',\n  multipart: 'multipart/mixed',\n};\nexport const SDK_VARIANT_HEADER = 'X-SDK-Variant';\nexport const SDK_VERSION_HEADER = 'X-SDK-Version';\n\nexport const DEFAULT_SDK_VARIANT = 'shopify-graphql-client';\n// This is value is replaced with package.json version during rollup build process\nexport const DEFAULT_CLIENT_VERSION = 'ROLLUP_REPLACE_CLIENT_VERSION';\n\nexport const RETRY_WAIT_TIME = 1000;\nexport const RETRIABLE_STATUS_CODES = [429, 503];\nexport const DEFER_OPERATION_REGEX = /@(defer)\\b/i;\nexport const NEWLINE_SEPARATOR = '\\r\\n';\nexport const BOUNDARY_HEADER_REGEX = /boundary=\"?([^=\";]+)\"?/i;\nexport const HEADER_SEPARATOR = NEWLINE_SEPARATOR + NEWLINE_SEPARATOR;\n", "import {CLIENT, MAX_RETRIES, MIN_RETRIES} from './constants';\n\nexport function formatErrorMessage(message: string, client = CLIENT) {\n  return message.startsWith(`${client}`) ? message : `${client}: ${message}`;\n}\n\nexport function getErrorMessage(error: any) {\n  return error instanceof Error ? error.message : JSON.stringify(error);\n}\n\nexport function getErrorCause(error: any): Record<string, any> | undefined {\n  return error instanceof Error && error.cause ? error.cause : undefined;\n}\n\nexport function combineErrors(dataArray: Record<string, any>[]) {\n  return dataArray.flatMap(({errors}) => {\n    return errors ?? [];\n  });\n}\n\nexport function validateRetries({\n  client,\n  retries,\n}: {\n  client: string;\n  retries?: number;\n}) {\n  if (\n    retries !== undefined &&\n    (typeof retries !== 'number' ||\n      retries < MIN_RETRIES ||\n      retries > MAX_RETRIES)\n  ) {\n    throw new Error(\n      `${client}: The provided \"retries\" value (${retries}) is invalid - it cannot be less than ${MIN_RETRIES} or greater than ${MAX_RETRIES}`,\n    );\n  }\n}\n\nexport function getKeyValueIfValid(key: string, value?: any) {\n  return value &&\n    (typeof value !== 'object' ||\n      Array.isArray(value) ||\n      (typeof value === 'object' && Object.keys(value).length > 0))\n    ? {[key]: value}\n    : {};\n}\n\nexport function buildDataObjectByPath(\n  path: string[],\n  data: any,\n): Record<string | number, any> {\n  if (path.length === 0) {\n    return data;\n  }\n\n  const key = path.pop() as string | number;\n  const newData = {\n    [key]: data,\n  };\n\n  if (path.length === 0) {\n    return newData;\n  }\n\n  return buildDataObjectByPath(path, newData);\n}\n\nfunction combineObjects(baseObject: any, newObject: any) {\n  return Object.keys(newObject || {}).reduce(\n    (acc: any, key: string | number) => {\n      if (\n        (typeof newObject[key] === 'object' || Array.isArray(newObject[key])) &&\n        baseObject[key]\n      ) {\n        acc[key] = combineObjects(baseObject[key], newObject[key]);\n        return acc;\n      }\n\n      acc[key] = newObject[key];\n      return acc;\n    },\n    Array.isArray(baseObject) ? [...baseObject] : {...baseObject},\n  );\n}\n\nexport function buildCombinedDataObject([\n  initialDatum,\n  ...remainingData\n]: any[]) {\n  return remainingData.reduce(combineObjects, {...initialDatum});\n}\n", "import {CLIENT, RETRIABLE_STATUS_CODES, RETRY_WAIT_TIME} from './constants';\nimport {CustomFetchApi, GraphQLClient, Logger} from './types';\nimport {formatErrorMessage, getErrorMessage} from './utilities';\n\ninterface GenerateHttpFetchOptions {\n  clientLogger: Logger;\n  customFetchApi?: CustomFetchApi;\n  client?: string;\n  defaultRetryWaitTime?: number;\n  retriableCodes?: number[];\n}\n\nexport function generateHttpFetch({\n  clientLogger,\n  customFetchApi = fetch,\n  client = CLIENT,\n  defaultRetryWaitTime = RETRY_WAIT_TIME,\n  retriableCodes = RETRIABLE_STATUS_CODES,\n}: GenerateHttpFetchOptions) {\n  const httpFetch = async (\n    requestParams: Parameters<CustomFetchApi>,\n    count: number,\n    maxRetries: number,\n  ): ReturnType<GraphQLClient['fetch']> => {\n    const nextCount = count + 1;\n    const maxTries = maxRetries + 1;\n    let response: Response | undefined;\n\n    try {\n      response = await customFetchA<PERSON>(...requestParams);\n\n      clientLogger({\n        type: 'HTTP-Response',\n        content: {\n          requestParams,\n          response,\n        },\n      });\n\n      if (\n        !response.ok &&\n        retriableCodes.includes(response.status) &&\n        nextCount <= maxTries\n      ) {\n        throw new Error();\n      }\n\n      const deprecationNotice =\n        response?.headers.get('X-Shopify-API-Deprecated-Reason') || '';\n      if (deprecationNotice) {\n        clientLogger({\n          type: 'HTTP-Response-GraphQL-Deprecation-Notice',\n          content: {\n            requestParams,\n            deprecationNotice,\n          },\n        });\n      }\n\n      return response;\n    } catch (error) {\n      if (nextCount <= maxTries) {\n        const retryAfter = response?.headers.get('Retry-After');\n        await sleep(\n          retryAfter ? parseInt(retryAfter, 10) : defaultRetryWaitTime,\n        );\n\n        clientLogger({\n          type: 'HTTP-Retry',\n          content: {\n            requestParams,\n            lastResponse: response,\n            retryAttempt: count,\n            maxRetries,\n          },\n        });\n\n        return httpFetch(requestParams, nextCount, maxRetries);\n      }\n\n      throw new Error(\n        formatErrorMessage(\n          `${\n            maxRetries > 0\n              ? `Attempted maximum number of ${maxRetries} network retries. Last message - `\n              : ''\n          }${getErrorMessage(error)}`,\n          client,\n        ),\n      );\n    }\n  };\n\n  return httpFetch;\n}\n\nasync function sleep(waitTime: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, waitTime));\n}\n", "import {generateHttpFetch} from './http-fetch';\nimport {\n  ClientOptions,\n  CustomFetchApi,\n  GraphQLClient,\n  ClientResponse,\n  ClientConfig,\n  Logger,\n  LogContentTypes,\n  DataChunk,\n} from './types';\nimport {\n  CLIENT,\n  GQL_API_ERROR,\n  UNEXPECTED_CONTENT_TYPE_ERROR,\n  NO_DATA_OR_ERRORS_ERROR,\n  CONTENT_TYPES,\n  RETRY_WAIT_TIME,\n  HEADER_SEPARATOR,\n  DEFER_OPERATION_REGEX,\n  BOUNDARY_HEADER_REGEX,\n  SDK_VARIANT_HEADER,\n  SDK_VERSION_HEADER,\n  DEFAULT_SDK_VARIANT,\n  DEFAULT_CLIENT_VERSION,\n} from './constants';\nimport {\n  formatErrorMessage,\n  getErrorMessage,\n  validateRetries,\n  getKeyValueIfValid,\n  buildDataObjectByPath,\n  buildCombinedDataObject,\n  getErrorCause,\n  combineErrors,\n} from './utilities';\n\nexport function createGraphQLClient({\n  headers,\n  url,\n  customFetchApi = fetch,\n  retries = 0,\n  logger,\n}: ClientOptions): GraphQLClient {\n  validateRetries({client: CLIENT, retries});\n\n  const config: ClientConfig = {\n    headers,\n    url,\n    retries,\n  };\n\n  const clientLogger = generateClientLogger(logger);\n  const httpFetch = generateHttpFetch({\n    customFetchApi,\n    clientLogger,\n    defaultRetryWaitTime: RETRY_WAIT_TIME,\n  });\n  const fetchFn = generateFetch(httpFetch, config);\n  const request = generateRequest(fetchFn);\n  const requestStream = generateRequestStream(fetchFn);\n\n  return {\n    config,\n    fetch: fetchFn,\n    request,\n    requestStream,\n  };\n}\n\nexport function generateClientLogger(logger?: Logger): Logger {\n  return (logContent: LogContentTypes) => {\n    if (logger) {\n      logger(logContent);\n    }\n  };\n}\n\nasync function processJSONResponse<TData = any>(\n  response: Response,\n): Promise<ClientResponse<TData>> {\n  const {errors, data, extensions} = await response.json<any>();\n\n  return {\n    ...getKeyValueIfValid('data', data),\n    ...getKeyValueIfValid('extensions', extensions),\n    headers: response.headers,\n\n    ...(errors || !data\n      ? {\n          errors: {\n            networkStatusCode: response.status,\n            message: formatErrorMessage(\n              errors ? GQL_API_ERROR : NO_DATA_OR_ERRORS_ERROR,\n            ),\n            ...getKeyValueIfValid('graphQLErrors', errors),\n            response,\n          },\n        }\n      : {}),\n  };\n}\n\nfunction generateFetch(\n  httpFetch: ReturnType<typeof generateHttpFetch>,\n  {url, headers, retries}: ClientConfig,\n): GraphQLClient['fetch'] {\n  return async (operation, options = {}) => {\n    const {\n      variables,\n      headers: overrideHeaders,\n      url: overrideUrl,\n      retries: overrideRetries,\n      keepalive,\n      signal,\n    } = options;\n\n    const body = JSON.stringify({\n      query: operation,\n      variables,\n    });\n\n    validateRetries({client: CLIENT, retries: overrideRetries});\n\n    const flatHeaders = Object.entries({\n      ...headers,\n      ...overrideHeaders,\n    }).reduce((headers: Record<string, string>, [key, value]) => {\n      headers[key] = Array.isArray(value) ? value.join(', ') : value.toString();\n      return headers;\n    }, {});\n\n    if (!flatHeaders[SDK_VARIANT_HEADER] && !flatHeaders[SDK_VERSION_HEADER]) {\n      flatHeaders[SDK_VARIANT_HEADER] = DEFAULT_SDK_VARIANT;\n      flatHeaders[SDK_VERSION_HEADER] = DEFAULT_CLIENT_VERSION;\n    }\n\n    const fetchParams: Parameters<CustomFetchApi> = [\n      overrideUrl ?? url,\n      {\n        method: 'POST',\n        headers: flatHeaders,\n        body,\n        signal,\n        keepalive,\n      },\n    ];\n\n    return httpFetch(fetchParams, 1, overrideRetries ?? retries);\n  };\n}\n\nfunction generateRequest(\n  fetchFn: ReturnType<typeof generateFetch>,\n): GraphQLClient['request'] {\n  return async (...props) => {\n    if (DEFER_OPERATION_REGEX.test(props[0])) {\n      throw new Error(\n        formatErrorMessage(\n          'This operation will result in a streamable response - use requestStream() instead.',\n        ),\n      );\n    }\n\n    let response: Response | null = null;\n    try {\n      response = await fetchFn(...props);\n      const {status, statusText} = response;\n      const contentType = response.headers.get('content-type') || '';\n\n      if (!response.ok) {\n        return {\n          errors: {\n            networkStatusCode: status,\n            message: formatErrorMessage(statusText),\n            response,\n          },\n        };\n      }\n\n      if (!contentType.includes(CONTENT_TYPES.json)) {\n        return {\n          errors: {\n            networkStatusCode: status,\n            message: formatErrorMessage(\n              `${UNEXPECTED_CONTENT_TYPE_ERROR} ${contentType}`,\n            ),\n            response,\n          },\n        };\n      }\n\n      return await processJSONResponse(response);\n    } catch (error) {\n      return {\n        errors: {\n          message: getErrorMessage(error),\n          ...(response == null\n            ? {}\n            : {\n                networkStatusCode: response.status,\n                response,\n              }),\n        },\n      };\n    }\n  };\n}\n\nasync function* getStreamBodyIterator(\n  response: Response,\n): AsyncIterableIterator<string> {\n  const decoder = new TextDecoder();\n\n  // Response body is an async iterator\n  if ((response.body as any)![Symbol.asyncIterator]) {\n    for await (const chunk of response.body! as any) {\n      yield decoder.decode(chunk);\n    }\n  } else {\n    const reader = response.body!.getReader();\n\n    let readResult: ReadableStreamReadResult<DataChunk>;\n    try {\n      while (!(readResult = await reader.read()).done) {\n        yield decoder.decode(readResult.value);\n      }\n    } finally {\n      reader.cancel();\n    }\n  }\n}\n\nfunction readStreamChunk(\n  streamBodyIterator: AsyncIterableIterator<string>,\n  boundary: string,\n) {\n  return {\n    async *[Symbol.asyncIterator]() {\n      try {\n        let buffer = '';\n\n        for await (const textChunk of streamBodyIterator) {\n          buffer += textChunk;\n\n          if (buffer.indexOf(boundary) > -1) {\n            const lastBoundaryIndex = buffer.lastIndexOf(boundary);\n            const fullResponses = buffer.slice(0, lastBoundaryIndex);\n\n            const chunkBodies = fullResponses\n              .split(boundary)\n              .filter((chunk) => chunk.trim().length > 0)\n              .map((chunk) => {\n                const body = chunk\n                  .slice(\n                    chunk.indexOf(HEADER_SEPARATOR) + HEADER_SEPARATOR.length,\n                  )\n                  .trim();\n                return body;\n              });\n\n            if (chunkBodies.length > 0) {\n              yield chunkBodies;\n            }\n\n            buffer = buffer.slice(lastBoundaryIndex + boundary.length);\n\n            if (buffer.trim() === `--`) {\n              buffer = '';\n            }\n          }\n        }\n      } catch (error) {\n        throw new Error(\n          `Error occured while processing stream payload - ${getErrorMessage(\n            error,\n          )}`,\n        );\n      }\n    },\n  };\n}\n\nfunction createJsonResponseAsyncIterator(response: Response) {\n  return {\n    async *[Symbol.asyncIterator]() {\n      const processedResponse = await processJSONResponse(response);\n\n      yield {\n        ...processedResponse,\n        hasNext: false,\n      };\n    },\n  };\n}\n\nfunction getResponseDataFromChunkBodies(chunkBodies: string[]): {\n  data: any;\n  errors?: any;\n  extensions?: any;\n  hasNext: boolean;\n}[] {\n  return chunkBodies\n    .map((value) => {\n      try {\n        return JSON.parse(value);\n      } catch (error) {\n        throw new Error(\n          `Error in parsing multipart response - ${getErrorMessage(error)}`,\n        );\n      }\n    })\n    .map((payload) => {\n      const {data, incremental, hasNext, extensions, errors} = payload;\n\n      // initial data chunk\n      if (!incremental) {\n        return {\n          data: data || {},\n          ...getKeyValueIfValid('errors', errors),\n          ...getKeyValueIfValid('extensions', extensions),\n          hasNext,\n        };\n      }\n\n      // subsequent data chunks\n      const incrementalArray: {data: any; errors?: any}[] = incremental.map(\n        ({data, path, errors}: any) => {\n          return {\n            data: data && path ? buildDataObjectByPath(path, data) : {},\n            ...getKeyValueIfValid('errors', errors),\n          };\n        },\n      );\n\n      return {\n        data:\n          incrementalArray.length === 1\n            ? incrementalArray[0].data\n            : buildCombinedDataObject([\n                ...incrementalArray.map(({data}) => data),\n              ]),\n        ...getKeyValueIfValid('errors', combineErrors(incrementalArray)),\n        hasNext,\n      };\n    });\n}\n\nfunction validateResponseData(\n  responseErrors: any[],\n  combinedData: ReturnType<typeof buildCombinedDataObject>,\n) {\n  if (responseErrors.length > 0) {\n    throw new Error(GQL_API_ERROR, {\n      cause: {\n        graphQLErrors: responseErrors,\n      },\n    });\n  }\n\n  if (Object.keys(combinedData).length === 0) {\n    throw new Error(NO_DATA_OR_ERRORS_ERROR);\n  }\n}\n\nfunction createMultipartResponseAsyncInterator(\n  response: Response,\n  responseContentType: string,\n) {\n  const boundaryHeader = (responseContentType ?? '').match(\n    BOUNDARY_HEADER_REGEX,\n  );\n  const boundary = `--${boundaryHeader ? boundaryHeader[1] : '-'}`;\n\n  if (\n    !response.body?.getReader &&\n    !(response.body as any)?.[Symbol.asyncIterator]\n  ) {\n    throw new Error('API multipart response did not return an iterable body', {\n      cause: response,\n    });\n  }\n\n  const streamBodyIterator = getStreamBodyIterator(response);\n\n  let combinedData: Record<string, any> = {};\n  let responseExtensions: Record<string, any> | undefined;\n\n  return {\n    async *[Symbol.asyncIterator]() {\n      try {\n        let streamHasNext = true;\n\n        for await (const chunkBodies of readStreamChunk(\n          streamBodyIterator,\n          boundary,\n        )) {\n          const responseData = getResponseDataFromChunkBodies(chunkBodies);\n\n          responseExtensions =\n            responseData.find((datum) => datum.extensions)?.extensions ??\n            responseExtensions;\n\n          const responseErrors = combineErrors(responseData);\n\n          combinedData = buildCombinedDataObject([\n            combinedData,\n            ...responseData.map(({data}) => data),\n          ]);\n\n          streamHasNext = responseData.slice(-1)[0].hasNext;\n\n          validateResponseData(responseErrors, combinedData);\n\n          yield {\n            ...getKeyValueIfValid('data', combinedData),\n            ...getKeyValueIfValid('extensions', responseExtensions),\n            hasNext: streamHasNext,\n          };\n        }\n\n        if (streamHasNext) {\n          throw new Error(`Response stream terminated unexpectedly`);\n        }\n      } catch (error) {\n        const cause = getErrorCause(error);\n\n        yield {\n          ...getKeyValueIfValid('data', combinedData),\n          ...getKeyValueIfValid('extensions', responseExtensions),\n          errors: {\n            message: formatErrorMessage(getErrorMessage(error)),\n            networkStatusCode: response.status,\n            ...getKeyValueIfValid('graphQLErrors', cause?.graphQLErrors),\n            response,\n          },\n          hasNext: false,\n        };\n      }\n    },\n  };\n}\n\nfunction generateRequestStream(\n  fetchFn: ReturnType<typeof generateFetch>,\n): GraphQLClient['requestStream'] {\n  return async (...props) => {\n    if (!DEFER_OPERATION_REGEX.test(props[0])) {\n      throw new Error(\n        formatErrorMessage(\n          'This operation does not result in a streamable response - use request() instead.',\n        ),\n      );\n    }\n\n    try {\n      const response = await fetchFn(...props);\n\n      const {statusText} = response;\n\n      if (!response.ok) {\n        throw new Error(statusText, {cause: response});\n      }\n\n      const responseContentType = response.headers.get('content-type') || '';\n\n      switch (true) {\n        case responseContentType.includes(CONTENT_TYPES.json):\n          return createJsonResponseAsyncIterator(response);\n        case responseContentType.includes(CONTENT_TYPES.multipart):\n          return createMultipartResponseAsyncInterator(\n            response,\n            responseContentType,\n          );\n        default:\n          throw new Error(\n            `${UNEXPECTED_CONTENT_TYPE_ERROR} ${responseContentType}`,\n            {cause: response},\n          );\n      }\n    } catch (error) {\n      return {\n        async *[Symbol.asyncIterator]() {\n          const response = getErrorCause(error);\n\n          yield {\n            errors: {\n              message: formatErrorMessage(getErrorMessage(error)),\n              ...getKeyValueIfValid('networkStatusCode', response?.status),\n              ...getKeyValueIfValid('response', response),\n            },\n            hasNext: false,\n          };\n        },\n      };\n    }\n  };\n}\n"], "names": ["CLIENT", "GQL_API_ERROR", "UNEXPECTED_CONTENT_TYPE_ERROR", "NO_DATA_OR_ERRORS_ERROR", "CONTENT_TYPES", "SDK_VARIANT_HEADER", "SDK_VERSION_HEADER", "DEFAULT_SDK_VARIANT", "DEFAULT_CLIENT_VERSION", "RETRIABLE_STATUS_CODES", "DEFER_OPERATION_REGEX", "BOUNDARY_HEADER_REGEX", "HEADER_SEPARATOR", "NEWLINE_SEPARATOR", "formatErrorMessage", "message", "client", "startsWith", "getErrorMessage", "error", "Error", "JSON", "stringify", "getErrorCause", "cause", "undefined", "combineErrors", "dataArray", "flatMap", "errors", "validateRetries", "retries", "getKeyValueIfValid", "key", "value", "Array", "isArray", "Object", "keys", "length", "buildDataObjectByPath", "path", "data", "pop", "newData", "combineObjects", "baseObject", "newObject", "reduce", "acc", "buildCombinedDataObject", "initialDatum", "remainingData", "generateHttpFetch", "clientLogger", "customFetchApi", "fetch", "defaultRetryWaitTime", "retriableCodes", "httpFetch", "async", "requestParams", "count", "maxRetries", "nextCount", "max<PERSON>ries", "response", "type", "content", "ok", "includes", "status", "deprecationNotice", "headers", "get", "retryAfter", "waitTime", "Promise", "resolve", "setTimeout", "sleep", "parseInt", "lastResponse", "retryAttempt", "processJSONResponse", "extensions", "json", "networkStatusCode", "getResponseDataFromChunkBodies", "chunkBodies", "map", "parse", "payload", "incremental", "hasNext", "incrementalArray", "validateResponseData", "responseErrors", "combinedData", "graphQLErrors", "url", "logger", "config", "logContent", "generateClientLogger", "fetchFn", "operation", "options", "variables", "overrideHeaders", "overrideUrl", "overrideRetries", "keepalive", "signal", "body", "query", "flatHeaders", "entries", "join", "toString", "method", "generateFetch", "request", "props", "test", "statusText", "contentType", "generateRequest", "requestStream", "responseContentType", "Symbol", "asyncIterator", "processedResponse", "createJsonResponseAsyncIterator", "<PERSON><PERSON><PERSON><PERSON>", "match", "boundary", "<PERSON><PERSON><PERSON><PERSON>", "streamBodyIterator", "decoder", "TextDecoder", "chunk", "decode", "reader", "readResult", "read", "done", "cancel", "getStreamBodyIterator", "responseExtensions", "streamHasNext", "buffer", "textChunk", "indexOf", "lastBoundaryIndex", "lastIndexOf", "slice", "split", "filter", "trim", "readStreamChunk", "responseData", "find", "datum", "createMultipartResponseAsyncInterator", "generateRequestStream"], "mappings": ";4PAAO,MAAMA,EAAS,iBAITC,EACX,qFACWC,EACX,6CACWC,EACX,qGAEWC,EACL,mBADKA,EAEA,kBAEAC,EAAqB,gBACrBC,EAAqB,gBAErBC,EAAsB,yBAEtBC,EAAyB,QAGzBC,EAAyB,CAAC,IAAK,KAC/BC,EAAwB,cAExBC,EAAwB,0BACxBC,EAAmBC,oBCzBhBC,EAAmBC,EAAiBC,EAAShB,GAC3D,OAAOe,EAAQE,WAAW,GAAGD,KAAYD,EAAU,GAAGC,MAAWD,GACnE,CAEM,SAAUG,EAAgBC,GAC9B,OAAOA,aAAiBC,MAAQD,EAAMJ,QAAUM,KAAKC,UAAUH,EACjE,CAEM,SAAUI,EAAcJ,GAC5B,OAAOA,aAAiBC,OAASD,EAAMK,MAAQL,EAAMK,WAAQC,CAC/D,CAEM,SAAUC,EAAcC,GAC5B,OAAOA,EAAUC,SAAQ,EAAEC,YAClBA,GAAU,IAErB,UAEgBC,GAAgBd,OAC9BA,EAAMe,QACNA,IAKA,QACcN,IAAZM,IACoB,iBAAZA,GACNA,ED7BqB,GC8BrBA,ED7BqB,GC+BvB,MAAM,IAAIX,MACR,GAAGJ,oCAAyCe,6DAGlD,CAEM,SAAUC,EAAmBC,EAAaC,GAC9C,OAAOA,IACa,iBAAVA,GACNC,MAAMC,QAAQF,IACI,iBAAVA,GAAsBG,OAAOC,KAAKJ,GAAOK,OAAS,GAC1D,CAACN,CAACA,GAAMC,GACR,CAAA,CACN,CAEM,SAAUM,EACdC,EACAC,GAEA,GAAoB,IAAhBD,EAAKF,OACP,OAAOG,EAGT,MAAMT,EAAMQ,EAAKE,MACXC,EAAU,CACdX,CAACA,GAAMS,GAGT,OAAoB,IAAhBD,EAAKF,OACAK,EAGFJ,EAAsBC,EAAMG,EACrC,CAEA,SAASC,EAAeC,EAAiBC,GACvC,OAAOV,OAAOC,KAAKS,GAAa,CAAA,GAAIC,QAClC,CAACC,EAAUhB,KAEoB,iBAAnBc,EAAUd,IAAqBE,MAAMC,QAAQW,EAAUd,MAC/Da,EAAWb,IAEXgB,EAAIhB,GAAOY,EAAeC,EAAWb,GAAMc,EAAUd,IAC9CgB,IAGTA,EAAIhB,GAAOc,EAAUd,GACdgB,IAETd,MAAMC,QAAQU,GAAc,IAAIA,GAAc,IAAIA,GAEtD,CAEM,SAAUI,GACdC,KACGC,IAEH,OAAOA,EAAcJ,OAAOH,EAAgB,IAAIM,GAClD,CC/EM,SAAUE,GAAkBC,aAChCA,EAAYC,eACZA,EAAiBC,MAAKxC,OACtBA,EAAShB,EAAMyD,qBACfA,EFM6B,IENSC,eACtCA,EAAiBjD,IAEjB,MAAMkD,EAAYC,MAChBC,EACAC,EACAC,KAEA,MAAMC,EAAYF,EAAQ,EACpBG,EAAWF,EAAa,EAC9B,IAAIG,EAEJ,IAWE,GAVAA,QAAiBX,KAAkBM,GAEnCP,EAAa,CACXa,KAAM,gBACNC,QAAS,CACPP,gBACAK,eAKDA,EAASG,IACVX,EAAeY,SAASJ,EAASK,SACjCP,GAAaC,EAEb,MAAM,IAAI7C,MAGZ,MAAMoD,EACJN,GAAUO,QAAQC,IAAI,oCAAsC,GAW9D,OAVIF,GACFlB,EAAa,CACXa,KAAM,2CACNC,QAAS,CACPP,gBACAW,uBAKCN,CACT,CAAE,MAAO/C,GACP,GAAI6C,GAAaC,EAAU,CACzB,MAAMU,EAAaT,GAAUO,QAAQC,IAAI,eAezC,aAmBRd,eAAqBgB,GACnB,OAAO,IAAIC,SAASC,GAAYC,WAAWD,EAASF,IACtD,CAnCcI,CACJL,EAAaM,SAASN,EAAY,IAAMlB,GAG1CH,EAAa,CACXa,KAAM,aACNC,QAAS,CACPP,gBACAqB,aAAchB,EACdiB,aAAcrB,EACdC,gBAIGJ,EAAUE,EAAeG,EAAWD,EAC7C,CAEA,MAAM,IAAI3C,MACRN,EACE,GACEiD,EAAa,EACT,+BAA+BA,qCAC/B,KACH7C,EAAgBC,KACnBH,GAGN,GAGF,OAAO2C,CACT,CChBAC,eAAewB,EACblB,GAEA,MAAMrC,OAACA,EAAMa,KAAEA,EAAI2C,WAAEA,SAAoBnB,EAASoB,OAElD,MAAO,IACFtD,EAAmB,OAAQU,MAC3BV,EAAmB,aAAcqD,GACpCZ,QAASP,EAASO,WAEd5C,IAAWa,EACX,CACEb,OAAQ,CACN0D,kBAAmBrB,EAASK,OAC5BxD,QAASD,EACPe,EAAS5B,EAAgBE,MAExB6B,EAAmB,gBAAiBH,GACvCqC,aAGJ,GAER,CAmMA,SAASsB,EAA+BC,GAMtC,OAAOA,EACJC,KAAKxD,IACJ,IACE,OAAOb,KAAKsE,MAAMzD,EACpB,CAAE,MAAOf,GACP,MAAM,IAAIC,MACR,yCAAyCF,EAAgBC,KAE7D,KAEDuE,KAAKE,IACJ,MAAMlD,KAACA,EAAImD,YAAEA,EAAWC,QAAEA,EAAOT,WAAEA,EAAUxD,OAAEA,GAAU+D,EAGzD,IAAKC,EACH,MAAO,CACLnD,KAAMA,GAAQ,CAAA,KACXV,EAAmB,SAAUH,MAC7BG,EAAmB,aAAcqD,GACpCS,WAKJ,MAAMC,EAAgDF,EAAYH,KAChE,EAAEhD,OAAMD,OAAMZ,aACL,CACLa,KAAMA,GAAQD,EAAOD,EAAsBC,EAAMC,GAAQ,CAAA,KACtDV,EAAmB,SAAUH,OAKtC,MAAO,CACLa,KAC8B,IAA5BqD,EAAiBxD,OACbwD,EAAiB,GAAGrD,KACpBQ,EAAwB,IACnB6C,EAAiBL,KAAI,EAAEhD,UAAUA,SAEzCV,EAAmB,SAAUN,EAAcqE,IAC9CD,UACD,GAEP,CAEA,SAASE,EACPC,EACAC,GAEA,GAAID,EAAe1D,OAAS,EAC1B,MAAM,IAAInB,MAAMnB,EAAe,CAC7BuB,MAAO,CACL2E,cAAeF,KAKrB,GAAyC,IAArC5D,OAAOC,KAAK4D,GAAc3D,OAC5B,MAAM,IAAInB,MAAMjB,EAEpB,iCAtUoCsE,QAClCA,EAAO2B,IACPA,EAAG7C,eACHA,EAAiBC,MAAKzB,QACtBA,EAAU,EAACsE,OACXA,IAEAvE,EAAgB,CAACd,OAAQhB,EAAQ+B,YAEjC,MAAMuE,EAAuB,CAC3B7B,UACA2B,MACArE,WAGIuB,EAkBF,SAA+B+C,GACnC,OAAQE,IACFF,GACFA,EAAOE,EACT,CAEJ,CAxBuBC,CAAqBH,GAMpCI,EA6CR,SACE9C,GACAyC,IAACA,EAAG3B,QAAEA,EAAO1C,QAAEA,IAEf,OAAO6B,MAAO8C,EAAWC,EAAU,MACjC,MAAMC,UACJA,EACAnC,QAASoC,EACTT,IAAKU,EACL/E,QAASgF,EAAeC,UACxBA,EAASC,OACTA,GACEN,EAEEO,EAAO7F,KAAKC,UAAU,CAC1B6F,MAAOT,EACPE,cAGF9E,EAAgB,CAACd,OAAQhB,EAAQ+B,QAASgF,IAE1C,MAAMK,EAAc/E,OAAOgF,QAAQ,IAC9B5C,KACAoC,IACF7D,QAAO,CAACyB,GAAkCxC,EAAKC,MAChDuC,EAAQxC,GAAOE,MAAMC,QAAQF,GAASA,EAAMoF,KAAK,MAAQpF,EAAMqF,WACxD9C,IACN,CAAA,GAEE2C,EAAY/G,IAAwB+G,EAAY9G,KACnD8G,EAAY/G,GAAsBE,EAClC6G,EAAY9G,GAAsBE,GAcpC,OAAOmD,EAXyC,CAC9CmD,GAAeV,EACf,CACEoB,OAAQ,OACR/C,QAAS2C,EACTF,OACAD,SACAD,cAI0B,EAAGD,GAAmBhF,EAAQ,CAEhE,CA5FkB0F,CALEpE,EAAkB,CAClCE,iBACAD,eACAG,qBHlC2B,MGoCY6C,GACnCoB,EA6FR,SACEjB,GAEA,OAAO7C,SAAU+D,KACf,GAAIjH,EAAsBkH,KAAKD,EAAM,IACnC,MAAM,IAAIvG,MACRN,EACE,uFAKN,IAAIoD,EAA4B,KAChC,IACEA,QAAiBuC,KAAWkB,GAC5B,MAAMpD,OAACA,EAAMsD,WAAEA,GAAc3D,EACvB4D,EAAc5D,EAASO,QAAQC,IAAI,iBAAmB,GAE5D,OAAKR,EAASG,GAUTyD,EAAYxD,SAASlE,SAYbgF,EAAoBlB,GAXxB,CACLrC,OAAQ,CACN0D,kBAAmBhB,EACnBxD,QAASD,EACP,GAAGZ,KAAiC4H,KAEtC5D,aAhBG,CACLrC,OAAQ,CACN0D,kBAAmBhB,EACnBxD,QAASD,EAAmB+G,GAC5B3D,YAkBR,CAAE,MAAO/C,GACP,MAAO,CACLU,OAAQ,CACNd,QAASG,EAAgBC,MACT,MAAZ+C,EACA,CAAA,EACA,CACEqB,kBAAmBrB,EAASK,OAC5BL,aAIZ,EAEJ,CApJkB6D,CAAgBtB,GAC1BuB,EA+XR,SACEvB,GAEA,OAAO7C,SAAU+D,KACf,IAAKjH,EAAsBkH,KAAKD,EAAM,IACpC,MAAM,IAAIvG,MACRN,EACE,qFAKN,IACE,MAAMoD,QAAiBuC,KAAWkB,IAE5BE,WAACA,GAAc3D,EAErB,IAAKA,EAASG,GACZ,MAAM,IAAIjD,MAAMyG,EAAY,CAACrG,MAAO0C,IAGtC,MAAM+D,EAAsB/D,EAASO,QAAQC,IAAI,iBAAmB,GAEpE,QAAQ,GACN,KAAKuD,EAAoB3D,SAASlE,GAChC,OAzLV,SAAyC8D,GACvC,MAAO,CACL,OAAQgE,OAAOC,iBACb,MAAMC,QAA0BhD,EAAoBlB,QAE9C,IACDkE,EACHtC,SAAS,EAEb,EAEJ,CA8KiBuC,CAAgCnE,GACzC,KAAK+D,EAAoB3D,SAASlE,GAChC,OAzGV,SACE8D,EACA+D,GAEA,MAAMK,GAAkBL,GAAuB,IAAIM,MACjD5H,GAEI6H,EAAW,KAAKF,EAAiBA,EAAe,GAAK,MAE3D,IACGpE,EAASgD,MAAMuB,YACdvE,EAASgD,OAAegB,OAAOC,eAEjC,MAAM,IAAI/G,MAAM,yDAA0D,CACxEI,MAAO0C,IAIX,MAAMwE,EA9KR9E,gBACEM,GAEA,MAAMyE,EAAU,IAAIC,YAGpB,GAAK1E,EAASgD,KAAcgB,OAAOC,eACjC,UAAW,MAAMU,KAAS3E,EAASgD,WAC3ByB,EAAQG,OAAOD,OAElB,CACL,MAAME,EAAS7E,EAASgD,KAAMuB,YAE9B,IAAIO,EACJ,IACE,OAASA,QAAmBD,EAAOE,QAAQC,YACnCP,EAAQG,OAAOE,EAAW9G,MAEpC,SACE6G,EAAOI,QACT,CACF,CACF,CAwJ6BC,CAAsBlF,GAEjD,IACImF,EADAnD,EAAoC,CAAA,EAGxC,MAAO,CACL,OAAQgC,OAAOC,iBACb,IACE,IAAImB,GAAgB,EAEpB,UAAW,MAAM7D,KAhKzB,SACEiD,EACAF,GAEA,MAAO,CACL,OAAQN,OAAOC,iBACb,IACE,IAAIoB,EAAS,GAEb,UAAW,MAAMC,KAAad,EAG5B,GAFAa,GAAUC,EAEND,EAAOE,QAAQjB,IAAa,EAAG,CACjC,MAAMkB,EAAoBH,EAAOI,YAAYnB,GAGvC/C,EAFgB8D,EAAOK,MAAM,EAAGF,GAGnCG,MAAMrB,GACNsB,QAAQjB,GAAUA,EAAMkB,OAAOxH,OAAS,IACxCmD,KAAKmD,GACSA,EACVe,MACCf,EAAMY,QAAQ7I,GAAoBA,GAEnCmJ,SAIHtE,EAAYlD,OAAS,UACjBkD,GAGR8D,EAASA,EAAOK,MAAMF,EAAoBlB,EAASjG,QAE7B,OAAlBgH,EAAOQ,SACTR,EAAS,GAEb,CAEJ,CAAE,MAAOpI,GACP,MAAM,IAAIC,MACR,mDAAmDF,EACjDC,KAGN,CACF,EAEJ,CAgHwC6I,CAC9BtB,EACAF,GACC,CACD,MAAMyB,EAAezE,EAA+BC,GAEpD4D,EACEY,EAAaC,MAAMC,GAAUA,EAAM9E,cAAaA,YAChDgE,EAEF,MAAMpD,EAAiBvE,EAAcuI,GAErC/D,EAAehD,EAAwB,CACrCgD,KACG+D,EAAavE,KAAI,EAAEhD,UAAUA,MAGlC4G,EAAgBW,EAAaL,OAAO,GAAG,GAAG9D,QAE1CE,EAAqBC,EAAgBC,QAE/B,IACDlE,EAAmB,OAAQkE,MAC3BlE,EAAmB,aAAcqH,GACpCvD,QAASwD,EAEb,CAEA,GAAIA,EACF,MAAM,IAAIlI,MAAM,0CAEpB,CAAE,MAAOD,GACP,MAAMK,EAAQD,EAAcJ,QAEtB,IACDa,EAAmB,OAAQkE,MAC3BlE,EAAmB,aAAcqH,GACpCxH,OAAQ,CACNd,QAASD,EAAmBI,EAAgBC,IAC5CoE,kBAAmBrB,EAASK,UACzBvC,EAAmB,gBAAiBR,GAAO2E,eAC9CjC,YAEF4B,SAAS,EAEb,CACF,EAEJ,CA6BiBsE,CACLlG,EACA+D,GAEJ,QACE,MAAM,IAAI7G,MACR,GAAGlB,KAAiC+H,IACpC,CAACzG,MAAO0C,IAGhB,CAAE,MAAO/C,GACP,MAAO,CACL,OAAQ+G,OAAOC,iBACb,MAAMjE,EAAW3C,EAAcJ,QAEzB,CACJU,OAAQ,CACNd,QAASD,EAAmBI,EAAgBC,OACzCa,EAAmB,oBAAqBkC,GAAUK,WAClDvC,EAAmB,WAAYkC,IAEpC4B,SAAS,EAEb,EAEJ,EAEJ,CArbwBuE,CAAsB5D,GAE5C,MAAO,CACLH,SACA9C,MAAOiD,EACPiB,UACAM,gBAEJ"}
{"name": "@shopify/graphql-client", "version": "1.4.1", "description": "Shopify GraphQL Client - A lightweight generic GraphQL JS client to interact with Shopify GraphQL APIs", "repository": {"type": "git", "url": "git+https://github.com/Shopify/shopify-app-js.git", "directory": "packages/api-clients/graphql-client"}, "author": "Shopify", "license": "MIT", "main": "./dist/umd/graphql-client.min.js", "browser": "./dist/umd/graphql-client.min.js", "module": "./dist/index.mjs", "types": "./dist/graphql-client.d.ts", "exports": {".": {"module": {"types": "./dist/ts/index.d.ts", "default": "./dist/index.mjs"}, "import": {"types": "./dist/ts/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/ts/index.d.ts", "default": "./dist/index.js"}, "default": "./dist/index.mjs"}}, "jest": {"projects": [{"displayName": {"name": "test:browser", "color": "blue"}, "testEnvironment": "jsdom", "testPathIgnorePatterns": ["(/__tests__/.*|(\\.|/)server(\\.|/)(test|spec))\\.[jt]sx?$"]}, {"displayName": {"name": "test:server", "color": "yellow"}, "testPathIgnorePatterns": ["(/__tests__/.*|(\\.|/)browser(\\.|/)(test|spec))\\.[jt]sx?$"]}], "setupFilesAfterEnv": ["./src/tests/setupTests.ts"], "transform": {".*": "babel-jest"}}, "publishConfig": {"access": "public"}, "keywords": ["shopify", "node", "graphql", "Admin API", "Storefront API"], "files": ["dist/**/*.*", "!node_modules"], "dependencies": {}, "devDependencies": {"jest-environment-jsdom": "^30.0.4", "jest-fetch-mock": "^3.0.3", "regenerator-runtime": "^0.14.1", "web-streams-polyfill": "^4.1.0"}, "bugs": {"url": "https://github.com/Shopify/shopify-app-js/issues"}, "homepage": "https://github.com/Shopify/shopify-app-js/tree/main/packages/api-clients/graphql-client#readme", "scripts": {"lint": "eslint . --ext .js,.ts", "build": "pnpm tsc -p ./tsconfig.build.json && pnpm rollup", "tsc": "tsc", "test": "jest", "test:ci": "pnpm test", "rollup": "rollup -c --bundleConfigAsCjs", "clean": "rimraf dist/*", "changeset": "changeset", "version": "changeset version", "release": "pnpm build && changeset publish"}}
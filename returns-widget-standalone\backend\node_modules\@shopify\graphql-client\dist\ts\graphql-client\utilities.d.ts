export declare function formatErrorMessage(message: string, client?: string): string;
export declare function getErrorMessage(error: any): string;
export declare function getErrorCause(error: any): Record<string, any> | undefined;
export declare function combineErrors(dataArray: Record<string, any>[]): any[];
export declare function validateRetries({ client, retries, }: {
    client: string;
    retries?: number;
}): void;
export declare function getKeyValueIfValid(key: string, value?: any): {
    [key]: any;
};
export declare function buildDataObjectByPath(path: string[], data: any): Record<string | number, any>;
export declare function buildCombinedDataObject([initialDatum, ...remainingData]: any[]): any;
//# sourceMappingURL=utilities.d.ts.map
{"version": 3, "file": "types.js", "sources": ["../../../../../../lib/utils/types.ts"], "sourcesContent": ["import {AdapterArgs} from '../../runtime/types';\n\nexport enum HmacValidationType {\n  Flow = 'flow',\n  Webhook = 'webhook',\n  FulfillmentService = 'fulfillment_service',\n}\n\nexport interface ValidateParams extends AdapterArgs {\n  /**\n   * The raw body of the request.\n   */\n  rawBody: string;\n}\n\nexport const ValidationErrorReason = {\n  MissingBody: 'missing_body',\n  InvalidHmac: 'invalid_hmac',\n  MissingHmac: 'missing_hmac',\n} as const;\n\nexport type ValidationErrorReasonType =\n  (typeof ValidationErrorReason)[keyof typeof ValidationErrorReason];\n\nexport interface ValidationInvalid {\n  /**\n   * Whether the request is a valid Flow request from Shopify.\n   */\n  valid: false;\n  /**\n   * The reason why the request is not valid.\n   */\n  reason: ValidationErrorReasonType;\n}\n\nexport interface ValidationValid {\n  /**\n   * Whether the request is a valid request from Shopify.\n   */\n  valid: true;\n}\n"], "names": ["HmacValidationType"], "mappings": ";;AAEYA;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC5B,IAAA,kBAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,kBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,kBAAA,CAAA,oBAAA,CAAA,GAAA,qBAA0C;AAC5C,CAAC,EAJWA,0BAAkB,KAAlBA,0BAAkB,GAAA,EAAA,CAAA,CAAA;AAavB,MAAM,qBAAqB,GAAG;AACnC,IAAA,WAAW,EAAE,cAAc;AAC3B,IAAA,WAAW,EAAE,cAAc;AAC3B,IAAA,WAAW,EAAE,cAAc;;;;;"}
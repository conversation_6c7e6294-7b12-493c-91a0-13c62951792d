{"version": 3, "file": "decode-session-token.js", "sources": ["../../../../../../lib/session/decode-session-token.ts"], "sourcesContent": ["import * as jose from 'jose';\n\nimport {ConfigInterface} from '../base-types';\nimport * as ShopifyErrors from '../error';\nimport {getHMACKey} from '../utils/get-hmac-key';\n\nimport {JwtPayload} from './types';\n\nconst JWT_PERMITTED_CLOCK_TOLERANCE = 10;\n\nexport interface DecodeSessionTokenOptions {\n  checkAudience?: boolean;\n}\n\nexport function decodeSessionToken(config: ConfigInterface) {\n  return async (\n    token: string,\n    {checkAudience = true}: DecodeSessionTokenOptions = {},\n  ): Promise<JwtPayload> => {\n    let payload: JwtPayload;\n    try {\n      payload = (\n        await jose.jwtVerify(token, getHMACKey(config.apiSecretKey), {\n          algorithms: ['HS256'],\n          clockTolerance: JWT_PERMITTED_CLOCK_TOLERANCE,\n        })\n      ).payload as unknown as JwtPayload;\n    } catch (error) {\n      throw new ShopifyErrors.InvalidJwtError(\n        `Failed to parse session token '${token}': ${error.message}`,\n      );\n    }\n\n    // The exp and nbf fields are validated by the JWT library\n\n    if (checkAudience && payload.aud !== config.apiKey) {\n      throw new ShopifyErrors.InvalidJwtError(\n        'Session token had invalid API key',\n      );\n    }\n\n    return payload;\n  };\n}\n"], "names": ["jose", "getHMACKey", "error", "ShopifyErrors.InvalidJwtError"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAQA,MAAM,6BAA6B,GAAG,EAAE;AAMlC,SAAU,kBAAkB,CAAC,MAAuB,EAAA;IACxD,OAAO,OACL,KAAa,EACb,EAAC,aAAa,GAAG,IAAI,EAAA,GAA+B,EAAE,KAC/B;AACvB,QAAA,IAAI,OAAmB;AACvB,QAAA,IAAI;AACF,YAAA,OAAO,GAAG,CACR,MAAMA,eAAI,CAAC,SAAS,CAAC,KAAK,EAAEC,qBAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;gBAC3D,UAAU,EAAE,CAAC,OAAO,CAAC;AACrB,gBAAA,cAAc,EAAE,6BAA6B;aAC9C,CAAC,EACF,OAAgC;QACpC;QAAE,OAAOC,OAAK,EAAE;AACd,YAAA,MAAM,IAAIC,qBAA6B,CACrC,CAAA,+BAAA,EAAkC,KAAK,CAAA,GAAA,EAAMD,OAAK,CAAC,OAAO,CAAA,CAAE,CAC7D;QACH;;QAIA,IAAI,aAAa,IAAI,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,MAAM,EAAE;AAClD,YAAA,MAAM,IAAIC,qBAA6B,CACrC,mCAAmC,CACpC;QACH;AAEA,QAAA,OAAO,OAAO;AAChB,IAAA,CAAC;AACH;;;;"}
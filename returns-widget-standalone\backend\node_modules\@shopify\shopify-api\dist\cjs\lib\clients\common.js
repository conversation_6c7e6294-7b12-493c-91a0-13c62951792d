'use strict';

var network = require('@shopify/network');
var error = require('../error.js');
var types = require('../types.js');
var version = require('../version.js');
require('../../runtime/crypto/crypto.js');
require('../../runtime/crypto/types.js');
var headers = require('../../runtime/http/headers.js');
var runtimeString = require('../../runtime/platform/runtime-string.js');
var index = require('../logger/index.js');

function getUserAgent(config) {
    let userAgentPrefix = `${types.LIBRARY_NAME} v${version.SHOPIFY_API_LIBRARY_VERSION} | ${runtimeString.abstractRuntimeString()}`;
    if (config.userAgentPrefix) {
        userAgentPrefix = `${config.userAgentPrefix} | ${userAgentPrefix}`;
    }
    return userAgentPrefix;
}
function serializeResponse(response) {
    if (!response) {
        return { error: 'No response object provided' };
    }
    try {
        const { status, statusText, ok, redirected, type, url, headers } = response;
        const serialized = {
            status,
            statusText,
            ok,
            redirected,
            type,
            url,
        };
        if (headers?.entries) {
            serialized.headers = Object.fromEntries(headers.entries());
        }
        else if (headers) {
            serialized.headers = headers;
        }
        return serialized;
    }
    catch {
        return response;
    }
}
function clientLoggerFactory(config) {
    return (logContent) => {
        if (config.logger.httpRequests) {
            switch (logContent.type) {
                case 'HTTP-Response': {
                    const responseLog = logContent.content;
                    index.logger(config).debug('Received response for HTTP request', {
                        requestParams: JSON.stringify(responseLog.requestParams),
                        response: JSON.stringify(serializeResponse(responseLog.response)),
                    });
                    break;
                }
                case 'HTTP-Retry': {
                    const responseLog = logContent.content;
                    index.logger(config).debug('Retrying HTTP request', {
                        requestParams: JSON.stringify(responseLog.requestParams),
                        retryAttempt: responseLog.retryAttempt,
                        maxRetries: responseLog.maxRetries,
                        response: responseLog.lastResponse
                            ? JSON.stringify(serializeResponse(responseLog.lastResponse))
                            : 'undefined',
                    });
                    break;
                }
                case 'HTTP-Response-GraphQL-Deprecation-Notice': {
                    const responseLog = logContent.content;
                    index.logger(config).debug('Received response containing Deprecated GraphQL Notice', {
                        requestParams: JSON.stringify(responseLog.requestParams),
                        deprecationNotice: responseLog.deprecationNotice,
                    });
                    break;
                }
                default: {
                    index.logger(config).debug(`HTTP request event: ${logContent.content}`);
                    break;
                }
            }
        }
    };
}
function throwFailedRequest(body, atMaxRetries, response) {
    if (typeof response === 'undefined') {
        const message = body?.errors?.message ?? '';
        throw new error.HttpRequestError(`Http request error, no response available: ${message}`);
    }
    const responseHeaders = headers.canonicalizeHeaders(Object.fromEntries(response.headers.entries() ?? []));
    if (response.status === network.StatusCode.Ok && body.errors.graphQLErrors) {
        throw new error.GraphqlQueryError({
            message: body.errors.graphQLErrors?.[0].message ?? 'GraphQL operation failed',
            response: response,
            headers: responseHeaders,
            body: body,
        });
    }
    const errorMessages = [];
    if (body.errors) {
        errorMessages.push(JSON.stringify(body.errors, null, 2));
    }
    const xRequestId = headers.getHeader(responseHeaders, 'x-request-id');
    if (xRequestId) {
        errorMessages.push(`If you report this error, please include this id: ${xRequestId}`);
    }
    const errorMessage = errorMessages.length
        ? `:\n${errorMessages.join('\n')}`
        : '';
    const code = response.status;
    const statusText = response.statusText;
    switch (true) {
        case response.status === network.StatusCode.TooManyRequests: {
            if (atMaxRetries) {
                throw new error.HttpMaxRetriesError('Attempted the maximum number of retries for HTTP request.');
            }
            else {
                const retryAfter = headers.getHeader(responseHeaders, 'Retry-After');
                throw new error.HttpThrottlingError({
                    message: `Shopify is throttling requests ${errorMessage}`,
                    code,
                    statusText,
                    body,
                    headers: responseHeaders,
                    retryAfter: retryAfter ? parseFloat(retryAfter) : undefined,
                });
            }
        }
        case response.status >= network.StatusCode.InternalServerError:
            if (atMaxRetries) {
                throw new error.HttpMaxRetriesError('Attempted the maximum number of retries for HTTP request.');
            }
            else {
                throw new error.HttpInternalError({
                    message: `Shopify internal error${errorMessage}`,
                    code,
                    statusText,
                    body,
                    headers: responseHeaders,
                });
            }
        default:
            throw new error.HttpResponseError({
                message: `Received an error response (${response.status} ${response.statusText}) from Shopify${errorMessage}`,
                code,
                statusText,
                body,
                headers: responseHeaders,
            });
    }
}

exports.clientLoggerFactory = clientLoggerFactory;
exports.getUserAgent = getUserAgent;
exports.throwFailedRequest = throwFailedRequest;
//# sourceMappingURL=common.js.map

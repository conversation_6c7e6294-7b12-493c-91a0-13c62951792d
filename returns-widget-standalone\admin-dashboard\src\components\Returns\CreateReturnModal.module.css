/* Modal Overlay */
.modalOverlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  padding: 1rem;
}

/* Modal Container */
.modalContainer {
  width: 100%;
  max-width: 56rem; /* 896px */
  max-height: 90vh;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.modalHeader {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.headerContent h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.headerContent p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0 0;
}

.closeButton {
  padding: 0.5rem;
  color: #6b7280;
  border: none;
  background: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: color 0.2s;
}

.closeButton:hover {
  color: #374151;
}

/* Content */
.modalContent {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
}

/* Items Grid */
.itemsGrid {
  display: grid;
  gap: 1rem;
}

.itemCard {
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s;
}

.itemCard.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.itemCardHeader {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.itemCheckbox {
  margin-top: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  accent-color: #3b82f6;
}

.itemIcon {
  margin-top: 0.25rem;
  color: #6b7280;
  flex-shrink: 0;
}

.itemDetails {
  flex: 1;
}

.itemDetailsHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.itemTitle {
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.itemPrice {
  font-weight: 600;
  color: #111827;
  font-size: 1.125rem;
}

.itemMeta {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0;
}

/* Item Controls */
.itemControls {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  gap: 1rem;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .itemControls {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.formSelect,
.formInput {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.formSelect:focus,
.formInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Notes Section */
.notesSection {
  margin-top: 2rem;
}

.notesTextarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 80px;
  transition: all 0.2s;
}

.notesTextarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Alert Messages */
.errorAlert {
  padding: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summaryAlert {
  padding: 1rem;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  color: #1d4ed8;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.summaryTitle {
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.summaryText {
  font-size: 0.875rem;
  margin: 0;
}

/* Footer Actions */
.modalFooter {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.cancelButton {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton:hover:not(:disabled) {
  background-color: #f9fafb;
}

.submitButton {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  background-color: #3b82f6;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.submitButton:hover:not(:disabled) {
  background-color: #2563eb;
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading State */
.loadingSpinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .modalOverlay {
    padding: 0.5rem;
  }
  
  .modalContainer {
    max-height: 95vh;
  }
  
  .modalHeader,
  .modalContent,
  .modalFooter {
    padding: 1rem;
  }
  
  .itemCardHeader {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .modalFooter {
    flex-direction: column;
  }
  
  .cancelButton,
  .submitButton {
    width: 100%;
  }
}

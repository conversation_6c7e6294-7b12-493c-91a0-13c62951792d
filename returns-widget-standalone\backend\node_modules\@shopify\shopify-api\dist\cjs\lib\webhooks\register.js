'use strict';

var client = require('../clients/admin/graphql/client.js');
require('@shopify/admin-api-client');
require('@shopify/network');
var error = require('../error.js');
var types = require('../types.js');
require('../../runtime/crypto/crypto.js');
require('../../runtime/crypto/types.js');
var index = require('../logger/index.js');
var registry = require('./registry.js');
var queryTemplate = require('./query-template.js');
var types$1 = require('./types.js');

function register(config, webhookRegistry) {
    return async function register({ session, }) {
        const log = index.logger(config);
        log.info('Registering webhooks', { shop: session.shop });
        const registerReturn = Object.keys(webhookRegistry).reduce((acc, topic) => {
            acc[topic] = [];
            return acc;
        }, {});
        const existingHandlers = await getExistingHandlers(config, session);
        log.debug(`Existing topics: [${Object.keys(existingHandlers).join(', ')}]`, { shop: session.shop });
        for (const topic in webhookRegistry) {
            if (!Object.prototype.hasOwnProperty.call(webhookRegistry, topic)) {
                continue;
            }
            if (types.privacyTopics.includes(topic)) {
                continue;
            }
            registerReturn[topic] = await registerTopic({
                config,
                session,
                topic,
                existingHandlers: existingHandlers[topic] || [],
                handlers: registry.getHandlers(webhookRegistry)(topic),
            });
            // Remove this topic from the list of existing handlers so we have a list of leftovers
            delete existingHandlers[topic];
        }
        // Delete any leftover handlers
        for (const topic in existingHandlers) {
            if (!Object.prototype.hasOwnProperty.call(existingHandlers, topic)) {
                continue;
            }
            const GraphqlClient = client.graphqlClientClass({ config });
            const client$1 = new GraphqlClient({ session });
            registerReturn[topic] = await runMutations({
                config,
                client: client$1,
                topic,
                handlers: existingHandlers[topic],
                operation: types$1.WebhookOperation.Delete,
            });
        }
        return registerReturn;
    };
}
async function getExistingHandlers(config, session) {
    const GraphqlClient = client.graphqlClientClass({ config });
    const client$1 = new GraphqlClient({ session });
    const existingHandlers = {};
    let hasNextPage;
    let endCursor = null;
    do {
        const query = buildCheckQuery(endCursor);
        const response = await client$1.request(query);
        response.data?.webhookSubscriptions?.edges.forEach((edge) => {
            const handler = buildHandlerFromNode(edge);
            if (!existingHandlers[edge.node.topic]) {
                existingHandlers[edge.node.topic] = [];
            }
            existingHandlers[edge.node.topic].push(handler);
        });
        endCursor = response.data?.webhookSubscriptions?.pageInfo.endCursor;
        hasNextPage = response.data?.webhookSubscriptions?.pageInfo.hasNextPage;
    } while (hasNextPage);
    return existingHandlers;
}
function buildCheckQuery(endCursor) {
    return queryTemplate.queryTemplate(TEMPLATE_GET_HANDLERS, {
        END_CURSOR: JSON.stringify(endCursor),
    });
}
function buildHandlerFromNode(edge) {
    const endpoint = edge.node.endpoint;
    let handler;
    switch (endpoint.__typename) {
        case 'WebhookHttpEndpoint':
            handler = {
                deliveryMethod: types$1.DeliveryMethod.Http,
                callbackUrl: endpoint.callbackUrl,
                // This is a dummy for now because we don't really care about it
                callback: async () => { },
            };
            break;
        case 'WebhookEventBridgeEndpoint':
            handler = {
                deliveryMethod: types$1.DeliveryMethod.EventBridge,
                arn: endpoint.arn,
            };
            break;
        case 'WebhookPubSubEndpoint':
            handler = {
                deliveryMethod: types$1.DeliveryMethod.PubSub,
                pubSubProject: endpoint.pubSubProject,
                pubSubTopic: endpoint.pubSubTopic,
            };
            break;
    }
    // Set common fields
    handler.id = edge.node.id;
    handler.includeFields = edge.node.includeFields;
    handler.metafieldNamespaces = edge.node.metafieldNamespaces;
    // Sort the array fields to make them cheaper to compare later on
    handler.includeFields?.sort();
    handler.metafieldNamespaces?.sort();
    return handler;
}
async function registerTopic({ config, session, topic, existingHandlers, handlers, }) {
    let registerResults = [];
    const { toCreate, toUpdate, toDelete } = categorizeHandlers(config, existingHandlers, handlers);
    const GraphqlClient = client.graphqlClientClass({ config });
    const client$1 = new GraphqlClient({ session });
    let operation = types$1.WebhookOperation.Create;
    registerResults = registerResults.concat(await runMutations({ config, client: client$1, topic, operation, handlers: toCreate }));
    operation = types$1.WebhookOperation.Update;
    registerResults = registerResults.concat(await runMutations({ config, client: client$1, topic, operation, handlers: toUpdate }));
    operation = types$1.WebhookOperation.Delete;
    registerResults = registerResults.concat(await runMutations({ config, client: client$1, topic, operation, handlers: toDelete }));
    return registerResults;
}
function categorizeHandlers(config, existingHandlers, handlers) {
    const handlersByKey = handlers.reduce((acc, value) => {
        acc[registry.handlerIdentifier(config, value)] = value;
        return acc;
    }, {});
    const existingHandlersByKey = existingHandlers.reduce((acc, value) => {
        acc[registry.handlerIdentifier(config, value)] = value;
        return acc;
    }, {});
    const toCreate = { ...handlersByKey };
    const toUpdate = {};
    const toDelete = {};
    for (const existingKey in existingHandlersByKey) {
        if (!Object.prototype.hasOwnProperty.call(existingHandlersByKey, existingKey)) {
            continue;
        }
        const existingHandler = existingHandlersByKey[existingKey];
        const handler = handlersByKey[existingKey];
        if (existingKey in handlersByKey) {
            delete toCreate[existingKey];
            if (!areHandlerFieldsEqual(existingHandler, handler)) {
                toUpdate[existingKey] = handler;
                toUpdate[existingKey].id = existingHandler.id;
            }
        }
        else {
            toDelete[existingKey] = existingHandler;
        }
    }
    return {
        toCreate: Object.values(toCreate),
        toUpdate: Object.values(toUpdate),
        toDelete: Object.values(toDelete),
    };
}
function areHandlerFieldsEqual(arr1, arr2) {
    const includeFieldsEqual = arraysEqual(arr1.includeFields || [], arr2.includeFields || []);
    const metafieldNamespacesEqual = arraysEqual(arr1.metafieldNamespaces || [], arr2.metafieldNamespaces || []);
    return includeFieldsEqual && metafieldNamespacesEqual;
}
function arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) {
        return false;
    }
    for (let i = 0; i < arr1.length; i++) {
        if (arr1[i] !== arr2[i]) {
            return false;
        }
    }
    return true;
}
async function runMutations({ config, client, topic, handlers, operation, }) {
    const registerResults = [];
    for (const handler of handlers) {
        registerResults.push(await runMutation({ config, client, topic, handler, operation }));
    }
    return registerResults;
}
async function runMutation({ config, client, topic, handler, operation, }) {
    let registerResult;
    index.logger(config).debug(`Running webhook mutation`, { topic, operation });
    try {
        const query = buildMutation(config, topic, handler, operation);
        const result = await client.request(query);
        registerResult = {
            deliveryMethod: handler.deliveryMethod,
            success: isSuccess(result, handler, operation),
            result,
            operation,
        };
    }
    catch (error$1) {
        if (error$1 instanceof error.InvalidDeliveryMethodError) {
            registerResult = {
                deliveryMethod: handler.deliveryMethod,
                success: false,
                result: { message: error$1.message },
                operation,
            };
        }
        else {
            throw error$1;
        }
    }
    return registerResult;
}
function buildMutation(config, topic, handler, operation) {
    const params = {};
    let identifier;
    if (handler.id) {
        identifier = `id: "${handler.id}"`;
    }
    else {
        identifier = `topic: ${topic}`;
    }
    const mutationArguments = {
        MUTATION_NAME: getMutationName(handler, operation),
        IDENTIFIER: identifier,
        MUTATION_PARAMS: '',
    };
    if (operation !== types$1.WebhookOperation.Delete) {
        switch (handler.deliveryMethod) {
            case types$1.DeliveryMethod.Http:
                params.callbackUrl = `"${registry.addHostToCallbackUrl(config, handler.callbackUrl)}"`;
                break;
            case types$1.DeliveryMethod.EventBridge:
                params.arn = `"${handler.arn}"`;
                break;
            case types$1.DeliveryMethod.PubSub:
                params.pubSubProject = `"${handler.pubSubProject}"`;
                params.pubSubTopic = `"${handler.pubSubTopic}"`;
                break;
            default:
                throw new error.InvalidDeliveryMethodError(`Unrecognized delivery method '${handler.deliveryMethod}'`);
        }
        if (handler.includeFields) {
            params.includeFields = JSON.stringify(handler.includeFields);
        }
        if (handler.metafieldNamespaces) {
            params.metafieldNamespaces = JSON.stringify(handler.metafieldNamespaces);
        }
        if (handler.subTopic) {
            const subTopicString = `subTopic: "${handler.subTopic}",`;
            mutationArguments.MUTATION_PARAMS = subTopicString;
        }
        const paramsString = Object.entries(params)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ');
        mutationArguments.MUTATION_PARAMS += `webhookSubscription: {${paramsString}}`;
    }
    return queryTemplate.queryTemplate(TEMPLATE_MUTATION, mutationArguments);
}
function getMutationName(handler, operation) {
    switch (operation) {
        case types$1.WebhookOperation.Create:
            return `${getEndpoint(handler)}Create`;
        case types$1.WebhookOperation.Update:
            return `${getEndpoint(handler)}Update`;
        case types$1.WebhookOperation.Delete:
            return 'webhookSubscriptionDelete';
        default:
            throw new error.ShopifyError(`Unrecognized operation '${operation}'`);
    }
}
function getEndpoint(handler) {
    switch (handler.deliveryMethod) {
        case types$1.DeliveryMethod.Http:
            return 'webhookSubscription';
        case types$1.DeliveryMethod.EventBridge:
            return 'eventBridgeWebhookSubscription';
        case types$1.DeliveryMethod.PubSub:
            return 'pubSubWebhookSubscription';
        default:
            throw new error.ShopifyError(`Unrecognized delivery method '${handler.deliveryMethod}'`);
    }
}
function isSuccess(result, handler, operation) {
    const mutationName = getMutationName(handler, operation);
    return Boolean(result.data &&
        result.data[mutationName] &&
        result.data[mutationName].userErrors.length === 0);
}
const TEMPLATE_GET_HANDLERS = `query shopifyApiReadWebhookSubscriptions {
  webhookSubscriptions(
    first: 250,
    after: {{END_CURSOR}},
  ) {
    edges {
      node {
        id
        topic
        includeFields
        metafieldNamespaces
        endpoint {
          __typename
          ... on WebhookHttpEndpoint {
            callbackUrl
          }
          ... on WebhookEventBridgeEndpoint {
            arn
          }
          ... on WebhookPubSubEndpoint {
            pubSubProject
            pubSubTopic
          }
        }
      }
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}`;
const TEMPLATE_MUTATION = `
  mutation shopifyApiCreateWebhookSubscription {
    {{MUTATION_NAME}}(
      {{IDENTIFIER}},
      {{MUTATION_PARAMS}}
    ) {
      userErrors {
        field
        message
      }
    }
  }
`;

exports.TEMPLATE_GET_HANDLERS = TEMPLATE_GET_HANDLERS;
exports.TEMPLATE_MUTATION = TEMPLATE_MUTATION;
exports.register = register;
//# sourceMappingURL=register.js.map

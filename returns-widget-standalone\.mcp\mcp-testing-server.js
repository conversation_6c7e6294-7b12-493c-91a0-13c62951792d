#!/usr/bin/env node

/**
 * MCP Server for Testing Tools
 * Provides tools for GitHub Copilot to run tests and generate test cases
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

class TestingMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'testing-tools-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'run_tests',
            description: 'Run tests using npm test or specific test command',
            inputSchema: {
              type: 'object',
              properties: {
                command: {
                  type: 'string',
                  description: 'Test command to run (e.g., "npm test", "npm run test:unit")',
                  default: 'npm test',
                },
                workingDir: {
                  type: 'string',
                  description: 'Working directory to run tests from',
                },
                pattern: {
                  type: 'string',
                  description: 'Test file pattern to run specific tests',
                },
              },
            },
          },
          {
            name: 'generate_test_case',
            description: 'Generate test cases for a given component or function',
            inputSchema: {
              type: 'object',
              properties: {
                componentPath: {
                  type: 'string',
                  description: 'Path to the component or function file',
                },
                testType: {
                  type: 'string',
                  description: 'Type of test (unit, integration, e2e)',
                  enum: ['unit', 'integration', 'e2e'],
                  default: 'unit',
                },
                framework: {
                  type: 'string',
                  description: 'Testing framework (vitest, jest, playwright)',
                  enum: ['vitest', 'jest', 'playwright'],
                  default: 'vitest',
                },
              },
              required: ['componentPath'],
            },
          },
          {
            name: 'coverage_report',
            description: 'Generate test coverage report',
            inputSchema: {
              type: 'object',
              properties: {
                workingDir: {
                  type: 'string',
                  description: 'Working directory to run coverage from',
                },
                format: {
                  type: 'string',
                  description: 'Coverage report format',
                  enum: ['text', 'html', 'json'],
                  default: 'text',
                },
              },
            },
          },
          {
            name: 'lint_code',
            description: 'Run linting on the codebase',
            inputSchema: {
              type: 'object',
              properties: {
                workingDir: {
                  type: 'string',
                  description: 'Working directory to run linting from',
                },
                fix: {
                  type: 'boolean',
                  description: 'Auto-fix linting issues',
                  default: false,
                },
                pattern: {
                  type: 'string',
                  description: 'File pattern to lint',
                },
              },
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'run_tests':
            return await this.runTests(args);
          case 'generate_test_case':
            return await this.generateTestCase(args);
          case 'coverage_report':
            return await this.generateCoverageReport(args);
          case 'lint_code':
            return await this.lintCode(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
        };
      }
    });
  }

  async runCommand(command, args, workingDir) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, {
        cwd: workingDir || process.cwd(),
        stdio: 'pipe',
        shell: true,
      });

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        resolve({
          code,
          stdout,
          stderr,
        });
      });

      process.on('error', reject);
    });
  }

  async runTests({ command = 'npm test', workingDir, pattern }) {
    let fullCommand = command;
    if (pattern) {
      fullCommand += ` -- ${pattern}`;
    }

    const result = await this.runCommand(fullCommand, [], workingDir);
    
    return {
      content: [
        {
          type: 'text',
          text: `Test Results:\n\nExit Code: ${result.code}\n\nStdout:\n${result.stdout}\n\nStderr:\n${result.stderr}`,
        },
      ],
    };
  }

  async generateTestCase({ componentPath, testType = 'unit', framework = 'vitest' }) {
    try {
      const componentContent = await fs.readFile(componentPath, 'utf8');
      const componentName = path.basename(componentPath, path.extname(componentPath));
      
      let testTemplate = '';
      
      if (framework === 'vitest' && testType === 'unit') {
        testTemplate = this.generateVitestTemplate(componentName, componentContent);
      } else if (framework === 'playwright' && testType === 'e2e') {
        testTemplate = this.generatePlaywrightTemplate(componentName);
      } else {
        testTemplate = this.generateJestTemplate(componentName, componentContent);
      }
      
      return {
        content: [
          {
            type: 'text',
            text: `Generated ${testType} test for ${componentName}:\n\n\`\`\`typescript\n${testTemplate}\n\`\`\``,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to read component file: ${error.message}`);
    }
  }

  generateVitestTemplate(componentName, componentContent) {
    const isReactComponent = componentContent.includes('React') || componentContent.includes('jsx') || componentContent.includes('tsx');
    
    if (isReactComponent) {
      return `import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ${componentName} } from './${componentName}'

describe('${componentName}', () => {
  it('renders correctly', () => {
    render(<${componentName} />)
    expect(screen.getByRole('main')).toBeInTheDocument()
  })

  it('handles props correctly', () => {
    const testProp = 'test value'
    render(<${componentName} title={testProp} />)
    expect(screen.getByText(testProp)).toBeInTheDocument()
  })

  it('handles user interactions', async () => {
    const { user } = render(<${componentName} />)
    // Add interaction tests here
  })
})`;
    } else {
      return `import { describe, it, expect } from 'vitest'
import { ${componentName} } from './${componentName}'

describe('${componentName}', () => {
  it('should work correctly', () => {
    // Add your test logic here
    expect(${componentName}).toBeDefined()
  })

  it('should handle edge cases', () => {
    // Add edge case tests here
  })
})`;
    }
  }

  generatePlaywrightTemplate(componentName) {
    return `import { test, expect } from '@playwright/test'

test.describe('${componentName}', () => {
  test('should load correctly', async ({ page }) => {
    await page.goto('/')
    await expect(page).toHaveTitle(/Returns Widget/)
  })

  test('should handle user interactions', async ({ page }) => {
    await page.goto('/')
    
    // Add your E2E test steps here
    await page.click('button[type="submit"]')
    await expect(page.locator('.success-message')).toBeVisible()
  })

  test('should be responsive', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // Add mobile-specific tests here
  })
})`;
  }

  generateJestTemplate(componentName, componentContent) {
    return `import { ${componentName} } from './${componentName}'

describe('${componentName}', () => {
  it('should work correctly', () => {
    expect(${componentName}).toBeDefined()
  })

  beforeEach(() => {
    // Setup before each test
  })

  afterEach(() => {
    // Cleanup after each test
  })
})`;
  }

  async generateCoverageReport({ workingDir, format = 'text' }) {
    const command = `npm run test:coverage -- --reporter=${format}`;
    const result = await this.runCommand(command, [], workingDir);
    
    return {
      content: [
        {
          type: 'text',
          text: `Coverage Report (${format}):\n\n${result.stdout}\n\nErrors:\n${result.stderr}`,
        },
      ],
    };
  }

  async lintCode({ workingDir, fix = false, pattern = '.' }) {
    const command = fix ? 'npm run lint:fix' : 'npm run lint';
    const result = await this.runCommand(`${command} ${pattern}`, [], workingDir);
    
    return {
      content: [
        {
          type: 'text',
          text: `Linting Results${fix ? ' (with fixes)' : ''}:\n\nExit Code: ${result.code}\n\nOutput:\n${result.stdout}\n\nErrors:\n${result.stderr}`,
        },
      ],
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Testing MCP server running on stdio');
  }
}

const server = new TestingMCPServer();
server.run().catch(console.error);

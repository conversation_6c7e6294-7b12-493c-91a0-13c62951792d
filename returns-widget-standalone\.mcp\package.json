{"name": "returns-widget-mcp-servers", "version": "1.0.0", "description": "Model Context Protocol servers for Returns Widget development", "type": "module", "scripts": {"start:shopify": "node mcp-shopify-server.js", "start:sendcloud": "node mcp-sendcloud-server.js", "start:database": "node mcp-database-server.js", "start:testing": "node mcp-testing-server.js", "install:all": "npm install && cd .. && npm install"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "better-sqlite3": "^9.0.0"}, "devDependencies": {"@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "model-context-protocol", "shopify", "sendcloud", "database", "testing", "returns-widget"], "author": "Returns Widget Team", "license": "MIT"}
{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/webhooks/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\n\nimport {addHandlers, getTopicsAdded, getHandlers, registry} from './registry';\nimport {register} from './register';\nimport {process} from './process';\nimport {validateFactory} from './validate';\nimport {HttpWebhookHandlerWithCallback, WebhookRegistry} from './types';\n\ninterface Webhooks {\n  /**\n   * Add shop-specific webhook handlers to the library registry,\n   * allowing you to register webhooks with Shopify and process HTTP webhook requests from Shopify.\n   * In most cases, you should use app-specific webhooks:\n   *\n   * {@link https://shopify.dev/docs/apps/build/webhooks/subscribe#app-specific-vs-shop-specific-subscriptions}\n   *\n   * If you use only app-specific webhooks, you do not need to use `addHandlers`.\n   *\n   */\n  addHandlers: ReturnType<typeof addHandlers>;\n\n  /**\n   * Fetches all topics that were added to the registry.\n   *\n   * In most cases, you should use app-specific webhooks:\n   *\n   * {@link https://shopify.dev/docs/apps/build/webhooks/subscribe#app-specific-vs-shop-specific-subscriptions}\n   *\n   * If you use only app-specific webhooks, you do not need to use `getTopicsAdded`.\n   *\n   */\n  getTopicsAdded: ReturnType<typeof getTopicsAdded>;\n\n  /**\n   * Fetches the configured handlers for shop-specific webhooks for the given topic.\n   *\n   * In most cases, you should use app-specific webhooks:\n   *\n   * {@link https://shopify.dev/docs/apps/build/webhooks/subscribe#app-specific-vs-shop-specific-subscriptions}\n   *\n   * If you use only app-specific webhooks, you do not need to use `getHandlers`.\n   *\n   */\n  getHandlers: ReturnType<typeof getHandlers>;\n\n  /**\n   * Registers a webhook handler for a given topic.\n   *\n   * In most cases, you should use app-specific webhooks:\n   *\n   * {@link https://shopify.dev/docs/apps/build/webhooks/subscribe#app-specific-vs-shop-specific-subscriptions}\n   *\n   * If you use only app-specific webhooks, you do not need to use `register`.\n   *\n   */\n  register: ReturnType<typeof register>;\n\n  /**\n   * Processes a webhook request.\n   *\n   * In most cases, you should use app-specific webhooks:\n   *\n   * {@link https://shopify.dev/docs/apps/build/webhooks/subscribe#app-specific-vs-shop-specific-subscriptions}\n   *\n   * If you use only app-specific webhooks, you do not need to use `process`.\n   *\n   */\n  process: ReturnType<typeof process>;\n\n  /**\n   * Validates an incoming request for `Http` handlers.\n   *\n   * If the call is invalid, it will return a `valid` field set to `false`.\n   *\n   * `validate` can be used to validate app-specific and shop-specific webhook requests.\n   *\n   */\n  validate: ReturnType<typeof validateFactory>;\n}\n\nexport function shopifyWebhooks(config: ConfigInterface): Webhooks {\n  const webhookRegistry = registry();\n\n  return {\n    addHandlers: addHandlers(config, webhookRegistry),\n    getTopicsAdded: getTopicsAdded(webhookRegistry),\n    getHandlers: getHandlers(webhookRegistry),\n    register: register(config, webhookRegistry),\n    process: process(\n      config,\n      webhookRegistry as WebhookRegistry<HttpWebhookHandlerWithCallback>,\n    ),\n    validate: validateFactory(config),\n  };\n}\n\nexport type ShopifyWebhooks = ReturnType<typeof shopifyWebhooks>;\n"], "names": ["registry", "addHandlers", "getTopicsAdded", "getHandlers", "register", "process", "validateFactory"], "mappings": ";;;;;;;AAgFM,SAAU,eAAe,CAAC,MAAuB,EAAA;AACrD,IAAA,MAAM,eAAe,GAAGA,iBAAQ,EAAE;IAElC,OAAO;AACL,QAAA,WAAW,EAAEC,oBAAW,CAAC,MAAM,EAAE,eAAe,CAAC;AACjD,QAAA,cAAc,EAAEC,uBAAc,CAAC,eAAe,CAAC;AAC/C,QAAA,WAAW,EAAEC,oBAAW,CAAC,eAAe,CAAC;AACzC,QAAA,QAAQ,EAAEC,iBAAQ,CAAC,MAAM,EAAE,eAAe,CAAC;AAC3C,QAAA,OAAO,EAAEC,eAAO,CACd,MAAM,EACN,eAAkE,CACnE;AACD,QAAA,QAAQ,EAAEC,wBAAe,CAAC,MAAM,CAAC;KAClC;AACH;;;;"}
.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.loading, .error {
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.error {
  color: #dc2626;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.metricIcon {
  background-color: #3b82f6;
  color: white;
  border-radius: 8px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.metricContent h3 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metricValue {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.recentReturns {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.recentReturns h2 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.emptyState svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.emptyState p {
  margin: 0;
  font-size: 0.875rem;
}

.setupHint {
  margin-top: 0.5rem !important;
  color: #3b82f6 !important;
  font-weight: 500 !important;
}

.quickActions {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.quickActions h2 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.actionButtons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  text-decoration: none;
  color: #374151;
  font-weight: 500;
  transition: all 0.2s;
}

.actionButton:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
  color: #1f2937;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.returnsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.returnItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #f9fafb;
}

.returnInfo {
  flex: 1;
}

.returnEmail {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.returnOrder {
  font-size: 0.875rem;
  color: #6b7280;
}

.returnStatus {
  margin: 0 1rem;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.statusBadge.approved {
  background-color: #d1fae5;
  color: #065f46;
}

.statusBadge.rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

.statusBadge.completed {
  background-color: #e0e7ff;
  color: #3730a3;
}

.returnDate {
  font-size: 0.875rem;
  color: #6b7280;
  min-width: 100px;
  text-align: right;
}

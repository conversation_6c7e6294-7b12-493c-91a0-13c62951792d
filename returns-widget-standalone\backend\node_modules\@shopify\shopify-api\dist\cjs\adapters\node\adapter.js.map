{"version": 3, "file": "adapter.js", "sources": ["../../../../../../adapters/node/adapter.ts"], "sourcesContent": ["import type {IncomingMessage, ServerResponse} from 'http';\n\nimport {\n  AdapterArgs,\n  canonicalizeHeaders,\n  Headers,\n  NormalizedRequest,\n  NormalizedResponse,\n} from '../../runtime/http';\n\ninterface NodeAdapterArgs extends AdapterArgs {\n  rawRequest: IncomingMessage;\n  rawResponse: ServerResponse;\n}\n\nexport async function nodeConvertRequest(\n  adapterArgs: NodeAdapterArgs,\n): Promise<NormalizedRequest> {\n  const req = adapterArgs.rawRequest;\n\n  return {\n    headers: canonicalizeHeaders({...req.headers} as any),\n    method: req.method ?? 'GET',\n    // Express.js overrides the url property, so we want to use originalUrl for it\n    url: (req as any).originalUrl || req.url!,\n  };\n}\n\nexport async function nodeConvertIncomingResponse(\n  adapterArgs: NodeAdapterArgs,\n): Promise<NormalizedResponse> {\n  return {\n    statusCode: adapterArgs.rawResponse.statusCode,\n    statusText: adapterArgs.rawResponse.statusMessage,\n    headers: canonicalizeHeaders(\n      adapterArgs.rawResponse.getHeaders() as any as Headers,\n    ),\n  } as NormalizedResponse;\n}\n\nexport async function nodeConvertAndSendResponse(\n  response: NormalizedResponse,\n  adapterArgs: NodeAdapterArgs,\n): Promise<void> {\n  const res = adapterArgs.rawResponse;\n\n  if (response.headers) {\n    await nodeConvertAndSetHeaders(response.headers, adapterArgs);\n  }\n\n  if (response.body) {\n    res.write(response.body);\n  }\n\n  res.statusCode = response.statusCode;\n  res.statusMessage = response.statusText;\n\n  res.end();\n}\n\nexport async function nodeConvertAndSetHeaders(\n  headers: Headers,\n  adapterArgs: NodeAdapterArgs,\n): Promise<void> {\n  const res = adapterArgs.rawResponse;\n\n  Object.entries(headers).forEach(([header, value]) =>\n    res.setHeader(header, value),\n  );\n}\n\nexport function nodeRuntimeString() {\n  return `Node ${process.version}`;\n}\n"], "names": ["canonicalizeHeaders"], "mappings": ";;;;;;AAeO,eAAe,kBAAkB,CACtC,WAA4B,EAAA;AAE5B,IAAA,MAAM,GAAG,GAAG,WAAW,CAAC,UAAU;IAElC,OAAO;QACL,OAAO,EAAEA,2BAAmB,CAAC,EAAC,GAAG,GAAG,CAAC,OAAO,EAAQ,CAAC;AACrD,QAAA,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,KAAK;;AAE3B,QAAA,GAAG,EAAG,GAAW,CAAC,WAAW,IAAI,GAAG,CAAC,GAAI;KAC1C;AACH;AAEO,eAAe,2BAA2B,CAC/C,WAA4B,EAAA;IAE5B,OAAO;AACL,QAAA,UAAU,EAAE,WAAW,CAAC,WAAW,CAAC,UAAU;AAC9C,QAAA,UAAU,EAAE,WAAW,CAAC,WAAW,CAAC,aAAa;QACjD,OAAO,EAAEA,2BAAmB,CAC1B,WAAW,CAAC,WAAW,CAAC,UAAU,EAAoB,CACvD;KACoB;AACzB;AAEO,eAAe,0BAA0B,CAC9C,QAA4B,EAC5B,WAA4B,EAAA;AAE5B,IAAA,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW;AAEnC,IAAA,IAAI,QAAQ,CAAC,OAAO,EAAE;QACpB,MAAM,wBAAwB,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC;IAC/D;AAEA,IAAA,IAAI,QAAQ,CAAC,IAAI,EAAE;AACjB,QAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC1B;AAEA,IAAA,GAAG,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU;AACpC,IAAA,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAC,UAAU;IAEvC,GAAG,CAAC,GAAG,EAAE;AACX;AAEO,eAAe,wBAAwB,CAC5C,OAAgB,EAChB,WAA4B,EAAA;AAE5B,IAAA,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW;IAEnC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,KAC9C,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAC7B;AACH;SAEgB,iBAAiB,GAAA;AAC/B,IAAA,OAAO,CAAA,KAAA,EAAQ,OAAO,CAAC,OAAO,EAAE;AAClC;;;;;;;;"}
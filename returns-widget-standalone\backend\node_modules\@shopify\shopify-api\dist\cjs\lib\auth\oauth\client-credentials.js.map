{"version": 3, "file": "client-credentials.js", "sources": ["../../../../../../../lib/auth/oauth/client-credentials.ts"], "sourcesContent": ["import {ConfigInterface} from '../../base-types';\nimport {throwFailedRequest} from '../../clients/common';\nimport {DataType} from '../../clients/types';\nimport {Session} from '../../session/session';\nimport {fetchRequestFactory} from '../../utils/fetch-request';\nimport {sanitizeShop} from '../../utils/shop-validator';\n\nimport {createSession} from './create-session';\nimport {AccessTokenResponse} from './types';\n\nexport interface ClientCredentialsParams {\n  shop: string;\n}\n\nconst ClientCredentialsGrantType = 'client_credentials';\n\nexport type ClientCredentials = (\n  params: ClientCredentialsParams,\n) => Promise<{session: Session}>;\n\nexport function clientCredentials(config: ConfigInterface): ClientCredentials {\n  return async ({shop}: ClientCredentialsParams) => {\n    const cleanShop = sanitizeShop(config)(shop, true)!;\n\n    const requestConfig = {\n      method: 'POST',\n      body: JSON.stringify({\n        client_id: config.apiKey,\n        client_secret: config.apiSecretKey,\n        grant_type: ClientCredentialsGrantType,\n      }),\n      headers: {\n        'Content-Type': DataType.JSON,\n        Accept: DataType.JSON,\n      },\n    };\n\n    const postResponse = await fetchRequestFactory(config)(\n      `https://${cleanShop}/admin/oauth/access_token`,\n      requestConfig,\n    );\n\n    const responseData = (await postResponse.json()) as AccessTokenResponse;\n\n    if (!postResponse.ok) {\n      throwFailedRequest(responseData, false, postResponse);\n    }\n\n    return {\n      session: createSession({\n        accessTokenResponse: responseData,\n        shop: cleanShop,\n        // We need to keep this as an empty string as our template DB schemas have this required\n        state: '',\n        config,\n      }),\n    };\n  };\n}\n"], "names": ["sanitizeShop", "DataType", "fetchRequestFactory", "throwFailedRequest", "createSession"], "mappings": ";;;;;;;;AAcA,MAAM,0BAA0B,GAAG,oBAAoB;AAMjD,SAAU,iBAAiB,CAAC,MAAuB,EAAA;AACvD,IAAA,OAAO,OAAO,EAAC,IAAI,EAA0B,KAAI;QAC/C,MAAM,SAAS,GAAGA,0BAAY,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAE;AAEnD,QAAA,MAAM,aAAa,GAAG;AACpB,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,SAAS,EAAE,MAAM,CAAC,MAAM;gBACxB,aAAa,EAAE,MAAM,CAAC,YAAY;AAClC,gBAAA,UAAU,EAAE,0BAA0B;aACvC,CAAC;AACF,YAAA,OAAO,EAAE;gBACP,cAAc,EAAEC,cAAQ,CAAC,IAAI;gBAC7B,MAAM,EAAEA,cAAQ,CAAC,IAAI;AACtB,aAAA;SACF;AAED,QAAA,MAAM,YAAY,GAAG,MAAMC,gCAAmB,CAAC,MAAM,CAAC,CACpD,CAAA,QAAA,EAAW,SAAS,CAAA,yBAAA,CAA2B,EAC/C,aAAa,CACd;QAED,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,IAAI,EAAE,CAAwB;AAEvE,QAAA,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;AACpB,YAAAC,yBAAkB,CAAC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;QACvD;QAEA,OAAO;YACL,OAAO,EAAEC,2BAAa,CAAC;AACrB,gBAAA,mBAAmB,EAAE,YAAY;AACjC,gBAAA,IAAI,EAAE,SAAS;;AAEf,gBAAA,KAAK,EAAE,EAAE;gBACT,MAAM;aACP,CAAC;SACH;AACH,IAAA,CAAC;AACH;;;;"}
{"version": 3, "file": "register.js", "sources": ["../../../../../../lib/webhooks/register.ts"], "sourcesContent": ["import {graphqlClientClass, GraphqlClient} from '../clients/admin';\nimport {InvalidDeliveryMethodError, ShopifyError} from '../error';\nimport {logger} from '../logger';\nimport {privacyTopics} from '../types';\nimport {ConfigInterface} from '../base-types';\nimport {Session} from '../session/session';\n\nimport {addHostToCallbackUrl, getHandlers, handlerIdentifier} from './registry';\nimport {queryTemplate} from './query-template';\nimport {\n  WebhookRegistry,\n  RegisterReturn,\n  WebhookHandler,\n  WebhookCheckResponse,\n  DeliveryMethod,\n  WebhookCheckResponseNode,\n  WebhookOperation,\n  RegisterResult,\n  RegisterParams,\n} from './types';\n\ninterface RegisterTopicParams {\n  config: ConfigInterface;\n  session: Session;\n  topic: string;\n  existingHandlers: WebhookHandler[];\n  handlers: WebhookHandler[];\n}\n\ninterface RunMutationsParams {\n  config: ConfigInterface;\n  client: GraphqlClient;\n  topic: string;\n  handlers: WebhookHandler[];\n  operation: WebhookOperation;\n}\n\ninterface RunMutationParams {\n  config: ConfigInterface;\n  client: GraphqlClient;\n  topic: string;\n  handler: WebhookHandler;\n  operation: WebhookOperation;\n}\n\nexport function register(\n  config: ConfigInterface,\n  webhookRegistry: WebhookRegistry,\n) {\n  return async function register({\n    session,\n  }: RegisterParams): Promise<RegisterReturn> {\n    const log = logger(config);\n    log.info('Registering webhooks', {shop: session.shop});\n\n    const registerReturn: RegisterReturn = Object.keys(webhookRegistry).reduce(\n      (acc: RegisterReturn, topic) => {\n        acc[topic] = [];\n        return acc;\n      },\n      {},\n    );\n\n    const existingHandlers = await getExistingHandlers(config, session);\n\n    log.debug(\n      `Existing topics: [${Object.keys(existingHandlers).join(', ')}]`,\n      {shop: session.shop},\n    );\n\n    for (const topic in webhookRegistry) {\n      if (!Object.prototype.hasOwnProperty.call(webhookRegistry, topic)) {\n        continue;\n      }\n\n      if (privacyTopics.includes(topic)) {\n        continue;\n      }\n\n      registerReturn[topic] = await registerTopic({\n        config,\n        session,\n        topic,\n        existingHandlers: existingHandlers[topic] || [],\n        handlers: getHandlers(webhookRegistry)(topic),\n      });\n\n      // Remove this topic from the list of existing handlers so we have a list of leftovers\n      delete existingHandlers[topic];\n    }\n\n    // Delete any leftover handlers\n    for (const topic in existingHandlers) {\n      if (!Object.prototype.hasOwnProperty.call(existingHandlers, topic)) {\n        continue;\n      }\n\n      const GraphqlClient = graphqlClientClass({config});\n      const client = new GraphqlClient({session});\n\n      registerReturn[topic] = await runMutations({\n        config,\n        client,\n        topic,\n        handlers: existingHandlers[topic],\n        operation: WebhookOperation.Delete,\n      });\n    }\n\n    return registerReturn;\n  };\n}\n\nasync function getExistingHandlers(\n  config: ConfigInterface,\n  session: Session,\n): Promise<WebhookRegistry> {\n  const GraphqlClient = graphqlClientClass({config});\n  const client = new GraphqlClient({session});\n\n  const existingHandlers: WebhookRegistry = {};\n\n  let hasNextPage: boolean;\n  let endCursor: string | null = null;\n  do {\n    const query = buildCheckQuery(endCursor);\n\n    const response = await client.request<WebhookCheckResponse>(query);\n\n    response.data?.webhookSubscriptions?.edges.forEach(\n      (edge: WebhookCheckResponseNode) => {\n        const handler = buildHandlerFromNode(edge);\n\n        if (!existingHandlers[edge.node.topic]) {\n          existingHandlers[edge.node.topic] = [];\n        }\n\n        existingHandlers[edge.node.topic].push(handler);\n      },\n    );\n\n    endCursor = response.data?.webhookSubscriptions?.pageInfo.endCursor!;\n    hasNextPage = response.data?.webhookSubscriptions?.pageInfo.hasNextPage!;\n  } while (hasNextPage);\n\n  return existingHandlers;\n}\n\nfunction buildCheckQuery(endCursor: string | null) {\n  return queryTemplate(TEMPLATE_GET_HANDLERS, {\n    END_CURSOR: JSON.stringify(endCursor),\n  });\n}\n\nfunction buildHandlerFromNode(edge: WebhookCheckResponseNode): WebhookHandler {\n  const endpoint = edge.node.endpoint;\n\n  let handler: WebhookHandler;\n\n  switch (endpoint.__typename) {\n    case 'WebhookHttpEndpoint':\n      handler = {\n        deliveryMethod: DeliveryMethod.Http,\n        callbackUrl: endpoint.callbackUrl,\n        // This is a dummy for now because we don't really care about it\n        callback: async () => {},\n      };\n      break;\n    case 'WebhookEventBridgeEndpoint':\n      handler = {\n        deliveryMethod: DeliveryMethod.EventBridge,\n        arn: endpoint.arn,\n      };\n      break;\n    case 'WebhookPubSubEndpoint':\n      handler = {\n        deliveryMethod: DeliveryMethod.PubSub,\n        pubSubProject: endpoint.pubSubProject,\n        pubSubTopic: endpoint.pubSubTopic,\n      };\n      break;\n  }\n\n  // Set common fields\n  handler.id = edge.node.id;\n  handler.includeFields = edge.node.includeFields;\n  handler.metafieldNamespaces = edge.node.metafieldNamespaces;\n\n  // Sort the array fields to make them cheaper to compare later on\n  handler.includeFields?.sort();\n  handler.metafieldNamespaces?.sort();\n\n  return handler;\n}\n\nasync function registerTopic({\n  config,\n  session,\n  topic,\n  existingHandlers,\n  handlers,\n}: RegisterTopicParams): Promise<RegisterResult[]> {\n  let registerResults: RegisterResult[] = [];\n\n  const {toCreate, toUpdate, toDelete} = categorizeHandlers(\n    config,\n    existingHandlers,\n    handlers,\n  );\n\n  const GraphqlClient = graphqlClientClass({config});\n  const client = new GraphqlClient({session});\n\n  let operation = WebhookOperation.Create;\n  registerResults = registerResults.concat(\n    await runMutations({config, client, topic, operation, handlers: toCreate}),\n  );\n\n  operation = WebhookOperation.Update;\n  registerResults = registerResults.concat(\n    await runMutations({config, client, topic, operation, handlers: toUpdate}),\n  );\n\n  operation = WebhookOperation.Delete;\n  registerResults = registerResults.concat(\n    await runMutations({config, client, topic, operation, handlers: toDelete}),\n  );\n\n  return registerResults;\n}\n\ntype HandlersByKey = Record<string, WebhookHandler>;\n\nfunction categorizeHandlers(\n  config: ConfigInterface,\n  existingHandlers: WebhookHandler[],\n  handlers: WebhookHandler[],\n) {\n  const handlersByKey = handlers.reduce((acc: HandlersByKey, value) => {\n    acc[handlerIdentifier(config, value)] = value;\n    return acc;\n  }, {});\n  const existingHandlersByKey = existingHandlers.reduce(\n    (acc: HandlersByKey, value) => {\n      acc[handlerIdentifier(config, value)] = value;\n      return acc;\n    },\n    {},\n  );\n\n  const toCreate: HandlersByKey = {...handlersByKey};\n  const toUpdate: HandlersByKey = {};\n  const toDelete: HandlersByKey = {};\n  for (const existingKey in existingHandlersByKey) {\n    if (\n      !Object.prototype.hasOwnProperty.call(existingHandlersByKey, existingKey)\n    ) {\n      continue;\n    }\n\n    const existingHandler = existingHandlersByKey[existingKey];\n    const handler = handlersByKey[existingKey];\n\n    if (existingKey in handlersByKey) {\n      delete toCreate[existingKey];\n\n      if (!areHandlerFieldsEqual(existingHandler, handler)) {\n        toUpdate[existingKey] = handler;\n        toUpdate[existingKey].id = existingHandler.id;\n      }\n    } else {\n      toDelete[existingKey] = existingHandler;\n    }\n  }\n\n  return {\n    toCreate: Object.values(toCreate),\n    toUpdate: Object.values(toUpdate),\n    toDelete: Object.values(toDelete),\n  };\n}\nfunction areHandlerFieldsEqual(\n  arr1: WebhookHandler,\n  arr2: WebhookHandler,\n): boolean {\n  const includeFieldsEqual = arraysEqual(\n    arr1.includeFields || [],\n    arr2.includeFields || [],\n  );\n  const metafieldNamespacesEqual = arraysEqual(\n    arr1.metafieldNamespaces || [],\n    arr2.metafieldNamespaces || [],\n  );\n\n  return includeFieldsEqual && metafieldNamespacesEqual;\n}\n\nfunction arraysEqual(arr1: any[], arr2: any[]): boolean {\n  if (arr1.length !== arr2.length) {\n    return false;\n  }\n\n  for (let i = 0; i < arr1.length; i++) {\n    if (arr1[i] !== arr2[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nasync function runMutations({\n  config,\n  client,\n  topic,\n  handlers,\n  operation,\n}: RunMutationsParams): Promise<RegisterResult[]> {\n  const registerResults: RegisterResult[] = [];\n\n  for (const handler of handlers) {\n    registerResults.push(\n      await runMutation({config, client, topic, handler, operation}),\n    );\n  }\n\n  return registerResults;\n}\n\nasync function runMutation({\n  config,\n  client,\n  topic,\n  handler,\n  operation,\n}: RunMutationParams): Promise<RegisterResult> {\n  let registerResult: RegisterResult;\n\n  logger(config).debug(`Running webhook mutation`, {topic, operation});\n\n  try {\n    const query = buildMutation(config, topic, handler, operation);\n\n    const result = await client.request(query);\n\n    registerResult = {\n      deliveryMethod: handler.deliveryMethod,\n      success: isSuccess(result, handler, operation),\n      result,\n      operation,\n    };\n  } catch (error) {\n    if (error instanceof InvalidDeliveryMethodError) {\n      registerResult = {\n        deliveryMethod: handler.deliveryMethod,\n        success: false,\n        result: {message: error.message},\n        operation,\n      };\n    } else {\n      throw error;\n    }\n  }\n\n  return registerResult;\n}\n\nfunction buildMutation(\n  config: ConfigInterface,\n  topic: string,\n  handler: WebhookHandler,\n  operation: WebhookOperation,\n): string {\n  const params: Record<string, string> = {};\n\n  let identifier: string;\n  if (handler.id) {\n    identifier = `id: \"${handler.id}\"`;\n  } else {\n    identifier = `topic: ${topic}`;\n  }\n\n  const mutationArguments = {\n    MUTATION_NAME: getMutationName(handler, operation),\n    IDENTIFIER: identifier,\n    MUTATION_PARAMS: '',\n  };\n\n  if (operation !== WebhookOperation.Delete) {\n    switch (handler.deliveryMethod) {\n      case DeliveryMethod.Http:\n        params.callbackUrl = `\"${addHostToCallbackUrl(\n          config,\n          handler.callbackUrl,\n        )}\"`;\n        break;\n      case DeliveryMethod.EventBridge:\n        params.arn = `\"${handler.arn}\"`;\n        break;\n      case DeliveryMethod.PubSub:\n        params.pubSubProject = `\"${handler.pubSubProject}\"`;\n        params.pubSubTopic = `\"${handler.pubSubTopic}\"`;\n        break;\n      default:\n        throw new InvalidDeliveryMethodError(\n          `Unrecognized delivery method '${(handler as any).deliveryMethod}'`,\n        );\n    }\n\n    if (handler.includeFields) {\n      params.includeFields = JSON.stringify(handler.includeFields);\n    }\n    if (handler.metafieldNamespaces) {\n      params.metafieldNamespaces = JSON.stringify(handler.metafieldNamespaces);\n    }\n\n    if (handler.subTopic) {\n      const subTopicString = `subTopic: \"${handler.subTopic}\",`;\n      mutationArguments.MUTATION_PARAMS = subTopicString;\n    }\n\n    const paramsString = Object.entries(params)\n      .map(([key, value]) => `${key}: ${value}`)\n      .join(', ');\n\n    mutationArguments.MUTATION_PARAMS += `webhookSubscription: {${paramsString}}`;\n  }\n\n  return queryTemplate(TEMPLATE_MUTATION, mutationArguments);\n}\n\nfunction getMutationName(\n  handler: WebhookHandler,\n  operation: WebhookOperation,\n): string {\n  switch (operation) {\n    case WebhookOperation.Create:\n      return `${getEndpoint(handler)}Create`;\n    case WebhookOperation.Update:\n      return `${getEndpoint(handler)}Update`;\n    case WebhookOperation.Delete:\n      return 'webhookSubscriptionDelete';\n    default:\n      throw new ShopifyError(`Unrecognized operation '${operation}'`);\n  }\n}\n\nfunction getEndpoint(handler: WebhookHandler): string {\n  switch (handler.deliveryMethod) {\n    case DeliveryMethod.Http:\n      return 'webhookSubscription';\n    case DeliveryMethod.EventBridge:\n      return 'eventBridgeWebhookSubscription';\n    case DeliveryMethod.PubSub:\n      return 'pubSubWebhookSubscription';\n    default:\n      throw new ShopifyError(\n        `Unrecognized delivery method '${(handler as any).deliveryMethod}'`,\n      );\n  }\n}\n\nfunction isSuccess(\n  result: any,\n  handler: WebhookHandler,\n  operation: WebhookOperation,\n): boolean {\n  const mutationName = getMutationName(handler, operation);\n\n  return Boolean(\n    result.data &&\n      result.data[mutationName] &&\n      result.data[mutationName].userErrors.length === 0,\n  );\n}\n\nexport const TEMPLATE_GET_HANDLERS = `query shopifyApiReadWebhookSubscriptions {\n  webhookSubscriptions(\n    first: 250,\n    after: {{END_CURSOR}},\n  ) {\n    edges {\n      node {\n        id\n        topic\n        includeFields\n        metafieldNamespaces\n        endpoint {\n          __typename\n          ... on WebhookHttpEndpoint {\n            callbackUrl\n          }\n          ... on WebhookEventBridgeEndpoint {\n            arn\n          }\n          ... on WebhookPubSubEndpoint {\n            pubSubProject\n            pubSubTopic\n          }\n        }\n      }\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}`;\n\nexport const TEMPLATE_MUTATION = `\n  mutation shopifyApiCreateWebhookSubscription {\n    {{MUTATION_NAME}}(\n      {{IDENTIFIER}},\n      {{MUTATION_PARAMS}}\n    ) {\n      userErrors {\n        field\n        message\n      }\n    }\n  }\n`;\n"], "names": ["logger", "privacyTopics", "getHandlers", "graphqlClientClass", "client", "WebhookOperation", "queryTemplate", "DeliveryMethod", "handlerIdentifier", "error", "InvalidDeliveryMethodError", "addHostToCallbackUrl", "ShopifyError"], "mappings": ";;;;;;;;;;;;;;AA6CM,SAAU,QAAQ,CACtB,MAAuB,EACvB,eAAgC,EAAA;AAEhC,IAAA,OAAO,eAAe,QAAQ,CAAC,EAC7B,OAAO,GACQ,EAAA;AACf,QAAA,MAAM,GAAG,GAAGA,YAAM,CAAC,MAAM,CAAC;AAC1B,QAAA,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAC,CAAC;AAEtD,QAAA,MAAM,cAAc,GAAmB,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,CACxE,CAAC,GAAmB,EAAE,KAAK,KAAI;AAC7B,YAAA,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE;AACf,YAAA,OAAO,GAAG;QACZ,CAAC,EACD,EAAE,CACH;QAED,MAAM,gBAAgB,GAAG,MAAM,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC;QAEnE,GAAG,CAAC,KAAK,CACP,CAAA,kBAAA,EAAqB,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,EAChE,EAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAC,CACrB;AAED,QAAA,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE;AACnC,YAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE;gBACjE;YACF;AAEA,YAAA,IAAIC,mBAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACjC;YACF;AAEA,YAAA,cAAc,CAAC,KAAK,CAAC,GAAG,MAAM,aAAa,CAAC;gBAC1C,MAAM;gBACN,OAAO;gBACP,KAAK;AACL,gBAAA,gBAAgB,EAAE,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE;AAC/C,gBAAA,QAAQ,EAAEC,oBAAW,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC;AAC9C,aAAA,CAAC;;AAGF,YAAA,OAAO,gBAAgB,CAAC,KAAK,CAAC;QAChC;;AAGA,QAAA,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE;AACpC,YAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;gBAClE;YACF;YAEA,MAAM,aAAa,GAAGC,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;YAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;AAE3C,YAAA,cAAc,CAAC,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC;gBACzC,MAAM;wBACNA,QAAM;gBACN,KAAK;AACL,gBAAA,QAAQ,EAAE,gBAAgB,CAAC,KAAK,CAAC;gBACjC,SAAS,EAAEC,wBAAgB,CAAC,MAAM;AACnC,aAAA,CAAC;QACJ;AAEA,QAAA,OAAO,cAAc;AACvB,IAAA,CAAC;AACH;AAEA,eAAe,mBAAmB,CAChC,MAAuB,EACvB,OAAgB,EAAA;IAEhB,MAAM,aAAa,GAAGF,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;IAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;IAE3C,MAAM,gBAAgB,GAAoB,EAAE;AAE5C,IAAA,IAAI,WAAoB;IACxB,IAAI,SAAS,GAAkB,IAAI;AACnC,IAAA,GAAG;AACD,QAAA,MAAM,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC;QAExC,MAAM,QAAQ,GAAG,MAAMA,QAAM,CAAC,OAAO,CAAuB,KAAK,CAAC;AAElE,QAAA,QAAQ,CAAC,IAAI,EAAE,oBAAoB,EAAE,KAAK,CAAC,OAAO,CAChD,CAAC,IAA8B,KAAI;AACjC,YAAA,MAAM,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC;YAE1C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACtC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACxC;AAEA,YAAA,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AACjD,QAAA,CAAC,CACF;QAED,SAAS,GAAG,QAAQ,CAAC,IAAI,EAAE,oBAAoB,EAAE,QAAQ,CAAC,SAAU;QACpE,WAAW,GAAG,QAAQ,CAAC,IAAI,EAAE,oBAAoB,EAAE,QAAQ,CAAC,WAAY;IAC1E,CAAC,QAAQ,WAAW;AAEpB,IAAA,OAAO,gBAAgB;AACzB;AAEA,SAAS,eAAe,CAAC,SAAwB,EAAA;IAC/C,OAAOE,2BAAa,CAAC,qBAAqB,EAAE;AAC1C,QAAA,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;AACtC,KAAA,CAAC;AACJ;AAEA,SAAS,oBAAoB,CAAC,IAA8B,EAAA;AAC1D,IAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;AAEnC,IAAA,IAAI,OAAuB;AAE3B,IAAA,QAAQ,QAAQ,CAAC,UAAU;AACzB,QAAA,KAAK,qBAAqB;AACxB,YAAA,OAAO,GAAG;gBACR,cAAc,EAAEC,sBAAc,CAAC,IAAI;gBACnC,WAAW,EAAE,QAAQ,CAAC,WAAW;;AAEjC,gBAAA,QAAQ,EAAE,cAAa,CAAC;aACzB;YACD;AACF,QAAA,KAAK,4BAA4B;AAC/B,YAAA,OAAO,GAAG;gBACR,cAAc,EAAEA,sBAAc,CAAC,WAAW;gBAC1C,GAAG,EAAE,QAAQ,CAAC,GAAG;aAClB;YACD;AACF,QAAA,KAAK,uBAAuB;AAC1B,YAAA,OAAO,GAAG;gBACR,cAAc,EAAEA,sBAAc,CAAC,MAAM;gBACrC,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,WAAW,EAAE,QAAQ,CAAC,WAAW;aAClC;YACD;;;IAIJ,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;IACzB,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa;IAC/C,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,mBAAmB;;AAG3D,IAAA,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE;AAC7B,IAAA,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;AAEnC,IAAA,OAAO,OAAO;AAChB;AAEA,eAAe,aAAa,CAAC,EAC3B,MAAM,EACN,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,QAAQ,GACY,EAAA;IACpB,IAAI,eAAe,GAAqB,EAAE;AAE1C,IAAA,MAAM,EAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAC,GAAG,kBAAkB,CACvD,MAAM,EACN,gBAAgB,EAChB,QAAQ,CACT;IAED,MAAM,aAAa,GAAGJ,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;IAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;AAE3C,IAAA,IAAI,SAAS,GAAGC,wBAAgB,CAAC,MAAM;IACvC,eAAe,GAAG,eAAe,CAAC,MAAM,CACtC,MAAM,YAAY,CAAC,EAAC,MAAM,UAAED,QAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAC3E;AAED,IAAA,SAAS,GAAGC,wBAAgB,CAAC,MAAM;IACnC,eAAe,GAAG,eAAe,CAAC,MAAM,CACtC,MAAM,YAAY,CAAC,EAAC,MAAM,UAAED,QAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAC3E;AAED,IAAA,SAAS,GAAGC,wBAAgB,CAAC,MAAM;IACnC,eAAe,GAAG,eAAe,CAAC,MAAM,CACtC,MAAM,YAAY,CAAC,EAAC,MAAM,UAAED,QAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAC3E;AAED,IAAA,OAAO,eAAe;AACxB;AAIA,SAAS,kBAAkB,CACzB,MAAuB,EACvB,gBAAkC,EAClC,QAA0B,EAAA;IAE1B,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAkB,EAAE,KAAK,KAAI;QAClE,GAAG,CAACI,0BAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK;AAC7C,QAAA,OAAO,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IACN,MAAM,qBAAqB,GAAG,gBAAgB,CAAC,MAAM,CACnD,CAAC,GAAkB,EAAE,KAAK,KAAI;QAC5B,GAAG,CAACA,0BAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK;AAC7C,QAAA,OAAO,GAAG;IACZ,CAAC,EACD,EAAE,CACH;AAED,IAAA,MAAM,QAAQ,GAAkB,EAAC,GAAG,aAAa,EAAC;IAClD,MAAM,QAAQ,GAAkB,EAAE;IAClC,MAAM,QAAQ,GAAkB,EAAE;AAClC,IAAA,KAAK,MAAM,WAAW,IAAI,qBAAqB,EAAE;AAC/C,QAAA,IACE,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC,EACzE;YACA;QACF;AAEA,QAAA,MAAM,eAAe,GAAG,qBAAqB,CAAC,WAAW,CAAC;AAC1D,QAAA,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC;AAE1C,QAAA,IAAI,WAAW,IAAI,aAAa,EAAE;AAChC,YAAA,OAAO,QAAQ,CAAC,WAAW,CAAC;YAE5B,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,OAAO,CAAC,EAAE;AACpD,gBAAA,QAAQ,CAAC,WAAW,CAAC,GAAG,OAAO;gBAC/B,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,eAAe,CAAC,EAAE;YAC/C;QACF;aAAO;AACL,YAAA,QAAQ,CAAC,WAAW,CAAC,GAAG,eAAe;QACzC;IACF;IAEA,OAAO;AACL,QAAA,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;KAClC;AACH;AACA,SAAS,qBAAqB,CAC5B,IAAoB,EACpB,IAAoB,EAAA;AAEpB,IAAA,MAAM,kBAAkB,GAAG,WAAW,CACpC,IAAI,CAAC,aAAa,IAAI,EAAE,EACxB,IAAI,CAAC,aAAa,IAAI,EAAE,CACzB;AACD,IAAA,MAAM,wBAAwB,GAAG,WAAW,CAC1C,IAAI,CAAC,mBAAmB,IAAI,EAAE,EAC9B,IAAI,CAAC,mBAAmB,IAAI,EAAE,CAC/B;IAED,OAAO,kBAAkB,IAAI,wBAAwB;AACvD;AAEA,SAAS,WAAW,CAAC,IAAW,EAAE,IAAW,EAAA;IAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;AAC/B,QAAA,OAAO,KAAK;IACd;AAEA,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;AACvB,YAAA,OAAO,KAAK;QACd;IACF;AAEA,IAAA,OAAO,IAAI;AACb;AAEA,eAAe,YAAY,CAAC,EAC1B,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,SAAS,GACU,EAAA;IACnB,MAAM,eAAe,GAAqB,EAAE;AAE5C,IAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAC9B,QAAA,eAAe,CAAC,IAAI,CAClB,MAAM,WAAW,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAC,CAAC,CAC/D;IACH;AAEA,IAAA,OAAO,eAAe;AACxB;AAEA,eAAe,WAAW,CAAC,EACzB,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO,EACP,SAAS,GACS,EAAA;AAClB,IAAA,IAAI,cAA8B;AAElC,IAAAR,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAA,wBAAA,CAA0B,EAAE,EAAC,KAAK,EAAE,SAAS,EAAC,CAAC;AAEpE,IAAA,IAAI;AACF,QAAA,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;QAE9D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAE1C,QAAA,cAAc,GAAG;YACf,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,OAAO,EAAE,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;YAC9C,MAAM;YACN,SAAS;SACV;IACH;IAAE,OAAOS,OAAK,EAAE;AACd,QAAA,IAAIA,OAAK,YAAYC,gCAA0B,EAAE;AAC/C,YAAA,cAAc,GAAG;gBACf,cAAc,EAAE,OAAO,CAAC,cAAc;AACtC,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,MAAM,EAAE,EAAC,OAAO,EAAED,OAAK,CAAC,OAAO,EAAC;gBAChC,SAAS;aACV;QACH;aAAO;AACL,YAAA,MAAMA,OAAK;QACb;IACF;AAEA,IAAA,OAAO,cAAc;AACvB;AAEA,SAAS,aAAa,CACpB,MAAuB,EACvB,KAAa,EACb,OAAuB,EACvB,SAA2B,EAAA;IAE3B,MAAM,MAAM,GAA2B,EAAE;AAEzC,IAAA,IAAI,UAAkB;AACtB,IAAA,IAAI,OAAO,CAAC,EAAE,EAAE;AACd,QAAA,UAAU,GAAG,CAAA,KAAA,EAAQ,OAAO,CAAC,EAAE,GAAG;IACpC;SAAO;AACL,QAAA,UAAU,GAAG,CAAA,OAAA,EAAU,KAAK,CAAA,CAAE;IAChC;AAEA,IAAA,MAAM,iBAAiB,GAAG;AACxB,QAAA,aAAa,EAAE,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC;AAClD,QAAA,UAAU,EAAE,UAAU;AACtB,QAAA,eAAe,EAAE,EAAE;KACpB;AAED,IAAA,IAAI,SAAS,KAAKJ,wBAAgB,CAAC,MAAM,EAAE;AACzC,QAAA,QAAQ,OAAO,CAAC,cAAc;YAC5B,KAAKE,sBAAc,CAAC,IAAI;AACtB,gBAAA,MAAM,CAAC,WAAW,GAAG,CAAA,CAAA,EAAII,6BAAoB,CAC3C,MAAM,EACN,OAAO,CAAC,WAAW,CACpB,GAAG;gBACJ;YACF,KAAKJ,sBAAc,CAAC,WAAW;gBAC7B,MAAM,CAAC,GAAG,GAAG,CAAA,CAAA,EAAI,OAAO,CAAC,GAAG,GAAG;gBAC/B;YACF,KAAKA,sBAAc,CAAC,MAAM;gBACxB,MAAM,CAAC,aAAa,GAAG,CAAA,CAAA,EAAI,OAAO,CAAC,aAAa,GAAG;gBACnD,MAAM,CAAC,WAAW,GAAG,CAAA,CAAA,EAAI,OAAO,CAAC,WAAW,GAAG;gBAC/C;AACF,YAAA;gBACE,MAAM,IAAIG,gCAA0B,CAClC,CAAA,8BAAA,EAAkC,OAAe,CAAC,cAAc,CAAA,CAAA,CAAG,CACpE;;AAGL,QAAA,IAAI,OAAO,CAAC,aAAa,EAAE;YACzB,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC;QAC9D;AACA,QAAA,IAAI,OAAO,CAAC,mBAAmB,EAAE;YAC/B,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,mBAAmB,CAAC;QAC1E;AAEA,QAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,YAAA,MAAM,cAAc,GAAG,CAAA,WAAA,EAAc,OAAO,CAAC,QAAQ,IAAI;AACzD,YAAA,iBAAiB,CAAC,eAAe,GAAG,cAAc;QACpD;AAEA,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM;AACvC,aAAA,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAA,EAAG,GAAG,CAAA,EAAA,EAAK,KAAK,EAAE;aACxC,IAAI,CAAC,IAAI,CAAC;AAEb,QAAA,iBAAiB,CAAC,eAAe,IAAI,CAAA,sBAAA,EAAyB,YAAY,GAAG;IAC/E;AAEA,IAAA,OAAOJ,2BAAa,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;AAC5D;AAEA,SAAS,eAAe,CACtB,OAAuB,EACvB,SAA2B,EAAA;IAE3B,QAAQ,SAAS;QACf,KAAKD,wBAAgB,CAAC,MAAM;AAC1B,YAAA,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ;QACxC,KAAKA,wBAAgB,CAAC,MAAM;AAC1B,YAAA,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ;QACxC,KAAKA,wBAAgB,CAAC,MAAM;AAC1B,YAAA,OAAO,2BAA2B;AACpC,QAAA;AACE,YAAA,MAAM,IAAIO,kBAAY,CAAC,2BAA2B,SAAS,CAAA,CAAA,CAAG,CAAC;;AAErE;AAEA,SAAS,WAAW,CAAC,OAAuB,EAAA;AAC1C,IAAA,QAAQ,OAAO,CAAC,cAAc;QAC5B,KAAKL,sBAAc,CAAC,IAAI;AACtB,YAAA,OAAO,qBAAqB;QAC9B,KAAKA,sBAAc,CAAC,WAAW;AAC7B,YAAA,OAAO,gCAAgC;QACzC,KAAKA,sBAAc,CAAC,MAAM;AACxB,YAAA,OAAO,2BAA2B;AACpC,QAAA;YACE,MAAM,IAAIK,kBAAY,CACpB,CAAA,8BAAA,EAAkC,OAAe,CAAC,cAAc,CAAA,CAAA,CAAG,CACpE;;AAEP;AAEA,SAAS,SAAS,CAChB,MAAW,EACX,OAAuB,EACvB,SAA2B,EAAA;IAE3B,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC;AAExD,IAAA,OAAO,OAAO,CACZ,MAAM,CAAC,IAAI;AACT,QAAA,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;AACzB,QAAA,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CACpD;AACH;AAEO,MAAM,qBAAqB,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiC9B,MAAM,iBAAiB,GAAG;;;;;;;;;;;;;;;;;;"}
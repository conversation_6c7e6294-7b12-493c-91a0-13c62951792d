.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.titleSection h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.tabs {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 2rem;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 0.25rem;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  justify-content: center;
}

.tab:hover {
  color: #374151;
  background-color: rgba(255, 255, 255, 0.5);
}

.tab.activeTab {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content {
  background: #f9fafb;
  border-radius: 8px;
  padding: 2rem;
  min-height: 600px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .tabs {
    flex-direction: column;
  }
  
  .tab {
    justify-content: flex-start;
  }
  
  .content {
    padding: 1rem;
  }
}

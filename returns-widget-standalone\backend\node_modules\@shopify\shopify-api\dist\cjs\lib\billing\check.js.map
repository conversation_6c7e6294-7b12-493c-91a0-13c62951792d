{"version": 3, "file": "check.js", "sources": ["../../../../../../lib/billing/check.ts"], "sourcesContent": ["import {FutureFlagOptions} from '../../future/flags';\nimport {ConfigInterface} from '../base-types';\nimport {graphqlClientClass, GraphqlClient} from '../clients/admin';\nimport {BillingError} from '../error';\n\nimport {\n  AppSubscription,\n  BillingCheck,\n  BillingCheckParams,\n  BillingCheckResponse,\n  BillingCheckResponseObject,\n  CurrentAppInstallation,\n  CurrentAppInstallations,\n  OneTimePurchase,\n  APP_SUBSCRIPTION_FRAGMENT,\n} from './types';\nimport {convertLineItems} from './utils';\n\ninterface SubscriptionMeetsCriteriaParams {\n  subscription: AppSubscription;\n  isTest?: boolean;\n  plans?: string | string[];\n}\n\ninterface PurchaseMeetsCriteriaParams {\n  purchase: OneTimePurchase;\n  isTest?: boolean;\n  plans?: string | string[];\n}\n\ninterface InternalParams {\n  client: GraphqlClient;\n  isTest?: boolean;\n  plans?: string | string[];\n}\n\nexport function check<\n  Config extends ConfigInterface,\n  Future extends FutureFlagOptions = Config['future'],\n>(config: Config): BillingCheck<Future> {\n  return async function check<Params extends BillingCheckParams<Future>>(\n    params: Params,\n  ): Promise<BillingCheckResponse<Params, Future>> {\n    if (!config.future?.unstable_managedPricingSupport && !config.billing) {\n      throw new BillingError({\n        message: 'Attempted to look for purchases without billing configs',\n        errorData: [],\n      });\n    }\n\n    const {session, isTest = true, plans} = params;\n    const returnObject =\n      (params as BillingCheckParams<{unstable_managedPricingSupport: false}>)\n        .returnObject ?? false;\n\n    const GraphqlClient = graphqlClientClass({config});\n    const client = new GraphqlClient({session});\n\n    const payments = await assessPayments({client, isTest, plans});\n\n    if (config.future?.unstable_managedPricingSupport || returnObject) {\n      return payments as BillingCheckResponse<Params, Future>;\n    } else {\n      return payments.hasActivePayment as BillingCheckResponse<Params, Future>;\n    }\n  };\n}\n\nexport async function assessPayments({\n  client,\n  isTest,\n  plans,\n}: InternalParams): Promise<BillingCheckResponseObject> {\n  const returnValue: BillingCheckResponseObject = {\n    hasActivePayment: false,\n    oneTimePurchases: [],\n    appSubscriptions: [],\n  };\n\n  let installation: CurrentAppInstallation;\n  let endCursor: string | null = null;\n  do {\n    const currentInstallations = await client.request<CurrentAppInstallations>(\n      HAS_PAYMENTS_QUERY,\n      {variables: {endCursor}},\n    );\n\n    installation = currentInstallations.data?.currentAppInstallation!;\n    installation.activeSubscriptions.forEach((subscription) => {\n      if (subscriptionMeetsCriteria({subscription, isTest, plans})) {\n        returnValue.hasActivePayment = true;\n        if (subscription.lineItems) {\n          subscription.lineItems = convertLineItems(subscription.lineItems);\n        }\n        returnValue.appSubscriptions.push(subscription);\n      }\n    });\n    installation.oneTimePurchases.edges.forEach(({node: purchase}) => {\n      if (purchaseMeetsCriteria({purchase, isTest, plans})) {\n        returnValue.hasActivePayment = true;\n        returnValue.oneTimePurchases.push(purchase);\n      }\n    });\n\n    endCursor = installation.oneTimePurchases.pageInfo.endCursor;\n  } while (installation?.oneTimePurchases.pageInfo.hasNextPage);\n\n  return returnValue;\n}\n\nfunction subscriptionMeetsCriteria({\n  subscription,\n  isTest,\n  plans,\n}: SubscriptionMeetsCriteriaParams): boolean {\n  return (\n    (typeof plans === 'undefined' || plans.includes(subscription.name)) &&\n    (isTest || !subscription.test)\n  );\n}\n\nfunction purchaseMeetsCriteria({\n  purchase,\n  isTest,\n  plans,\n}: PurchaseMeetsCriteriaParams): boolean {\n  return (\n    (typeof plans === 'undefined' || plans.includes(purchase.name)) &&\n    (isTest || !purchase.test) &&\n    purchase.status === 'ACTIVE'\n  );\n}\n\nconst HAS_PAYMENTS_QUERY = `\n  ${APP_SUBSCRIPTION_FRAGMENT}\n  query appSubscription($endCursor: String) {\n    currentAppInstallation {\n      activeSubscriptions {\n        ...AppSubscriptionFragment\n      }\n      oneTimePurchases(first: 250, sortKey: CREATED_AT, after: $endCursor) {\n        edges {\n          node {\n            id\n            name\n            test\n            status\n          }\n        }\n        pageInfo {\n          hasNextPage\n          endCursor\n        }\n      }\n    }\n  }\n`;\n"], "names": ["BillingError", "graphqlClientClass", "client", "convertLineItems", "APP_SUBSCRIPTION_FRAGMENT"], "mappings": ";;;;;;;;;;;;;AAoCM,SAAU,KAAK,CAGnB,MAAc,EAAA;AACd,IAAA,OAAO,eAAe,KAAK,CACzB,MAAc,EAAA;AAEd,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,8BAA8B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACrE,MAAM,IAAIA,kBAAY,CAAC;AACrB,gBAAA,OAAO,EAAE,yDAAyD;AAClE,gBAAA,SAAS,EAAE,EAAE;AACd,aAAA,CAAC;QACJ;QAEA,MAAM,EAAC,OAAO,EAAE,MAAM,GAAG,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM;QAC9C,MAAM,YAAY,GACf;aACE,YAAY,IAAI,KAAK;QAE1B,MAAM,aAAa,GAAGC,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;QAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;AAE3C,QAAA,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,UAACA,QAAM,EAAE,MAAM,EAAE,KAAK,EAAC,CAAC;QAE9D,IAAI,MAAM,CAAC,MAAM,EAAE,8BAA8B,IAAI,YAAY,EAAE;AACjE,YAAA,OAAO,QAAgD;QACzD;aAAO;YACL,OAAO,QAAQ,CAAC,gBAAwD;QAC1E;AACF,IAAA,CAAC;AACH;AAEO,eAAe,cAAc,CAAC,EACnC,MAAM,EACN,MAAM,EACN,KAAK,GACU,EAAA;AACf,IAAA,MAAM,WAAW,GAA+B;AAC9C,QAAA,gBAAgB,EAAE,KAAK;AACvB,QAAA,gBAAgB,EAAE,EAAE;AACpB,QAAA,gBAAgB,EAAE,EAAE;KACrB;AAED,IAAA,IAAI,YAAoC;IACxC,IAAI,SAAS,GAAkB,IAAI;AACnC,IAAA,GAAG;AACD,QAAA,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,OAAO,CAC/C,kBAAkB,EAClB,EAAC,SAAS,EAAE,EAAC,SAAS,EAAC,EAAC,CACzB;AAED,QAAA,YAAY,GAAG,oBAAoB,CAAC,IAAI,EAAE,sBAAuB;QACjE,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,YAAY,KAAI;YACxD,IAAI,yBAAyB,CAAC,EAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAC,CAAC,EAAE;AAC5D,gBAAA,WAAW,CAAC,gBAAgB,GAAG,IAAI;AACnC,gBAAA,IAAI,YAAY,CAAC,SAAS,EAAE;oBAC1B,YAAY,CAAC,SAAS,GAAGC,sBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC;gBACnE;AACA,gBAAA,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;YACjD;AACF,QAAA,CAAC,CAAC;AACF,QAAA,YAAY,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAC,KAAI;YAC/D,IAAI,qBAAqB,CAAC,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAC,CAAC,EAAE;AACpD,gBAAA,WAAW,CAAC,gBAAgB,GAAG,IAAI;AACnC,gBAAA,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7C;AACF,QAAA,CAAC,CAAC;QAEF,SAAS,GAAG,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS;IAC9D,CAAC,QAAQ,YAAY,EAAE,gBAAgB,CAAC,QAAQ,CAAC,WAAW;AAE5D,IAAA,OAAO,WAAW;AACpB;AAEA,SAAS,yBAAyB,CAAC,EACjC,YAAY,EACZ,MAAM,EACN,KAAK,GAC2B,EAAA;AAChC,IAAA,QACE,CAAC,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC;SACjE,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AAElC;AAEA,SAAS,qBAAqB,CAAC,EAC7B,QAAQ,EACR,MAAM,EACN,KAAK,GACuB,EAAA;AAC5B,IAAA,QACE,CAAC,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC9D,SAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC1B,QAAA,QAAQ,CAAC,MAAM,KAAK,QAAQ;AAEhC;AAEA,MAAM,kBAAkB,GAAG;IACvBC,+BAAyB;;;;;;;;;;;;;;;;;;;;;;CAsB5B;;;;;"}
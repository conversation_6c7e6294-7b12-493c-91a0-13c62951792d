{"version": 3, "file": "client.js", "sources": ["../../../../../../../../lib/clients/admin/rest/client.ts"], "sourcesContent": ["import {\n  AdminRestApiClient,\n  createAdminRestApiClient,\n} from '@shopify/admin-api-client';\nimport {Method} from '@shopify/network';\n\nimport {\n  clientLoggerFactory,\n  getUserAgent,\n  throwFailedRequest,\n} from '../../common';\nimport {\n  HashFormat,\n  NormalizedRequest,\n  abstractF<PERSON>ch,\n  canonicalizeHeaders,\n  create<PERSON><PERSON>256<PERSON><PERSON><PERSON>,\n  getHeader,\n} from '../../../../runtime';\nimport {ConfigInterface} from '../../../base-types';\nimport * as ShopifyErrors from '../../../error';\nimport {logger} from '../../../logger';\nimport {\n  RestRequestReturn,\n  PageInfo,\n  RestClientParams,\n  PageInfoParams,\n} from '../types';\nimport type {\n  RequestParams,\n  GetRequestParams,\n  PutRequestParams,\n  PostRequestParams,\n  DeleteRequestParams,\n} from '../../types';\nimport {ApiVersion} from '../../../types';\nimport {Session} from '../../../session/session';\n\nexport interface RestClientClassParams {\n  config: ConfigInterface;\n  formatPaths?: boolean;\n}\n\ninterface DeprecationInterface {\n  message: string | null;\n  path: string;\n  body?: string;\n}\n\nexport class RestClient {\n  public static config: ConfigInterface;\n  public static formatPaths: boolean;\n\n  static LINK_HEADER_REGEXP = /<([^<]+)>; rel=\"([^\"]+)\"/;\n  static DEFAULT_LIMIT = '50';\n  static RETRY_WAIT_TIME = 1000;\n\n  static readonly DEPRECATION_ALERT_DELAY = 300000;\n  loggedDeprecations: Record<string, number> = {};\n\n  readonly client: AdminRestApiClient;\n  readonly session: Session;\n  readonly apiVersion: ApiVersion;\n\n  public constructor({session, apiVersion}: RestClientParams) {\n    const config = this.restClass().config;\n\n    if (!config.isCustomStoreApp && !session.accessToken) {\n      throw new ShopifyErrors.MissingRequiredArgument(\n        'Missing access token when creating REST client',\n      );\n    }\n\n    if (apiVersion) {\n      const message =\n        apiVersion === config.apiVersion\n          ? `REST client has a redundant API version override to the default ${apiVersion}`\n          : `REST client overriding default API version ${config.apiVersion} with ${apiVersion}`;\n\n      logger(config).debug(message);\n    }\n\n    const customStoreAppAccessToken =\n      config.adminApiAccessToken ?? config.apiSecretKey;\n\n    this.session = session;\n    this.apiVersion = apiVersion ?? config.apiVersion;\n    this.client = createAdminRestApiClient({\n      scheme: config.hostScheme,\n      storeDomain: session.shop,\n      apiVersion: apiVersion ?? config.apiVersion,\n      accessToken: config.isCustomStoreApp\n        ? customStoreAppAccessToken\n        : session.accessToken!,\n      customFetchApi: abstractFetch,\n      logger: clientLoggerFactory(config),\n      userAgentPrefix: getUserAgent(config),\n      defaultRetryTime: this.restClass().RETRY_WAIT_TIME,\n      formatPaths: this.restClass().formatPaths,\n      isTesting: config.isTesting,\n    });\n  }\n\n  /**\n   * Performs a GET request on the given path.\n   */\n  public async get<T = any>(params: GetRequestParams) {\n    return this.request<T>({method: Method.Get, ...params});\n  }\n\n  /**\n   * Performs a POST request on the given path.\n   */\n  public async post<T = any>(params: PostRequestParams) {\n    return this.request<T>({method: Method.Post, ...params});\n  }\n\n  /**\n   * Performs a PUT request on the given path.\n   */\n  public async put<T = any>(params: PutRequestParams) {\n    return this.request<T>({method: Method.Put, ...params});\n  }\n\n  /**\n   * Performs a DELETE request on the given path.\n   */\n  public async delete<T = any>(params: DeleteRequestParams) {\n    return this.request<T>({method: Method.Delete, ...params});\n  }\n\n  protected async request<T = any>(\n    params: RequestParams,\n  ): Promise<RestRequestReturn<T>> {\n    const requestParams = {\n      headers: {\n        ...params.extraHeaders,\n        ...(params.type ? {'Content-Type': params.type.toString()} : {}),\n      },\n      retries: params.tries ? params.tries - 1 : undefined,\n      searchParams: params.query,\n    };\n\n    let response: Response;\n    switch (params.method) {\n      case Method.Get:\n        response = await this.client.get(params.path, requestParams);\n        break;\n      case Method.Put:\n        response = await this.client.put(params.path, {\n          ...requestParams,\n          data: params.data!,\n        });\n        break;\n      case Method.Post:\n        response = await this.client.post(params.path, {\n          ...requestParams,\n          data: params.data!,\n        });\n        break;\n      case Method.Delete:\n        response = await this.client.delete(params.path, requestParams);\n        break;\n      default:\n        throw new ShopifyErrors.InvalidRequestError(\n          `Unsupported request method '${params.method}'`,\n        );\n    }\n\n    const bodyString: string = await response.text();\n\n    // Some DELETE requests return an empty body but are still valid responses, we want those to go through\n    const body: any =\n      params.method === Method.Delete && bodyString === ''\n        ? {}\n        : JSON.parse(bodyString);\n\n    const responseHeaders = canonicalizeHeaders(\n      Object.fromEntries(response.headers.entries()),\n    );\n\n    if (!response.ok) {\n      throwFailedRequest(body, (params.tries ?? 1) > 1, response);\n    }\n\n    const requestReturn: RestRequestReturn<T> = {\n      body,\n      headers: responseHeaders,\n    };\n\n    await this.logDeprecations(\n      {\n        method: params.method,\n        url: params.path,\n        headers: requestParams.headers,\n        body: params.data ? JSON.stringify(params.data) : undefined,\n      },\n      requestReturn,\n    );\n\n    const link = response.headers.get('Link');\n    if (link !== undefined) {\n      const pageInfo: PageInfo = {\n        limit: params.query?.limit\n          ? params.query?.limit.toString()\n          : RestClient.DEFAULT_LIMIT,\n      };\n\n      if (link) {\n        const links = link.split(', ');\n\n        for (const link of links) {\n          const parsedLink = link.match(RestClient.LINK_HEADER_REGEXP);\n          if (!parsedLink) {\n            continue;\n          }\n\n          const linkRel = parsedLink[2];\n          const linkUrl = new URL(parsedLink[1]);\n          const linkFields = linkUrl.searchParams.get('fields');\n          const linkPageToken = linkUrl.searchParams.get('page_info');\n\n          if (!pageInfo.fields && linkFields) {\n            pageInfo.fields = linkFields.split(',');\n          }\n\n          if (linkPageToken) {\n            switch (linkRel) {\n              case 'previous':\n                pageInfo.previousPageUrl = parsedLink[1];\n                pageInfo.prevPage = this.buildRequestParams(parsedLink[1]);\n                break;\n              case 'next':\n                pageInfo.nextPageUrl = parsedLink[1];\n                pageInfo.nextPage = this.buildRequestParams(parsedLink[1]);\n                break;\n            }\n          }\n        }\n      }\n\n      requestReturn.pageInfo = pageInfo;\n    }\n\n    return requestReturn;\n  }\n\n  private restClass() {\n    return this.constructor as typeof RestClient;\n  }\n\n  private buildRequestParams(newPageUrl: string): PageInfoParams {\n    const pattern = `^/admin/api/[^/]+/(.*).json$`;\n\n    const url = new URL(newPageUrl);\n    const path = url.pathname.replace(new RegExp(pattern), '$1');\n    return {\n      path,\n      query: Object.fromEntries(url.searchParams.entries()),\n    };\n  }\n\n  private async logDeprecations(\n    request: NormalizedRequest,\n    response: RestRequestReturn,\n  ) {\n    const config = this.restClass().config;\n\n    const deprecationReason = getHeader(\n      response.headers,\n      'X-Shopify-API-Deprecated-Reason',\n    );\n    if (deprecationReason) {\n      const deprecation: DeprecationInterface = {\n        message: deprecationReason,\n        path: request.url,\n      };\n\n      if (request.body) {\n        // This can only be a string, since we're always converting the body before calling this method\n        deprecation.body = `${(request.body as string).substring(0, 100)}...`;\n      }\n\n      const depHash = await createSHA256HMAC(\n        config.apiSecretKey,\n        JSON.stringify(deprecation),\n        HashFormat.Hex,\n      );\n\n      if (\n        !Object.keys(this.loggedDeprecations).includes(depHash) ||\n        Date.now() - this.loggedDeprecations[depHash] >=\n          RestClient.DEPRECATION_ALERT_DELAY\n      ) {\n        this.loggedDeprecations[depHash] = Date.now();\n\n        const stack = new Error().stack;\n        const message = `API Deprecation Notice ${new Date().toLocaleString()} : ${JSON.stringify(\n          deprecation,\n        )}  -  Stack Trace: ${stack}`;\n        await logger(config).warning(message);\n      }\n    }\n  }\n}\n\nexport function restClientClass(\n  params: RestClientClassParams,\n): typeof RestClient {\n  const {config, formatPaths} = params;\n\n  class NewRestClient extends RestClient {\n    public static config = config;\n    public static formatPaths = formatPaths === undefined ? true : formatPaths;\n  }\n\n  Reflect.defineProperty(NewRestClient, 'name', {\n    value: 'RestClient',\n  });\n\n  return NewRestClient as typeof RestClient;\n}\n"], "names": ["ShopifyErrors.MissingRequiredArgument", "logger", "createAdminRestApiClient", "abstractFetch", "clientLoggerFactory", "getUserAgent", "Method", "ShopifyErrors.InvalidRequestError", "canonicalizeHeaders", "throwFailedRequest", "<PERSON><PERSON><PERSON><PERSON>", "createSHA256HMAC", "HashFormat"], "mappings": ";;;;;;;;;;;;;MAiDa,UAAU,CAAA;IACd,OAAO,MAAM;IACb,OAAO,WAAW;AAEzB,IAAA,OAAO,kBAAkB,GAAG,0BAA0B;AACtD,IAAA,OAAO,aAAa,GAAG,IAAI;AAC3B,IAAA,OAAO,eAAe,GAAG,IAAI;AAE7B,IAAA,OAAgB,uBAAuB,GAAG,MAAM;IAChD,kBAAkB,GAA2B,EAAE;AAEtC,IAAA,MAAM;AACN,IAAA,OAAO;AACP,IAAA,UAAU;AAEnB,IAAA,WAAA,CAAmB,EAAC,OAAO,EAAE,UAAU,EAAmB,EAAA;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM;QAEtC,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AACpD,YAAA,MAAM,IAAIA,6BAAqC,CAC7C,gDAAgD,CACjD;QACH;QAEA,IAAI,UAAU,EAAE;AACd,YAAA,MAAM,OAAO,GACX,UAAU,KAAK,MAAM,CAAC;kBAClB,CAAA,gEAAA,EAAmE,UAAU,CAAA;kBAC7E,8CAA8C,MAAM,CAAC,UAAU,CAAA,MAAA,EAAS,UAAU,EAAE;YAE1FC,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QAC/B;QAEA,MAAM,yBAAyB,GAC7B,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,YAAY;AAEnD,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,MAAM,CAAC,UAAU;AACjD,QAAA,IAAI,CAAC,MAAM,GAAGC,uCAAwB,CAAC;YACrC,MAAM,EAAE,MAAM,CAAC,UAAU;YACzB,WAAW,EAAE,OAAO,CAAC,IAAI;AACzB,YAAA,UAAU,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU;YAC3C,WAAW,EAAE,MAAM,CAAC;AAClB,kBAAE;kBACA,OAAO,CAAC,WAAY;AACxB,YAAA,cAAc,EAAEC,qBAAa;AAC7B,YAAA,MAAM,EAAEC,0BAAmB,CAAC,MAAM,CAAC;AACnC,YAAA,eAAe,EAAEC,mBAAY,CAAC,MAAM,CAAC;AACrC,YAAA,gBAAgB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,eAAe;AAClD,YAAA,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,WAAW;YACzC,SAAS,EAAE,MAAM,CAAC,SAAS;AAC5B,SAAA,CAAC;IACJ;AAEA;;AAEG;IACI,MAAM,GAAG,CAAU,MAAwB,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAI,EAAC,MAAM,EAAEC,cAAM,CAAC,GAAG,EAAE,GAAG,MAAM,EAAC,CAAC;IACzD;AAEA;;AAEG;IACI,MAAM,IAAI,CAAU,MAAyB,EAAA;AAClD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAI,EAAC,MAAM,EAAEA,cAAM,CAAC,IAAI,EAAE,GAAG,MAAM,EAAC,CAAC;IAC1D;AAEA;;AAEG;IACI,MAAM,GAAG,CAAU,MAAwB,EAAA;AAChD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAI,EAAC,MAAM,EAAEA,cAAM,CAAC,GAAG,EAAE,GAAG,MAAM,EAAC,CAAC;IACzD;AAEA;;AAEG;IACI,MAAM,MAAM,CAAU,MAA2B,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAI,EAAC,MAAM,EAAEA,cAAM,CAAC,MAAM,EAAE,GAAG,MAAM,EAAC,CAAC;IAC5D;IAEU,MAAM,OAAO,CACrB,MAAqB,EAAA;AAErB,QAAA,MAAM,aAAa,GAAG;AACpB,YAAA,OAAO,EAAE;gBACP,GAAG,MAAM,CAAC,YAAY;gBACtB,IAAI,MAAM,CAAC,IAAI,GAAG,EAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC,GAAG,EAAE,CAAC;AACjE,aAAA;AACD,YAAA,OAAO,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS;YACpD,YAAY,EAAE,MAAM,CAAC,KAAK;SAC3B;AAED,QAAA,IAAI,QAAkB;AACtB,QAAA,QAAQ,MAAM,CAAC,MAAM;YACnB,KAAKA,cAAM,CAAC,GAAG;AACb,gBAAA,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC;gBAC5D;YACF,KAAKA,cAAM,CAAC,GAAG;gBACb,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;AAC5C,oBAAA,GAAG,aAAa;oBAChB,IAAI,EAAE,MAAM,CAAC,IAAK;AACnB,iBAAA,CAAC;gBACF;YACF,KAAKA,cAAM,CAAC,IAAI;gBACd,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAC7C,oBAAA,GAAG,aAAa;oBAChB,IAAI,EAAE,MAAM,CAAC,IAAK;AACnB,iBAAA,CAAC;gBACF;YACF,KAAKA,cAAM,CAAC,MAAM;AAChB,gBAAA,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC;gBAC/D;AACF,YAAA;gBACE,MAAM,IAAIC,yBAAiC,CACzC,CAAA,4BAAA,EAA+B,MAAM,CAAC,MAAM,CAAA,CAAA,CAAG,CAChD;;AAGL,QAAA,MAAM,UAAU,GAAW,MAAM,QAAQ,CAAC,IAAI,EAAE;;AAGhD,QAAA,MAAM,IAAI,GACR,MAAM,CAAC,MAAM,KAAKD,cAAM,CAAC,MAAM,IAAI,UAAU,KAAK;AAChD,cAAE;AACF,cAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;AAE5B,QAAA,MAAM,eAAe,GAAGE,2BAAmB,CACzC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAC/C;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AAChB,YAAAC,yBAAkB,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC;QAC7D;AAEA,QAAA,MAAM,aAAa,GAAyB;YAC1C,IAAI;AACJ,YAAA,OAAO,EAAE,eAAe;SACzB;QAED,MAAM,IAAI,CAAC,eAAe,CACxB;YACE,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,GAAG,EAAE,MAAM,CAAC,IAAI;YAChB,OAAO,EAAE,aAAa,CAAC,OAAO;AAC9B,YAAA,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS;SAC5D,EACD,aAAa,CACd;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;AACzC,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,MAAM,QAAQ,GAAa;AACzB,gBAAA,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE;sBACjB,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ;sBAC5B,UAAU,CAAC,aAAa;aAC7B;YAED,IAAI,IAAI,EAAE;gBACR,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAE9B,gBAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;oBACxB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,CAAC;oBAC5D,IAAI,CAAC,UAAU,EAAE;wBACf;oBACF;AAEA,oBAAA,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC;oBAC7B,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACtC,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;oBACrD,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AAE3D,oBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,UAAU,EAAE;wBAClC,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;oBACzC;oBAEA,IAAI,aAAa,EAAE;wBACjB,QAAQ,OAAO;AACb,4BAAA,KAAK,UAAU;AACb,gCAAA,QAAQ,CAAC,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC;AACxC,gCAAA,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gCAC1D;AACF,4BAAA,KAAK,MAAM;AACT,gCAAA,QAAQ,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC;AACpC,gCAAA,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gCAC1D;;oBAEN;gBACF;YACF;AAEA,YAAA,aAAa,CAAC,QAAQ,GAAG,QAAQ;QACnC;AAEA,QAAA,OAAO,aAAa;IACtB;IAEQ,SAAS,GAAA;QACf,OAAO,IAAI,CAAC,WAAgC;IAC9C;AAEQ,IAAA,kBAAkB,CAAC,UAAkB,EAAA;QAC3C,MAAM,OAAO,GAAG,CAAA,4BAAA,CAA8B;AAE9C,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC;AAC/B,QAAA,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC;QAC5D,OAAO;YACL,IAAI;YACJ,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SACtD;IACH;AAEQ,IAAA,MAAM,eAAe,CAC3B,OAA0B,EAC1B,QAA2B,EAAA;QAE3B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM;QAEtC,MAAM,iBAAiB,GAAGC,iBAAS,CACjC,QAAQ,CAAC,OAAO,EAChB,iCAAiC,CAClC;QACD,IAAI,iBAAiB,EAAE;AACrB,YAAA,MAAM,WAAW,GAAyB;AACxC,gBAAA,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE,OAAO,CAAC,GAAG;aAClB;AAED,YAAA,IAAI,OAAO,CAAC,IAAI,EAAE;;AAEhB,gBAAA,WAAW,CAAC,IAAI,GAAG,CAAA,EAAI,OAAO,CAAC,IAAe,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;YACvE;YAEA,MAAM,OAAO,GAAG,MAAMC,sBAAgB,CACpC,MAAM,CAAC,YAAY,EACnB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAC3BC,gBAAU,CAAC,GAAG,CACf;AAED,YAAA,IACE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACvD,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;oBAC3C,UAAU,CAAC,uBAAuB,EACpC;gBACA,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE;AAE7C,gBAAA,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK;AAC/B,gBAAA,MAAM,OAAO,GAAG,CAAA,uBAAA,EAA0B,IAAI,IAAI,EAAE,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC,SAAS,CACvF,WAAW,CACZ,CAAA,kBAAA,EAAqB,KAAK,EAAE;gBAC7B,MAAMX,YAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YACvC;QACF;IACF;;AAGI,SAAU,eAAe,CAC7B,MAA6B,EAAA;AAE7B,IAAA,MAAM,EAAC,MAAM,EAAE,WAAW,EAAC,GAAG,MAAM;IAEpC,MAAM,aAAc,SAAQ,UAAU,CAAA;AAC7B,QAAA,OAAO,MAAM,GAAG,MAAM;AACtB,QAAA,OAAO,WAAW,GAAG,WAAW,KAAK,SAAS,GAAG,IAAI,GAAG,WAAW;;AAG5E,IAAA,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE;AAC5C,QAAA,KAAK,EAAE,YAAY;AACpB,KAAA,CAAC;AAEF,IAAA,OAAO,aAAkC;AAC3C;;;;;"}
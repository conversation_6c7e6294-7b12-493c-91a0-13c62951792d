{"version": 3, "file": "index.js", "sources": ["../../../../../../adapters/mock/index.ts"], "sourcesContent": ["import crypto from 'crypto';\n\nimport {\n  setAbstractFetchFunc,\n  setAbstractConvertRequestFunc,\n  setAbstractConvertResponseFunc,\n  setAbstractConvertHeadersFunc,\n  setAbstractRuntimeString,\n  setCrypto,\n} from '../../runtime';\n\nimport {\n  mockFetch,\n  mockConvertRequest,\n  mockConvertResponse,\n  mockConvertHeaders,\n  mockRuntimeString,\n} from './adapter';\n\nsetAbstractFetchFunc(mockFetch);\nsetAbstractConvertRequestFunc(mockConvertRequest);\nsetAbstractConvertResponseFunc(mockConvertResponse);\nsetAbstractConvertHeadersFunc(mockConvertHeaders);\nsetAbstractRuntimeString(mockRuntimeString);\nsetCrypto(crypto as any);\n"], "names": ["setAbstractFetchFunc", "mockFetch", "setAbstractConvertRequestFunc", "mockConvertRequest", "setAbstractConvertResponseFunc", "mockConvertResponse", "setAbstractConvertHeadersFunc", "mockConvertHeaders", "setAbstractRuntimeString", "mockRuntimeString", "setCrypto", "crypto"], "mappings": ";;;;;;;;;AAmBAA,0BAAoB,CAACC,iBAAS,CAAC;AAC/BC,mCAA6B,CAACC,0BAAkB,CAAC;AACjDC,oCAA8B,CAACC,2BAAmB,CAAC;AACnDC,mCAA6B,CAACC,0BAAkB,CAAC;AACjDC,sCAAwB,CAACC,yBAAiB,CAAC;AAC3CC,gBAAS,CAACC,QAAa,CAAC;;"}
'use strict';

var error = require('../../error.js');
var client = require('../admin/graphql/client.js');
require('@shopify/admin-api-client');
require('@shopify/network');
require('../../types.js');
require('../../../runtime/crypto/crypto.js');
require('../../../runtime/crypto/types.js');
require('compare-versions');

function graphqlProxy(config) {
    return async ({ session, rawBody }) => {
        if (!session.accessToken) {
            throw new error.InvalidSession('Cannot proxy query. Session not authenticated.');
        }
        const GraphqlClient = client.graphqlClientClass({ config });
        const client$1 = new GraphqlClient({ session });
        let query;
        let variables;
        if (typeof rawBody === 'string') {
            query = rawBody;
        }
        else {
            query = rawBody.query;
            variables = rawBody.variables;
        }
        if (!query) {
            throw new error.MissingRequiredArgument('Query missing.');
        }
        const response = await client$1.request(query, { variables });
        return { body: response, headers: {} };
    };
}

exports.graphqlProxy = graphqlProxy;
//# sourceMappingURL=graphql_proxy.js.map

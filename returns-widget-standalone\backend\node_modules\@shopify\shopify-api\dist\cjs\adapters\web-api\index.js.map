{"version": 3, "file": "index.js", "sources": ["../../../../../../adapters/web-api/index.ts"], "sourcesContent": ["import {\n  setAbstractFetchFunc,\n  setAbstractConvertRequestFunc,\n  setAbstractConvertResponseFunc,\n  setAbstractConvertHeadersFunc,\n  setAbstractRuntimeString,\n} from '../../runtime';\n\nimport {\n  webApiConvertHeaders,\n  webApiConvertRequest,\n  webApiConvertResponse,\n  webApiRuntimeString,\n} from './adapter';\n\nsetAbstractFetchFunc(fetch);\nsetAbstractConvertRequestFunc(webApiConvertRequest);\nsetAbstractConvertResponseFunc(webApiConvertResponse);\nsetAbstractConvertHeadersFunc(webApiConvertHeaders);\nsetAbstractRuntimeString(webApiRuntimeString);\n"], "names": ["setAbstractFetchFunc", "setAbstractConvertRequestFunc", "webApiConvertRequest", "setAbstractConvertResponseFunc", "webApiConvertResponse", "setAbstractConvertHeadersFunc", "webApiConvertHeaders", "setAbstractRuntimeString", "webApiRuntimeString"], "mappings": ";;;;;;;;AAeAA,0BAAoB,CAAC,KAAK,CAAC;AAC3BC,mCAA6B,CAACC,4BAAoB,CAAC;AACnDC,oCAA8B,CAACC,6BAAqB,CAAC;AACrDC,mCAA6B,CAACC,4BAAoB,CAAC;AACnDC,sCAAwB,CAACC,2BAAmB,CAAC;;"}
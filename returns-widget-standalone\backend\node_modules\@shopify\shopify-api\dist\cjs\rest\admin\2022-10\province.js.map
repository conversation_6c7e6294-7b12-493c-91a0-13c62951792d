{"version": 3, "file": "province.js", "sources": ["../../../../../../../rest/admin/2022-10/province.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  country_id?: number | string | null;\n  fields?: unknown;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  country_id?: number | string | null;\n  since_id?: unknown;\n  fields?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n  country_id?: number | string | null;\n}\n\nexport class Province extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [\"country_id\"], \"path\": \"countries/<country_id>/provinces/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"country_id\"], \"path\": \"countries/<country_id>/provinces.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"country_id\", \"id\"], \"path\": \"countries/<country_id>/provinces/<id>.json\"},\n    {\"http_method\": \"put\", \"operation\": \"put\", \"ids\": [\"country_id\", \"id\"], \"path\": \"countries/<country_id>/provinces/<id>.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"province\",\n      \"plural\": \"provinces\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      country_id = null,\n      fields = null\n    }: FindArgs\n  ): Promise<Province | null> {\n    const result = await this.baseFind<Province>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id, \"country_id\": country_id},\n      params: {\"fields\": fields},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async all(\n    {\n      session,\n      country_id = null,\n      since_id = null,\n      fields = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Province>> {\n    const response = await this.baseFind<Province>({\n      session: session,\n      urlIds: {\"country_id\": country_id},\n      params: {\"since_id\": since_id, \"fields\": fields, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      country_id = null,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<Province>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {\"country_id\": country_id},\n      params: {...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public code: string | null;\n  public country_id: number | null;\n  public id: number | null;\n  public name: string | null;\n  public shipping_zone_id: number | null;\n  public tax: number | null;\n  public tax_name: string | null;\n  public tax_percentage: number | null;\n  public tax_type: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AA0BlH,MAAO,QAAS,SAAQA,SAAI,CAAA;AACzB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,6CAA6C,EAAC;AAC1H,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,uCAAuC,EAAC;AAClH,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,4CAA4C,EAAC;AAC7H,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,4CAA4C;KAC7H;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,UAAU;AACtB,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,UAAU,GAAG,IAAI,EACjB,MAAM,GAAG,IAAI,EACJ,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAW;AAC3C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAE,YAAY,EAAE,UAAU,EAAC;AAC5C,YAAA,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAC;AAC3B,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,GAAG,CACrB,EACE,OAAO,EACP,UAAU,GAAG,IAAI,EACjB,QAAQ,GAAG,IAAI,EACf,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAW;AAC7C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,YAAY,EAAE,UAAU,EAAC;AAClC,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AAC/D,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,aAAa,KAAK,CACvB,EACE,OAAO,EACP,UAAU,GAAG,IAAI,EACjB,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAW;AAC5C,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,YAAY,EAAE,UAAU,EAAC;AAClC,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACtB,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,IAAI;AACJ,IAAA,UAAU;AACV,IAAA,EAAE;AACF,IAAA,IAAI;AACJ,IAAA,gBAAgB;AAChB,IAAA,GAAG;AACH,IAAA,QAAQ;AACR,IAAA,cAAc;AACd,IAAA,QAAQ;;;;;"}
'use strict';

var crypto$1 = require('crypto');
var index = require('../../runtime/http/index.js');
require('../../runtime/crypto/types.js');
var crypto = require('../../runtime/crypto/crypto.js');
var runtimeString = require('../../runtime/platform/runtime-string.js');
var adapter = require('./adapter.js');

index.setAbstractFetchFunc(adapter.mockFetch);
index.setAbstractConvertRequestFunc(adapter.mockConvertRequest);
index.setAbstractConvertResponseFunc(adapter.mockConvertResponse);
index.setAbstractConvertHeadersFunc(adapter.mockConvertHeaders);
runtimeString.setAbstractRuntimeString(adapter.mockRuntimeString);
crypto.setCrypto(crypto$1);
//# sourceMappingURL=index.js.map

.sidebar {
  width: 256px;
  background: white;
  border-right: 1px solid #e5e7eb;
  padding: 1rem 0;
}

.nav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 1rem;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #6b7280;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s;
}

.navItem:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.navItem.active {
  background-color: #3b82f6;
  color: white;
}

.navItem.active:hover {
  background-color: #2563eb;
}

.navItem span {
  font-size: 0.875rem;
}

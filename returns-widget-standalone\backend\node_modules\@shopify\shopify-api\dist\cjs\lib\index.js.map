{"version": 3, "file": "index.js", "sources": ["../../../../../lib/index.ts"], "sourcesContent": ["import {loadRestResources} from '../rest/load-rest-resources';\nimport {ShopifyRestResources} from '../rest/types';\nimport {abstractRuntimeString} from '../runtime/platform';\nimport {FutureFlagOptions, logDisabledFutureFlags} from '../future/flags';\n\nimport {ConfigParams, ConfigInterface} from './base-types';\nimport {validateConfig} from './config';\nimport {clientClasses, ShopifyClients} from './clients';\nimport {shopifyAuth, ShopifyAuth} from './auth';\nimport {shopifySession, ShopifySession} from './session';\nimport {shopifyUtils, ShopifyUtils} from './utils';\nimport {shopifyWebhooks, ShopifyWebhooks} from './webhooks';\nimport {shopifyBilling, ShopifyBilling} from './billing';\nimport {logger, ShopifyLogger} from './logger';\nimport {SHOPIFY_API_LIBRARY_VERSION} from './version';\nimport {restClientClass} from './clients/admin/rest/client';\nimport {ShopifyFlow, shopifyFlow} from './flow';\nimport {FulfillmentService, fulfillmentService} from './fulfillment-service';\n\nexport * from './error';\nexport * from './session/classes';\n\nexport * from '../rest/types';\nexport * from './types';\nexport * from './base-types';\nexport * from './auth/types';\nexport * from './billing/types';\nexport * from './clients/types';\nexport * from './session/types';\nexport * from './webhooks/types';\nexport * from './utils/types';\n\nexport interface Shopify<\n  Params extends ConfigParams = ConfigParams,\n  Resources extends ShopifyRestResources = ShopifyRestResources,\n  Future extends FutureFlagOptions = FutureFlagOptions,\n> {\n  config: ConfigInterface<Params>;\n  clients: ShopifyClients;\n  auth: ShopifyAuth;\n  session: ShopifySession;\n  utils: ShopifyUtils;\n\n  /**\n   * Functions for working with webhooks.\n   *\n   * Most of these functions are used for interacting with shop-specific webhooks.\n   * Unless your app needs different webhooks for different shops, we recommend using app-specific webhooks instead:\n   *\n   * {@link https://shopify.dev/docs/apps/build/webhooks/subscribe#app-specific-subscriptions}\n   *\n   * If you use only app-specific webhooks, the only function you will need is `shopify.webhooks.validate`.\n   */\n  webhooks: ShopifyWebhooks;\n  billing: ShopifyBilling<Future>;\n  logger: ShopifyLogger;\n  rest: Resources;\n  flow: ShopifyFlow;\n  fulfillmentService: FulfillmentService;\n}\n\nexport function shopifyApi<\n  Params extends ConfigParams<Resources, Future>,\n  Resources extends ShopifyRestResources,\n  Future extends FutureFlagOptions,\n>({\n  future,\n  restResources,\n  ...config\n}: {future?: Future; restResources?: Resources} & Params): Shopify<\n  Params,\n  Resources,\n  Future\n> {\n  const libConfig = {...config, future, restResources};\n  const validatedConfig = validateConfig(libConfig);\n\n  const shopify = {\n    config: validatedConfig,\n    clients: clientClasses(validatedConfig),\n    auth: shopifyAuth(validatedConfig),\n    session: shopifySession(validatedConfig),\n    utils: shopifyUtils(validatedConfig),\n    webhooks: shopifyWebhooks(validatedConfig),\n    billing: shopifyBilling(validatedConfig),\n    flow: shopifyFlow(validatedConfig),\n    fulfillmentService: fulfillmentService(validatedConfig),\n    logger: logger(validatedConfig),\n    rest: {} as Resources,\n  };\n\n  if (restResources) {\n    shopify.rest = loadRestResources({\n      resources: restResources,\n      config: validatedConfig,\n      RestClient: restClientClass({config: validatedConfig}),\n    });\n  }\n\n  shopify.logger\n    .info(\n      `version ${SHOPIFY_API_LIBRARY_VERSION}, environment ${abstractRuntimeString()}`,\n    )\n    .catch((err) => console.log(err));\n\n  logDisabledFutureFlags(validatedConfig, shopify.logger);\n\n  return shopify as Shopify<Params, Resources, Future>;\n}\n"], "names": ["config", "validateConfig", "clientClasses", "shopifyAuth", "shopifySession", "shopifyUtils", "shopifyWebhooks", "shopifyBilling", "shopifyFlow", "fulfillmentService", "logger", "loadRestResources", "restClientClass", "SHOPIFY_API_LIBRARY_VERSION", "abstractRuntimeString", "logDisabledFutureFlags"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DM,SAAU,UAAU,CAIxB,EACA,MAAM,EACN,aAAa,EACb,GAAGA,QAAM,EAC6C,EAAA;IAKtD,MAAM,SAAS,GAAG,EAAC,GAAGA,QAAM,EAAE,MAAM,EAAE,aAAa,EAAC;AACpD,IAAA,MAAM,eAAe,GAAGC,qBAAc,CAAC,SAAS,CAAC;AAEjD,IAAA,MAAM,OAAO,GAAG;AACd,QAAA,MAAM,EAAE,eAAe;AACvB,QAAA,OAAO,EAAEC,qBAAa,CAAC,eAAe,CAAC;AACvC,QAAA,IAAI,EAAEC,mBAAW,CAAC,eAAe,CAAC;AAClC,QAAA,OAAO,EAAEC,sBAAc,CAAC,eAAe,CAAC;AACxC,QAAA,KAAK,EAAEC,oBAAY,CAAC,eAAe,CAAC;AACpC,QAAA,QAAQ,EAAEC,uBAAe,CAAC,eAAe,CAAC;AAC1C,QAAA,OAAO,EAAEC,sBAAc,CAAC,eAAe,CAAC;AACxC,QAAA,IAAI,EAAEC,mBAAW,CAAC,eAAe,CAAC;AAClC,QAAA,kBAAkB,EAAEC,0BAAkB,CAAC,eAAe,CAAC;AACvD,QAAA,MAAM,EAAEC,cAAM,CAAC,eAAe,CAAC;AAC/B,QAAA,IAAI,EAAE,EAAe;KACtB;IAED,IAAI,aAAa,EAAE;AACjB,QAAA,OAAO,CAAC,IAAI,GAAGC,mCAAiB,CAAC;AAC/B,YAAA,SAAS,EAAE,aAAa;AACxB,YAAA,MAAM,EAAE,eAAe;YACvB,UAAU,EAAEC,wBAAe,CAAC,EAAC,MAAM,EAAE,eAAe,EAAC,CAAC;AACvD,SAAA,CAAC;IACJ;AAEA,IAAA,OAAO,CAAC;AACL,SAAA,IAAI,CACH,CAAA,QAAA,EAAWC,mCAA2B,iBAAiBC,mCAAqB,EAAE,EAAE;AAEjF,SAAA,KAAK,CAAC,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEnC,IAAAC,4BAAsB,CAAC,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC;AAEvD,IAAA,OAAO,OAA6C;AACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
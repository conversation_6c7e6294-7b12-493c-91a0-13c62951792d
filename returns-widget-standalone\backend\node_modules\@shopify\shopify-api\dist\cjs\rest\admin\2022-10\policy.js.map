{"version": 3, "file": "policy.js", "sources": ["../../../../../../../rest/admin/2022-10/policy.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n}\n\nexport class Policy extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"policies.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"policy\",\n      \"plural\": \"policies\"\n    }\n  ];\n\n  public static async all(\n    {\n      session,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Policy>> {\n    const response = await this.baseFind<Policy>({\n      session: session,\n      urlIds: {},\n      params: {...otherArgs},\n    });\n\n    return response;\n  }\n\n  public body: string | null;\n  public created_at: string | null;\n  public handle: string | null;\n  public title: string | null;\n  public updated_at: string | null;\n  public url: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAYlH,MAAO,MAAO,SAAQA,SAAI,CAAA;AACvB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,eAAe;KAC9E;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,GAAG,CACrB,EACE,OAAO,EACP,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAS;AAC3C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,GAAG,SAAS,EAAC;AACvB,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,IAAI;AACJ,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,GAAG;;;;;"}
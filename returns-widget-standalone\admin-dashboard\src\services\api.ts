import type { 
  AuthUser, 
  ReturnRecord, 
  MerchantSettings,
  ShopifyCredentials,
  SendcloudCredentials,
  DashboardMetrics,
  ApiResponse 
} from './types'

const API_BASE = import.meta.env.VITE_API_URL || 'http://localhost:3000/api'

export class ApiService {
  private getHeaders(): HeadersInit {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const result: ApiResponse<T> = await response.json()
    
    if (!response.ok || !result.success) {
      throw new Error(result.error || `API Error: ${response.statusText}`)
    }
    
    return result.data!
  }

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      headers: this.getHeaders()
    })
    
    return this.handleResponse<T>(response)
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    })
    
    return this.handleResponse<T>(response)
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    })
    
    return this.handleResponse<T>(response)
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'DELETE',
      headers: this.getHeaders()
    })
    
    return this.handleResponse<T>(response)
  }

  // Dashboard endpoints
  async getMerchant(): Promise<AuthUser> {
    return this.get<AuthUser>('/dashboard/merchant')
  }

  async getReturns(): Promise<ReturnRecord[]> {
    console.log('Making API call to /dashboard/returns')
    const token = localStorage.getItem('token')
    console.log('Using token:', token ? 'Token present' : 'No token')
    return this.get<ReturnRecord[]>('/dashboard/returns')
  }

  async getReturnDetails(returnId: string): Promise<ReturnRecord> {
    return this.get<ReturnRecord>(`/dashboard/returns/${returnId}`)
  }

  async updateReturnStatus(returnId: string, status: ReturnRecord['status']): Promise<void> {
    return this.put<void>(`/dashboard/returns/${returnId}/status`, { status })
  }

  async searchOrders(searchTerm: string): Promise<{ orders: any[] }> {
    return this.get<{ orders: any[] }>(`/dashboard/orders/search?q=${encodeURIComponent(searchTerm)}`)
  }

  async createReturn(returnData: {
    orderId: string;
    orderNumber: string;
    customerEmail: string;
    returnItems: Array<{
      id: string;
      title: string;
      quantity: number;
      reason: string;
      note?: string;
    }>;
    notes?: string;
  }): Promise<{ returnId: string; message: string }> {
    return this.post<{ returnId: string; message: string }>('/dashboard/returns/create', returnData)
  }

  async getDashboardMetrics(): Promise<DashboardMetrics> {
    // This will be implemented in the backend later
    // For now, we'll derive it from the returns data
    console.log('Fetching returns for dashboard metrics...')
    const returns = await this.getReturns()
    console.log('Returns fetched:', returns)

    const metrics = {
      totalReturns: returns.length,
      pendingReturns: returns.filter(r => r.status === 'pending').length,
      approvedReturns: returns.filter(r => r.status === 'approved').length,
      rejectedReturns: returns.filter(r => r.status === 'rejected').length,
      recentReturns: returns.slice(0, 5)
    }

    console.log('Dashboard metrics calculated:', metrics)
    return metrics
  }

  async getAnalytics(startDate?: string, endDate?: string): Promise<any> {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const query = params.toString() ? `?${params}` : '';
    return this.get<any>(`/dashboard/analytics${query}`);
  }

  // Credentials endpoints
  async saveShopifyCredentials(credentials: ShopifyCredentials): Promise<void> {
    return this.post<void>('/credentials/shopify', credentials)
  }

  async saveSendcloudCredentials(credentials: SendcloudCredentials): Promise<void> {
    return this.post<void>('/credentials/sendcloud', credentials)
  }

  async testShopifyConnection(): Promise<{ connected: boolean; shopName?: string }> {
    return this.post<{ connected: boolean; shopName?: string }>('/credentials/shopify/test', {})
  }

  async testSendcloudConnection(): Promise<{ connected: boolean; carrierCount?: number }> {
    return this.post<{ connected: boolean; carrierCount?: number }>('/credentials/sendcloud/test', {})
  }

  // Settings endpoints (to be implemented in backend)
  async getSettings(): Promise<MerchantSettings> {
    // Placeholder - will be implemented in backend
    return {}
  }

  async updateSettings(settings: MerchantSettings): Promise<void> {
    // Placeholder - will be implemented in backend
    return this.put<void>('/settings', settings)
  }
}

export const api = new ApiService()

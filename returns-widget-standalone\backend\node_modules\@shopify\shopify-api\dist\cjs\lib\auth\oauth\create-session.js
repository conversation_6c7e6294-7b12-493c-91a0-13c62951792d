'use strict';

var uuid = require('uuid');
var session = require('../../session/session.js');
var index = require('../../logger/index.js');
var sessionUtils = require('../../session/session-utils.js');

function createSession({ config, accessTokenResponse, shop, state, }) {
    const associatedUser = accessTokenResponse
        .associated_user;
    const isOnline = Boolean(associatedUser);
    index.logger(config).info('Creating new session', { shop, isOnline });
    const getSessionExpiration = (expires_in) => new Date(Date.now() + expires_in * 1000);
    const getOnlineSessionProperties = (responseBody) => {
        const { access_token, scope, ...rest } = responseBody;
        const sessionId = config.isEmbeddedApp
            ? sessionUtils.getJwtSessionId(config)(shop, `${rest.associated_user.id}`)
            : uuid.v4();
        return {
            id: sessionId,
            onlineAccessInfo: rest,
            expires: getSessionExpiration(rest.expires_in),
        };
    };
    const getOfflineSessionProperties = (responseBody) => {
        const { expires_in } = responseBody;
        return {
            id: sessionUtils.getOfflineId(config)(shop),
            ...(expires_in && { expires: getSessionExpiration(expires_in) }),
        };
    };
    return new session.Session({
        shop,
        state,
        isOnline,
        accessToken: accessTokenResponse.access_token,
        scope: accessTokenResponse.scope,
        ...(isOnline
            ? getOnlineSessionProperties(accessTokenResponse)
            : getOfflineSessionProperties(accessTokenResponse)),
    });
}

exports.createSession = createSession;
//# sourceMappingURL=create-session.js.map

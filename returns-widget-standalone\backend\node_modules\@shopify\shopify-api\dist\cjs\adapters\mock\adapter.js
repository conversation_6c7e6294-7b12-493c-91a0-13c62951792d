'use strict';

var fetch = require('node-fetch');
require('../../runtime/crypto/crypto.js');
require('../../runtime/crypto/types.js');
var headers = require('../../runtime/http/headers.js');
var mock_test_requests = require('./mock_test_requests.js');

async function mockConvertRequest(adapterArgs) {
    return Promise.resolve(adapterArgs.rawRequest);
}
async function mockConvertResponse(response, _adapterArgs) {
    return Promise.resolve(response);
}
async function mockConvertHeaders(headers, _adapterArgs) {
    return Promise.resolve(headers);
}
const mockFetch = async (url, init) => {
    const mockInit = init;
    const request = new fetch.Request(url, mockInit);
    const headers$1 = Object.fromEntries(new fetch.Headers(mockInit?.headers).entries());
    mock_test_requests.mockTestRequests.requestList.push({
        url: request.url,
        method: request.method,
        headers: headers.canonicalizeHeaders(headers$1),
        body: await request.text(),
    });
    const next = mock_test_requests.mockTestRequests.responseList.shift();
    if (!next) {
        throw new Error(`Missing mock for ${request.method} to ${url}, have you queued all required responses?`);
    }
    if (next instanceof Error) {
        throw next;
    }
    const responseHeaders = new fetch.Headers();
    Object.entries(next.headers ?? {}).forEach(([key, value]) => {
        responseHeaders.set(key, typeof value === 'string' ? value : value.join(', '));
    });
    return new fetch.Response(next.body, {
        status: next.statusCode,
        statusText: next.statusText,
        headers: responseHeaders,
    });
};
function mockRuntimeString() {
    return 'Mock adapter';
}

exports.mockConvertHeaders = mockConvertHeaders;
exports.mockConvertRequest = mockConvertRequest;
exports.mockConvertResponse = mockConvertResponse;
exports.mockFetch = mockFetch;
exports.mockRuntimeString = mockRuntimeString;
//# sourceMappingURL=adapter.js.map

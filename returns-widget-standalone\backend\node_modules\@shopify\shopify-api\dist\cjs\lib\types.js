'use strict';

exports.LogSeverity = void 0;
(function (LogSeverity) {
    LogSeverity[LogSeverity["Error"] = 0] = "Error";
    LogSeverity[LogSeverity["Warning"] = 1] = "Warning";
    LogSeverity[LogSeverity["Info"] = 2] = "Info";
    LogSeverity[LogSeverity["Debug"] = 3] = "Debug";
})(exports.LogSeverity || (exports.LogSeverity = {}));
exports.ApiVersion = void 0;
(function (ApiVersion) {
    ApiVersion["October22"] = "2022-10";
    ApiVersion["January23"] = "2023-01";
    ApiVersion["April23"] = "2023-04";
    ApiVersion["July23"] = "2023-07";
    ApiVersion["October23"] = "2023-10";
    ApiVersion["January24"] = "2024-01";
    ApiVersion["April24"] = "2024-04";
    ApiVersion["July24"] = "2024-07";
    ApiVersion["October24"] = "2024-10";
    ApiVersion["January25"] = "2025-01";
    ApiVersion["April25"] = "2025-04";
    ApiVersion["July25"] = "2025-07";
    ApiVersion["October25"] = "2025-10";
    ApiVersion["Unstable"] = "unstable";
})(exports.ApiVersion || (exports.ApiVersion = {}));
const LIBRARY_NAME = 'Shopify API Library';
const LATEST_API_VERSION = exports.ApiVersion.July25;
const RELEASE_CANDIDATE_API_VERSION = exports.ApiVersion.October25;
/* eslint-disable @shopify/typescript/prefer-pascal-case-enums */
exports.ShopifyHeader = void 0;
(function (ShopifyHeader) {
    ShopifyHeader["AccessToken"] = "X-Shopify-Access-Token";
    ShopifyHeader["ApiVersion"] = "X-Shopify-API-Version";
    ShopifyHeader["Domain"] = "X-Shopify-Shop-Domain";
    ShopifyHeader["Hmac"] = "X-Shopify-Hmac-Sha256";
    ShopifyHeader["Topic"] = "X-Shopify-Topic";
    ShopifyHeader["SubTopic"] = "X-Shopify-Sub-Topic";
    ShopifyHeader["WebhookId"] = "X-Shopify-Webhook-Id";
    ShopifyHeader["StorefrontPrivateToken"] = "Shopify-Storefront-Private-Token";
    ShopifyHeader["StorefrontSDKVariant"] = "X-SDK-Variant";
    ShopifyHeader["StorefrontSDKVersion"] = "X-SDK-Version";
})(exports.ShopifyHeader || (exports.ShopifyHeader = {}));
/* eslint-enable @shopify/typescript/prefer-pascal-case-enums */
exports.ClientType = void 0;
(function (ClientType) {
    ClientType["Rest"] = "rest";
    ClientType["Graphql"] = "graphql";
})(exports.ClientType || (exports.ClientType = {}));
const privacyTopics = [
    'CUSTOMERS_DATA_REQUEST',
    'CUSTOMERS_REDACT',
    'SHOP_REDACT',
];
exports.BillingInterval = void 0;
(function (BillingInterval) {
    BillingInterval["OneTime"] = "ONE_TIME";
    BillingInterval["Every30Days"] = "EVERY_30_DAYS";
    BillingInterval["Annual"] = "ANNUAL";
    BillingInterval["Usage"] = "USAGE";
})(exports.BillingInterval || (exports.BillingInterval = {}));
exports.BillingReplacementBehavior = void 0;
(function (BillingReplacementBehavior) {
    BillingReplacementBehavior["ApplyImmediately"] = "APPLY_IMMEDIATELY";
    BillingReplacementBehavior["ApplyOnNextBillingCycle"] = "APPLY_ON_NEXT_BILLING_CYCLE";
    BillingReplacementBehavior["Standard"] = "STANDARD";
})(exports.BillingReplacementBehavior || (exports.BillingReplacementBehavior = {}));

exports.LATEST_API_VERSION = LATEST_API_VERSION;
exports.LIBRARY_NAME = LIBRARY_NAME;
exports.RELEASE_CANDIDATE_API_VERSION = RELEASE_CANDIDATE_API_VERSION;
exports.privacyTopics = privacyTopics;
//# sourceMappingURL=types.js.map

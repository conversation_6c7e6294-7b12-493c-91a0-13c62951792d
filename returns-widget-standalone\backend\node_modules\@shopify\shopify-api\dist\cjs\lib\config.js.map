{"version": 3, "file": "config.js", "sources": ["../../../../../lib/config.ts"], "sourcesContent": ["import {ShopifyError} from './error';\nimport {ConfigInterface, ConfigParams} from './base-types';\nimport {LATEST_API_VERSION, LogSeverity} from './types';\nimport {AuthScopes} from './auth/scopes';\nimport {logger as createLogger} from './logger';\n\nexport function validateConfig<Params extends ConfigParams>(\n  params: Params,\n): ConfigInterface<Params> {\n  const config = {\n    apiKey: '',\n    apiSecretKey: '',\n    hostName: '',\n    hostScheme: 'https',\n    apiVersion: LATEST_API_VERSION,\n    isEmbeddedApp: true,\n    isCustomStoreApp: false,\n    logger: {\n      log: defaultLogFunction,\n      level: LogSeverity.Info,\n      httpRequests: false,\n      timestamps: false,\n    },\n    future: {},\n    _logDisabledFutureFlags: true,\n  } as ConfigInterface<Params>;\n\n  // Make sure that the essential params actually have content in them\n  const mandatory: (keyof Params)[] = ['apiSecretKey', 'hostName'];\n  if (!('isCustomStoreApp' in params) || !params.isCustomStoreApp) {\n    mandatory.push('apiKey');\n  }\n  if ('isCustomStoreApp' in params && params.isCustomStoreApp) {\n    if (\n      !('adminApiAccessToken' in params) ||\n      params.adminApiAccessToken?.length === 0\n    ) {\n      mandatory.push('adminApiAccessToken');\n    }\n  }\n\n  const missing: (keyof Params)[] = [];\n  mandatory.forEach((key) => {\n    if (!notEmpty(params[key])) {\n      missing.push(key);\n    }\n  });\n\n  if (missing.length) {\n    throw new ShopifyError(\n      `Cannot initialize Shopify API Library. Missing values for: ${missing.join(\n        ', ',\n      )}`,\n    );\n  }\n\n  // Alias the v10_lineItemBilling flag to lineItemBilling because we aren't releasing in v10\n  const future = params.future?.v10_lineItemBilling\n    ? {\n        lineItemBilling: params.future?.v10_lineItemBilling,\n        ...params.future,\n      }\n    : params.future;\n\n  const {\n    hostScheme,\n    isCustomStoreApp,\n    adminApiAccessToken,\n    userAgentPrefix,\n    logger,\n    privateAppStorefrontAccessToken,\n    customShopDomains,\n    billing,\n    ...mandatoryParams\n  } = params;\n\n  let scopes;\n  if (params.scopes === undefined) {\n    scopes = undefined;\n  } else if (params.scopes instanceof AuthScopes) {\n    scopes = params.scopes;\n  } else {\n    scopes = new AuthScopes(params.scopes);\n  }\n\n  Object.assign(config, mandatoryParams, {\n    hostName: params.hostName.replace(/\\/$/, ''),\n    scopes,\n    hostScheme: hostScheme ?? config.hostScheme,\n    isCustomStoreApp: isCustomStoreApp ?? config.isCustomStoreApp,\n    adminApiAccessToken: adminApiAccessToken ?? config.adminApiAccessToken,\n    userAgentPrefix: userAgentPrefix ?? config.userAgentPrefix,\n    logger: {...config.logger, ...(logger || {})},\n    privateAppStorefrontAccessToken:\n      privateAppStorefrontAccessToken ?? config.privateAppStorefrontAccessToken,\n    customShopDomains: customShopDomains ?? config.customShopDomains,\n    billing: billing ?? config.billing,\n    future: future ?? config.future,\n  });\n\n  if (\n    config.isCustomStoreApp &&\n    params.adminApiAccessToken === params.apiSecretKey\n  ) {\n    createLogger(config).warning(\n      \"adminApiAccessToken is set to the same value as apiSecretKey. adminApiAccessToken should be set to the Admin API access token for custom store apps; apiSecretKey should be set to the custom store app's API secret key.\",\n    );\n  }\n\n  return config;\n}\n\nfunction notEmpty<T>(value: T): value is NonNullable<T> {\n  if (value == null) {\n    return false;\n  }\n  return typeof value === 'string' || Array.isArray(value)\n    ? value.length > 0\n    : true;\n}\n\nfunction defaultLogFunction(severity: LogSeverity, message: string): void {\n  switch (severity) {\n    case LogSeverity.Debug:\n      console.debug(message);\n      break;\n    case LogSeverity.Info:\n      console.log(message);\n      break;\n    case LogSeverity.Warning:\n      console.warn(message);\n      break;\n    case LogSeverity.Error:\n      console.error(message);\n      break;\n  }\n}\n"], "names": ["LATEST_API_VERSION", "LogSeverity", "ShopifyError", "AuthScopes", "createLogger"], "mappings": ";;;;;;;AAMM,SAAU,cAAc,CAC5B,MAAc,EAAA;AAEd,IAAA,MAAM,MAAM,GAAG;AACb,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,YAAY,EAAE,EAAE;AAChB,QAAA,QAAQ,EAAE,EAAE;AACZ,QAAA,UAAU,EAAE,OAAO;AACnB,QAAA,UAAU,EAAEA,wBAAkB;AAC9B,QAAA,aAAa,EAAE,IAAI;AACnB,QAAA,gBAAgB,EAAE,KAAK;AACvB,QAAA,MAAM,EAAE;AACN,YAAA,GAAG,EAAE,kBAAkB;YACvB,KAAK,EAAEC,iBAAW,CAAC,IAAI;AACvB,YAAA,YAAY,EAAE,KAAK;AACnB,YAAA,UAAU,EAAE,KAAK;AAClB,SAAA;AACD,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,uBAAuB,EAAE,IAAI;KACH;;AAG5B,IAAA,MAAM,SAAS,GAAqB,CAAC,cAAc,EAAE,UAAU,CAAC;AAChE,IAAA,IAAI,EAAE,kBAAkB,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;AAC/D,QAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC1B;IACA,IAAI,kBAAkB,IAAI,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE;AAC3D,QAAA,IACE,EAAE,qBAAqB,IAAI,MAAM,CAAC;AAClC,YAAA,MAAM,CAAC,mBAAmB,EAAE,MAAM,KAAK,CAAC,EACxC;AACA,YAAA,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACvC;IACF;IAEA,MAAM,OAAO,GAAqB,EAAE;AACpC,IAAA,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;QACxB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AAC1B,YAAA,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QACnB;AACF,IAAA,CAAC,CAAC;AAEF,IAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,QAAA,MAAM,IAAIC,kBAAY,CACpB,CAAA,2DAAA,EAA8D,OAAO,CAAC,IAAI,CACxE,IAAI,CACL,CAAA,CAAE,CACJ;IACH;;AAGA,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE;AAC5B,UAAE;AACE,YAAA,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,mBAAmB;YACnD,GAAG,MAAM,CAAC,MAAM;AACjB;AACH,UAAE,MAAM,CAAC,MAAM;IAEjB,MAAM,EACJ,UAAU,EACV,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,MAAM,EACN,+BAA+B,EAC/B,iBAAiB,EACjB,OAAO,EACP,GAAG,eAAe,EACnB,GAAG,MAAM;AAEV,IAAA,IAAI,MAAM;AACV,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;QAC/B,MAAM,GAAG,SAAS;IACpB;AAAO,SAAA,IAAI,MAAM,CAAC,MAAM,YAAYC,gBAAU,EAAE;AAC9C,QAAA,MAAM,GAAG,MAAM,CAAC,MAAM;IACxB;SAAO;QACL,MAAM,GAAG,IAAIA,gBAAU,CAAC,MAAM,CAAC,MAAM,CAAC;IACxC;AAEA,IAAA,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE;QACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;QAC5C,MAAM;AACN,QAAA,UAAU,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU;AAC3C,QAAA,gBAAgB,EAAE,gBAAgB,IAAI,MAAM,CAAC,gBAAgB;AAC7D,QAAA,mBAAmB,EAAE,mBAAmB,IAAI,MAAM,CAAC,mBAAmB;AACtE,QAAA,eAAe,EAAE,eAAe,IAAI,MAAM,CAAC,eAAe;AAC1D,QAAA,MAAM,EAAE,EAAC,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,MAAM,IAAI,EAAE,CAAC,EAAC;AAC7C,QAAA,+BAA+B,EAC7B,+BAA+B,IAAI,MAAM,CAAC,+BAA+B;AAC3E,QAAA,iBAAiB,EAAE,iBAAiB,IAAI,MAAM,CAAC,iBAAiB;AAChE,QAAA,OAAO,EAAE,OAAO,IAAI,MAAM,CAAC,OAAO;AAClC,QAAA,MAAM,EAAE,MAAM,IAAI,MAAM,CAAC,MAAM;AAChC,KAAA,CAAC;IAEF,IACE,MAAM,CAAC,gBAAgB;AACvB,QAAA,MAAM,CAAC,mBAAmB,KAAK,MAAM,CAAC,YAAY,EAClD;QACAC,cAAY,CAAC,MAAM,CAAC,CAAC,OAAO,CAC1B,2NAA2N,CAC5N;IACH;AAEA,IAAA,OAAO,MAAM;AACf;AAEA,SAAS,QAAQ,CAAI,KAAQ,EAAA;AAC3B,IAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,QAAA,OAAO,KAAK;IACd;IACA,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK;AACrD,UAAE,KAAK,CAAC,MAAM,GAAG;UACf,IAAI;AACV;AAEA,SAAS,kBAAkB,CAAC,QAAqB,EAAE,OAAe,EAAA;IAChE,QAAQ,QAAQ;QACd,KAAKH,iBAAW,CAAC,KAAK;AACpB,YAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;YACtB;QACF,KAAKA,iBAAW,CAAC,IAAI;AACnB,YAAA,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;YACpB;QACF,KAAKA,iBAAW,CAAC,OAAO;AACtB,YAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;YACrB;QACF,KAAKA,iBAAW,CAAC,KAAK;AACpB,YAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;YACtB;;AAEN;;;;"}
{"version": 3, "file": "subscriptions.js", "sources": ["../../../../../../lib/billing/subscriptions.ts"], "sourcesContent": ["import {BillingError} from '../error';\nimport {ConfigInterface} from '../base-types';\nimport {graphqlClientClass} from '../clients/admin';\n\nimport {\n  ActiveSubscriptions,\n  BillingSubscriptionParams,\n  BillingSubscriptions,\n  SubscriptionResponse,\n  APP_SUBSCRIPTION_FRAGMENT,\n} from './types';\nimport {convertLineItems} from './utils';\n\nconst SUBSCRIPTION_QUERY = `\n${APP_SUBSCRIPTION_FRAGMENT}\nquery appSubscription {\n  currentAppInstallation {\n    activeSubscriptions {\n      ...AppSubscriptionFragment\n    }\n  }\n}\n`;\n\nexport function subscriptions(config: ConfigInterface): BillingSubscriptions {\n  return async function ({\n    session,\n  }: BillingSubscriptionParams): Promise<ActiveSubscriptions> {\n    if (!config.future?.unstable_managedPricingSupport && !config.billing) {\n      throw new BillingError({\n        message: 'Attempted to look for purchases without billing configs',\n        errorData: [],\n      });\n    }\n\n    const GraphqlClient = graphqlClientClass({config});\n    const client = new GraphqlClient({session});\n\n    const response =\n      await client.request<SubscriptionResponse>(SUBSCRIPTION_QUERY);\n\n    if (!response.data?.currentAppInstallation?.activeSubscriptions) {\n      return {activeSubscriptions: []};\n    }\n\n    const activeSubscriptions =\n      response.data.currentAppInstallation.activeSubscriptions;\n    activeSubscriptions.forEach((subscription) => {\n      if (subscription.lineItems) {\n        subscription.lineItems = convertLineItems(subscription.lineItems);\n      }\n    });\n\n    return {\n      activeSubscriptions,\n    };\n  };\n}\n"], "names": ["APP_SUBSCRIPTION_FRAGMENT", "BillingError", "graphqlClientClass", "client", "convertLineItems"], "mappings": ";;;;;;;;;;;;;AAaA,MAAM,kBAAkB,GAAG;EACzBA,+BAAyB;;;;;;;;CAQ1B;AAEK,SAAU,aAAa,CAAC,MAAuB,EAAA;AACnD,IAAA,OAAO,gBAAgB,EACrB,OAAO,GACmB,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,8BAA8B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACrE,MAAM,IAAIC,kBAAY,CAAC;AACrB,gBAAA,OAAO,EAAE,yDAAyD;AAClE,gBAAA,SAAS,EAAE,EAAE;AACd,aAAA,CAAC;QACJ;QAEA,MAAM,aAAa,GAAGC,yBAAkB,CAAC,EAAC,MAAM,EAAC,CAAC;QAClD,MAAMC,QAAM,GAAG,IAAI,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;QAE3C,MAAM,QAAQ,GACZ,MAAMA,QAAM,CAAC,OAAO,CAAuB,kBAAkB,CAAC;QAEhE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,sBAAsB,EAAE,mBAAmB,EAAE;AAC/D,YAAA,OAAO,EAAC,mBAAmB,EAAE,EAAE,EAAC;QAClC;QAEA,MAAM,mBAAmB,GACvB,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,mBAAmB;AAC1D,QAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,YAAY,KAAI;AAC3C,YAAA,IAAI,YAAY,CAAC,SAAS,EAAE;gBAC1B,YAAY,CAAC,SAAS,GAAGC,sBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC;YACnE;AACF,QAAA,CAAC,CAAC;QAEF,OAAO;YACL,mBAAmB;SACpB;AACH,IAAA,CAAC;AACH;;;;"}
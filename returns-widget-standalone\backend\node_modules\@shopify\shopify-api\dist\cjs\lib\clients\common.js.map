{"version": 3, "file": "common.js", "sources": ["../../../../../../lib/clients/common.ts"], "sourcesContent": ["import {\n  H<PERSON><PERSON>esponseLog,\n  HTTPRetryLog,\n  HTTPResponseGraphQLDeprecationNotice,\n  LogContent,\n} from '@shopify/admin-api-client';\nimport {StatusCode} from '@shopify/network';\n\nimport * as ShopifyErrors from '../error';\nimport {LIBRARY_NAME} from '../types';\nimport {ConfigInterface} from '../base-types';\nimport {SHOPIFY_API_LIBRARY_VERSION} from '../version';\nimport {\n  abstractRuntimeString,\n  canonicalizeHeaders,\n  getHeader,\n} from '../../runtime';\nimport {logger} from '../logger';\n\nexport function getUserAgent(config: ConfigInterface): string {\n  let userAgentPrefix = `${LIBRARY_NAME} v${SHOPIFY_API_LIBRARY_VERSION} | ${abstractRuntimeString()}`;\n  if (config.userAgentPrefix) {\n    userAgentPrefix = `${config.userAgentPrefix} | ${userAgentPrefix}`;\n  }\n\n  return userAgentPrefix;\n}\n\nfunction serializeResponse(response: Response | any) {\n  if (!response) {\n    return {error: 'No response object provided'};\n  }\n\n  try {\n    const {status, statusText, ok, redirected, type, url, headers} = response;\n\n    const serialized: any = {\n      status,\n      statusText,\n      ok,\n      redirected,\n      type,\n      url,\n    };\n\n    if (headers?.entries) {\n      serialized.headers = Object.fromEntries(headers.entries());\n    } else if (headers) {\n      serialized.headers = headers;\n    }\n\n    return serialized;\n  } catch {\n    return response;\n  }\n}\n\nexport function clientLoggerFactory(config: ConfigInterface) {\n  return (logContent: LogContent) => {\n    if (config.logger.httpRequests) {\n      switch (logContent.type) {\n        case 'HTTP-Response': {\n          const responseLog: HTTPResponseLog['content'] = logContent.content;\n          logger(config).debug('Received response for HTTP request', {\n            requestParams: JSON.stringify(responseLog.requestParams),\n            response: JSON.stringify(serializeResponse(responseLog.response)),\n          });\n          break;\n        }\n        case 'HTTP-Retry': {\n          const responseLog: HTTPRetryLog['content'] = logContent.content;\n          logger(config).debug('Retrying HTTP request', {\n            requestParams: JSON.stringify(responseLog.requestParams),\n            retryAttempt: responseLog.retryAttempt,\n            maxRetries: responseLog.maxRetries,\n            response: responseLog.lastResponse\n              ? JSON.stringify(serializeResponse(responseLog.lastResponse))\n              : 'undefined',\n          });\n          break;\n        }\n        case 'HTTP-Response-GraphQL-Deprecation-Notice': {\n          const responseLog: HTTPResponseGraphQLDeprecationNotice['content'] =\n            logContent.content;\n          logger(config).debug(\n            'Received response containing Deprecated GraphQL Notice',\n            {\n              requestParams: JSON.stringify(responseLog.requestParams),\n              deprecationNotice: responseLog.deprecationNotice,\n            },\n          );\n          break;\n        }\n        default: {\n          logger(config).debug(`HTTP request event: ${logContent.content}`);\n          break;\n        }\n      }\n    }\n  };\n}\n\nexport function throwFailedRequest(\n  body: any,\n  atMaxRetries: boolean,\n  response?: Response,\n): never {\n  if (typeof response === 'undefined') {\n    const message = body?.errors?.message ?? '';\n    throw new ShopifyErrors.HttpRequestError(\n      `Http request error, no response available: ${message}`,\n    );\n  }\n\n  const responseHeaders = canonicalizeHeaders(\n    Object.fromEntries(response.headers.entries() ?? []),\n  );\n\n  if (response.status === StatusCode.Ok && body.errors.graphQLErrors) {\n    throw new ShopifyErrors.GraphqlQueryError({\n      message:\n        body.errors.graphQLErrors?.[0].message ?? 'GraphQL operation failed',\n      response: response as Record<string, any>,\n      headers: responseHeaders,\n      body: body as Record<string, any>,\n    });\n  }\n\n  const errorMessages: string[] = [];\n  if (body.errors) {\n    errorMessages.push(JSON.stringify(body.errors, null, 2));\n  }\n  const xRequestId = getHeader(responseHeaders, 'x-request-id');\n  if (xRequestId) {\n    errorMessages.push(\n      `If you report this error, please include this id: ${xRequestId}`,\n    );\n  }\n\n  const errorMessage = errorMessages.length\n    ? `:\\n${errorMessages.join('\\n')}`\n    : '';\n  const code = response.status;\n  const statusText = response.statusText;\n\n  switch (true) {\n    case response.status === StatusCode.TooManyRequests: {\n      if (atMaxRetries) {\n        throw new ShopifyErrors.HttpMaxRetriesError(\n          'Attempted the maximum number of retries for HTTP request.',\n        );\n      } else {\n        const retryAfter = getHeader(responseHeaders, 'Retry-After');\n        throw new ShopifyErrors.HttpThrottlingError({\n          message: `Shopify is throttling requests ${errorMessage}`,\n          code,\n          statusText,\n          body,\n          headers: responseHeaders,\n          retryAfter: retryAfter ? parseFloat(retryAfter) : undefined,\n        });\n      }\n    }\n    case response.status >= StatusCode.InternalServerError:\n      if (atMaxRetries) {\n        throw new ShopifyErrors.HttpMaxRetriesError(\n          'Attempted the maximum number of retries for HTTP request.',\n        );\n      } else {\n        throw new ShopifyErrors.HttpInternalError({\n          message: `Shopify internal error${errorMessage}`,\n          code,\n          statusText,\n          body,\n          headers: responseHeaders,\n        });\n      }\n    default:\n      throw new ShopifyErrors.HttpResponseError({\n        message: `Received an error response (${response.status} ${response.statusText}) from Shopify${errorMessage}`,\n        code,\n        statusText,\n        body,\n        headers: responseHeaders,\n      });\n  }\n}\n"], "names": ["LIBRARY_NAME", "SHOPIFY_API_LIBRARY_VERSION", "abstractRuntimeString", "logger", "ShopifyErrors.HttpRequestError", "canonicalizeHeaders", "StatusCode", "ShopifyErrors.GraphqlQueryError", "<PERSON><PERSON><PERSON><PERSON>", "ShopifyErrors.HttpMaxRetriesError", "ShopifyErrors.HttpThrottlingError", "ShopifyErrors.HttpInternalError", "ShopifyErrors.HttpResponseError"], "mappings": ";;;;;;;;;;;;AAmBM,SAAU,YAAY,CAAC,MAAuB,EAAA;IAClD,IAAI,eAAe,GAAG,CAAA,EAAGA,kBAAY,CAAA,EAAA,EAAKC,mCAA2B,CAAA,GAAA,EAAMC,mCAAqB,EAAE,CAAA,CAAE;AACpG,IAAA,IAAI,MAAM,CAAC,eAAe,EAAE;QAC1B,eAAe,GAAG,GAAG,MAAM,CAAC,eAAe,CAAA,GAAA,EAAM,eAAe,EAAE;IACpE;AAEA,IAAA,OAAO,eAAe;AACxB;AAEA,SAAS,iBAAiB,CAAC,QAAwB,EAAA;IACjD,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,OAAO,EAAC,KAAK,EAAE,6BAA6B,EAAC;IAC/C;AAEA,IAAA,IAAI;AACF,QAAA,MAAM,EAAC,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAC,GAAG,QAAQ;AAEzE,QAAA,MAAM,UAAU,GAAQ;YACtB,MAAM;YACN,UAAU;YACV,EAAE;YACF,UAAU;YACV,IAAI;YACJ,GAAG;SACJ;AAED,QAAA,IAAI,OAAO,EAAE,OAAO,EAAE;AACpB,YAAA,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC5D;aAAO,IAAI,OAAO,EAAE;AAClB,YAAA,UAAU,CAAC,OAAO,GAAG,OAAO;QAC9B;AAEA,QAAA,OAAO,UAAU;IACnB;AAAE,IAAA,MAAM;AACN,QAAA,OAAO,QAAQ;IACjB;AACF;AAEM,SAAU,mBAAmB,CAAC,MAAuB,EAAA;IACzD,OAAO,CAAC,UAAsB,KAAI;AAChC,QAAA,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE;AAC9B,YAAA,QAAQ,UAAU,CAAC,IAAI;gBACrB,KAAK,eAAe,EAAE;AACpB,oBAAA,MAAM,WAAW,GAA+B,UAAU,CAAC,OAAO;AAClE,oBAAAC,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,oCAAoC,EAAE;wBACzD,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC;wBACxD,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAClE,qBAAA,CAAC;oBACF;gBACF;gBACA,KAAK,YAAY,EAAE;AACjB,oBAAA,MAAM,WAAW,GAA4B,UAAU,CAAC,OAAO;AAC/D,oBAAAA,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,uBAAuB,EAAE;wBAC5C,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC;wBACxD,YAAY,EAAE,WAAW,CAAC,YAAY;wBACtC,UAAU,EAAE,WAAW,CAAC,UAAU;wBAClC,QAAQ,EAAE,WAAW,CAAC;8BAClB,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,WAAW,CAAC,YAAY,CAAC;AAC5D,8BAAE,WAAW;AAChB,qBAAA,CAAC;oBACF;gBACF;gBACA,KAAK,0CAA0C,EAAE;AAC/C,oBAAA,MAAM,WAAW,GACf,UAAU,CAAC,OAAO;AACpB,oBAAAA,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAClB,wDAAwD,EACxD;wBACE,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC;wBACxD,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;AACjD,qBAAA,CACF;oBACD;gBACF;gBACA,SAAS;AACP,oBAAAA,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAA,oBAAA,EAAuB,UAAU,CAAC,OAAO,CAAA,CAAE,CAAC;oBACjE;gBACF;;QAEJ;AACF,IAAA,CAAC;AACH;SAEgB,kBAAkB,CAChC,IAAS,EACT,YAAqB,EACrB,QAAmB,EAAA;AAEnB,IAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;QACnC,MAAM,OAAO,GAAG,IAAI,EAAE,MAAM,EAAE,OAAO,IAAI,EAAE;QAC3C,MAAM,IAAIC,sBAA8B,CACtC,CAAA,2CAAA,EAA8C,OAAO,CAAA,CAAE,CACxD;IACH;AAEA,IAAA,MAAM,eAAe,GAAGC,2BAAmB,CACzC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CACrD;AAED,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAKC,kBAAU,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;AAClE,QAAA,MAAM,IAAIC,uBAA+B,CAAC;AACxC,YAAA,OAAO,EACL,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,0BAA0B;AACtE,YAAA,QAAQ,EAAE,QAA+B;AACzC,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,IAAI,EAAE,IAA2B;AAClC,SAAA,CAAC;IACJ;IAEA,MAAM,aAAa,GAAa,EAAE;AAClC,IAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,QAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1D;IACA,MAAM,UAAU,GAAGC,iBAAS,CAAC,eAAe,EAAE,cAAc,CAAC;IAC7D,IAAI,UAAU,EAAE;AACd,QAAA,aAAa,CAAC,IAAI,CAChB,qDAAqD,UAAU,CAAA,CAAE,CAClE;IACH;AAEA,IAAA,MAAM,YAAY,GAAG,aAAa,CAAC;UAC/B,MAAM,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;UAC9B,EAAE;AACN,IAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM;AAC5B,IAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU;IAEtC,QAAQ,IAAI;QACV,KAAK,QAAQ,CAAC,MAAM,KAAKF,kBAAU,CAAC,eAAe,EAAE;YACnD,IAAI,YAAY,EAAE;AAChB,gBAAA,MAAM,IAAIG,yBAAiC,CACzC,2DAA2D,CAC5D;YACH;iBAAO;gBACL,MAAM,UAAU,GAAGD,iBAAS,CAAC,eAAe,EAAE,aAAa,CAAC;AAC5D,gBAAA,MAAM,IAAIE,yBAAiC,CAAC;oBAC1C,OAAO,EAAE,CAAA,+BAAA,EAAkC,YAAY,CAAA,CAAE;oBACzD,IAAI;oBACJ,UAAU;oBACV,IAAI;AACJ,oBAAA,OAAO,EAAE,eAAe;AACxB,oBAAA,UAAU,EAAE,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,SAAS;AAC5D,iBAAA,CAAC;YACJ;QACF;AACA,QAAA,KAAK,QAAQ,CAAC,MAAM,IAAIJ,kBAAU,CAAC,mBAAmB;YACpD,IAAI,YAAY,EAAE;AAChB,gBAAA,MAAM,IAAIG,yBAAiC,CACzC,2DAA2D,CAC5D;YACH;iBAAO;AACL,gBAAA,MAAM,IAAIE,uBAA+B,CAAC;oBACxC,OAAO,EAAE,CAAA,sBAAA,EAAyB,YAAY,CAAA,CAAE;oBAChD,IAAI;oBACJ,UAAU;oBACV,IAAI;AACJ,oBAAA,OAAO,EAAE,eAAe;AACzB,iBAAA,CAAC;YACJ;AACF,QAAA;AACE,YAAA,MAAM,IAAIC,uBAA+B,CAAC;gBACxC,OAAO,EAAE,CAAA,4BAAA,EAA+B,QAAQ,CAAC,MAAM,CAAA,CAAA,EAAI,QAAQ,CAAC,UAAU,CAAA,cAAA,EAAiB,YAAY,CAAA,CAAE;gBAC7G,IAAI;gBACJ,UAAU;gBACV,IAAI;AACJ,gBAAA,OAAO,EAAE,eAAe;AACzB,aAAA,CAAC;;AAER;;;;;;"}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

.header {
  margin-bottom: 1.5rem;
}

.header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 1rem;
  transition: color 0.2s;
}

.backButton:hover {
  color: #2563eb;
}

.returnHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.returnTitle {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.statusBadge.approved {
  background-color: #d1fae5;
  color: #065f46;
}

.statusBadge.rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

.statusBadge.completed {
  background-color: #e0e7ff;
  color: #3730a3;
}

.detailsGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.itemCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
}

.itemImage {
  width: 4rem;
  height: 4rem;
  background-color: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.itemDetails {
  flex: 1;
}

.itemTitle {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
  color: #1f2937;
}

.itemMeta {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timelineItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.timelineIcon {
  width: 2rem;
  height: 2rem;
  background-color: #dbeafe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.timelineContent {
  flex: 1;
}

.timelineTitle {
  margin: 0 0 0.125rem 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.timelineDate {
  margin: 0;
  font-size: 0.75rem;
  color: #6b7280;
}

.customerInfo {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.infoLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.infoValue {
  font-size: 0.875rem;
  color: #1f2937;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.approveButton {
  background-color: #16a34a;
  color: white;
}

.approveButton:hover {
  background-color: #15803d;
}

.rejectButton {
  background-color: #dc2626;
  color: white;
}

.rejectButton:hover {
  background-color: #b91c1c;
}

.filterTabs {
  margin-bottom: 1.5rem;
}

.tabsContainer {
  border-bottom: 1px solid #e5e7eb;
}

.tabsList {
  display: flex;
  gap: 2rem;
  margin: 0;
  padding: 0;
  list-style: none;
}

.tab {
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  padding: 0.75rem 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.tab:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.loading {
  text-align: center;
  padding: 3rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loadingText {
  color: #6b7280;
  margin: 0;
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
}

.emptyIcon {
  margin: 0 auto 1rem;
  color: #9ca3af;
}

.emptyTitle {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #1f2937;
}

.emptyDescription {
  margin: 0;
  color: #6b7280;
}

.table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.tableContainer {
  overflow-x: auto;
}

.tableElement {
  width: 100%;
  border-collapse: collapse;
}

.tableHeader {
  background-color: #f9fafb;
}

.tableHeaderCell {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tableRow {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
}

.tableRow:hover {
  background-color: #f9fafb;
}

.tableCell {
  padding: 1rem 1.5rem;
  white-space: nowrap;
}

.cellContent {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.primaryText {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.secondaryText {
  font-size: 0.875rem;
  color: #6b7280;
}

.viewButton {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: color 0.2s;
}

.viewButton:hover {
  color: #2563eb;
}

@media (max-width: 1024px) {
  .detailsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .returnHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .tabsList {
    gap: 1rem;
  }
  
  .tableContainer {
    font-size: 0.875rem;
  }
  
  .tableHeaderCell,
  .tableCell {
    padding: 0.5rem 0.75rem;
  }
}

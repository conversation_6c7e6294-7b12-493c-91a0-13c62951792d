#!/usr/bin/env node

/**
 * MCP Server for Shopify GraphQL API Integration
 * Provides tools for GitHub Copilot to interact with Shopify Admin API
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

class ShopifyMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'shopify-api-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'shopify_find_order',
            description: 'Find a Shopify order by order number or email',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'Search query (order number or email)',
                },
                accessToken: {
                  type: 'string',
                  description: 'Shopify access token',
                },
                shopDomain: {
                  type: 'string',
                  description: 'Shopify domain (e.g., mystore.myshopify.com)',
                },
              },
              required: ['query', 'accessToken', 'shopDomain'],
            },
          },
          {
            name: 'shopify_create_return',
            description: 'Create a return in Shopify',
            inputSchema: {
              type: 'object',
              properties: {
                orderId: {
                  type: 'string',
                  description: 'Shopify order ID',
                },
                returnLineItems: {
                  type: 'array',
                  description: 'Items to return',
                  items: {
                    type: 'object',
                    properties: {
                      fulfillmentLineItemId: { type: 'string' },
                      quantity: { type: 'number' },
                      returnReason: { type: 'string' },
                      customerNote: { type: 'string' },
                    },
                  },
                },
                accessToken: {
                  type: 'string',
                  description: 'Shopify access token',
                },
                shopDomain: {
                  type: 'string',
                  description: 'Shopify domain',
                },
              },
              required: ['orderId', 'returnLineItems', 'accessToken', 'shopDomain'],
            },
          },
          {
            name: 'shopify_test_connection',
            description: 'Test Shopify API connection',
            inputSchema: {
              type: 'object',
              properties: {
                accessToken: {
                  type: 'string',
                  description: 'Shopify access token',
                },
                shopDomain: {
                  type: 'string',
                  description: 'Shopify domain',
                },
              },
              required: ['accessToken', 'shopDomain'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'shopify_find_order':
            return await this.findOrder(args);
          case 'shopify_create_return':
            return await this.createReturn(args);
          case 'shopify_test_connection':
            return await this.testConnection(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
        };
      }
    });
  }

  async findOrder({ query, accessToken, shopDomain }) {
    const graphqlQuery = `
      query findOrder($query: String!) {
        orders(first: 1, query: $query) {
          edges {
            node {
              id
              name
              email
              createdAt
              totalPriceSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              shippingAddress {
                firstName
                lastName
                address1
                address2
                city
                province
                zip
                country
                phone
              }
              lineItems(first: 50) {
                edges {
                  node {
                    id
                    name
                    title
                    quantity
                    currentQuantity
                    fulfillableQuantity
                    image {
                      altText
                      url
                    }
                    variant {
                      id
                      title
                      price
                      product {
                        id
                        title
                      }
                    }
                  }
                }
              }
              fulfillments(first: 10) {
                trackingCompany
                trackingNumbers
                location {
                  name
                }
                createdAt
              }
            }
          }
        }
      }
    `;

    const response = await fetch(`https://${shopDomain}/admin/api/2024-07/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken,
      },
      body: JSON.stringify({
        query: graphqlQuery,
        variables: { query },
      }),
    });

    const data = await response.json();
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2),
        },
      ],
    };
  }

  async createReturn({ orderId, returnLineItems, accessToken, shopDomain }) {
    const mutation = `
      mutation returnCreate($returnInput: ReturnInput!) {
        returnCreate(returnInput: $returnInput) {
          return {
            id
            name
            status
            returnLineItems {
              customerNote
              quantity
              returnReason
              returnReasonNote
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const returnInput = {
      orderId,
      returnLineItems: returnLineItems.map(item => ({
        fulfillmentLineItemId: item.fulfillmentLineItemId,
        quantity: item.quantity,
        returnReason: item.returnReason || 'DEFECTIVE',
        customerNote: item.customerNote || '',
      })),
    };

    const response = await fetch(`https://${shopDomain}/admin/api/2024-07/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken,
      },
      body: JSON.stringify({
        query: mutation,
        variables: { returnInput },
      }),
    });

    const data = await response.json();
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2),
        },
      ],
    };
  }

  async testConnection({ accessToken, shopDomain }) {
    const query = `
      query {
        shop {
          id
          name
          email
          plan {
            displayName
          }
        }
      }
    `;

    const response = await fetch(`https://${shopDomain}/admin/api/2024-07/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': accessToken,
      },
      body: JSON.stringify({ query }),
    });

    const data = await response.json();
    
    return {
      content: [
        {
          type: 'text',
          text: `Connection ${response.ok ? 'successful' : 'failed'}: ${JSON.stringify(data, null, 2)}`,
        },
      ],
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Shopify MCP server running on stdio');
  }
}

const server = new ShopifyMCPServer();
server.run().catch(console.error);

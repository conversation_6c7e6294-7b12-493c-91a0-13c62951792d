{"version": 3, "file": "graphql-client.js", "sources": ["../../../src/graphql-client/constants.ts", "../../../src/graphql-client/utilities.ts", "../../../src/graphql-client/http-fetch.ts", "../../../src/graphql-client/graphql-client.ts"], "sourcesContent": ["export const CLIENT = 'GraphQL Client';\nexport const MIN_RETRIES = 0;\nexport const MAX_RETRIES = 3;\n\nexport const GQL_API_ERROR =\n  \"An error occurred while fetching from the API. Review 'graphQLErrors' for details.\";\nexport const UNEXPECTED_CONTENT_TYPE_ERROR =\n  'Response returned unexpected Content-Type:';\nexport const NO_DATA_OR_ERRORS_ERROR =\n  'An unknown error has occurred. The API did not return a data object or any errors in its response.';\n\nexport const CONTENT_TYPES = {\n  json: 'application/json',\n  multipart: 'multipart/mixed',\n};\nexport const SDK_VARIANT_HEADER = 'X-SDK-Variant';\nexport const SDK_VERSION_HEADER = 'X-SDK-Version';\n\nexport const DEFAULT_SDK_VARIANT = 'shopify-graphql-client';\n// This is value is replaced with package.json version during rollup build process\nexport const DEFAULT_CLIENT_VERSION = 'ROLLUP_REPLACE_CLIENT_VERSION';\n\nexport const RETRY_WAIT_TIME = 1000;\nexport const RETRIABLE_STATUS_CODES = [429, 503];\nexport const DEFER_OPERATION_REGEX = /@(defer)\\b/i;\nexport const NEWLINE_SEPARATOR = '\\r\\n';\nexport const BOUNDARY_HEADER_REGEX = /boundary=\"?([^=\";]+)\"?/i;\nexport const HEADER_SEPARATOR = NEWLINE_SEPARATOR + NEWLINE_SEPARATOR;\n", "import {CLIENT, MAX_RETRIES, MIN_RETRIES} from './constants';\n\nexport function formatErrorMessage(message: string, client = CLIENT) {\n  return message.startsWith(`${client}`) ? message : `${client}: ${message}`;\n}\n\nexport function getErrorMessage(error: any) {\n  return error instanceof Error ? error.message : JSON.stringify(error);\n}\n\nexport function getErrorCause(error: any): Record<string, any> | undefined {\n  return error instanceof Error && error.cause ? error.cause : undefined;\n}\n\nexport function combineErrors(dataArray: Record<string, any>[]) {\n  return dataArray.flatMap(({errors}) => {\n    return errors ?? [];\n  });\n}\n\nexport function validateRetries({\n  client,\n  retries,\n}: {\n  client: string;\n  retries?: number;\n}) {\n  if (\n    retries !== undefined &&\n    (typeof retries !== 'number' ||\n      retries < MIN_RETRIES ||\n      retries > MAX_RETRIES)\n  ) {\n    throw new Error(\n      `${client}: The provided \"retries\" value (${retries}) is invalid - it cannot be less than ${MIN_RETRIES} or greater than ${MAX_RETRIES}`,\n    );\n  }\n}\n\nexport function getKeyValueIfValid(key: string, value?: any) {\n  return value &&\n    (typeof value !== 'object' ||\n      Array.isArray(value) ||\n      (typeof value === 'object' && Object.keys(value).length > 0))\n    ? {[key]: value}\n    : {};\n}\n\nexport function buildDataObjectByPath(\n  path: string[],\n  data: any,\n): Record<string | number, any> {\n  if (path.length === 0) {\n    return data;\n  }\n\n  const key = path.pop() as string | number;\n  const newData = {\n    [key]: data,\n  };\n\n  if (path.length === 0) {\n    return newData;\n  }\n\n  return buildDataObjectByPath(path, newData);\n}\n\nfunction combineObjects(baseObject: any, newObject: any) {\n  return Object.keys(newObject || {}).reduce(\n    (acc: any, key: string | number) => {\n      if (\n        (typeof newObject[key] === 'object' || Array.isArray(newObject[key])) &&\n        baseObject[key]\n      ) {\n        acc[key] = combineObjects(baseObject[key], newObject[key]);\n        return acc;\n      }\n\n      acc[key] = newObject[key];\n      return acc;\n    },\n    Array.isArray(baseObject) ? [...baseObject] : {...baseObject},\n  );\n}\n\nexport function buildCombinedDataObject([\n  initialDatum,\n  ...remainingData\n]: any[]) {\n  return remainingData.reduce(combineObjects, {...initialDatum});\n}\n", "import {CLIENT, RETRIABLE_STATUS_CODES, RETRY_WAIT_TIME} from './constants';\nimport {CustomFetchApi, GraphQLClient, Logger} from './types';\nimport {formatErrorMessage, getErrorMessage} from './utilities';\n\ninterface GenerateHttpFetchOptions {\n  clientLogger: Logger;\n  customFetchApi?: CustomFetchApi;\n  client?: string;\n  defaultRetryWaitTime?: number;\n  retriableCodes?: number[];\n}\n\nexport function generateHttpFetch({\n  clientLogger,\n  customFetchApi = fetch,\n  client = CLIENT,\n  defaultRetryWaitTime = RETRY_WAIT_TIME,\n  retriableCodes = RETRIABLE_STATUS_CODES,\n}: GenerateHttpFetchOptions) {\n  const httpFetch = async (\n    requestParams: Parameters<CustomFetchApi>,\n    count: number,\n    maxRetries: number,\n  ): ReturnType<GraphQLClient['fetch']> => {\n    const nextCount = count + 1;\n    const maxTries = maxRetries + 1;\n    let response: Response | undefined;\n\n    try {\n      response = await customFetchA<PERSON>(...requestParams);\n\n      clientLogger({\n        type: 'HTTP-Response',\n        content: {\n          requestParams,\n          response,\n        },\n      });\n\n      if (\n        !response.ok &&\n        retriableCodes.includes(response.status) &&\n        nextCount <= maxTries\n      ) {\n        throw new Error();\n      }\n\n      const deprecationNotice =\n        response?.headers.get('X-Shopify-API-Deprecated-Reason') || '';\n      if (deprecationNotice) {\n        clientLogger({\n          type: 'HTTP-Response-GraphQL-Deprecation-Notice',\n          content: {\n            requestParams,\n            deprecationNotice,\n          },\n        });\n      }\n\n      return response;\n    } catch (error) {\n      if (nextCount <= maxTries) {\n        const retryAfter = response?.headers.get('Retry-After');\n        await sleep(\n          retryAfter ? parseInt(retryAfter, 10) : defaultRetryWaitTime,\n        );\n\n        clientLogger({\n          type: 'HTTP-Retry',\n          content: {\n            requestParams,\n            lastResponse: response,\n            retryAttempt: count,\n            maxRetries,\n          },\n        });\n\n        return httpFetch(requestParams, nextCount, maxRetries);\n      }\n\n      throw new Error(\n        formatErrorMessage(\n          `${\n            maxRetries > 0\n              ? `Attempted maximum number of ${maxRetries} network retries. Last message - `\n              : ''\n          }${getErrorMessage(error)}`,\n          client,\n        ),\n      );\n    }\n  };\n\n  return httpFetch;\n}\n\nasync function sleep(waitTime: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, waitTime));\n}\n", "import {generateHttpFetch} from './http-fetch';\nimport {\n  ClientOptions,\n  CustomFetchApi,\n  GraphQLClient,\n  ClientResponse,\n  ClientConfig,\n  Logger,\n  LogContentTypes,\n  DataChunk,\n} from './types';\nimport {\n  CLIENT,\n  GQL_API_ERROR,\n  UNEXPECTED_CONTENT_TYPE_ERROR,\n  NO_DATA_OR_ERRORS_ERROR,\n  CONTENT_TYPES,\n  RETRY_WAIT_TIME,\n  HEADER_SEPARATOR,\n  DEFER_OPERATION_REGEX,\n  BOUNDARY_HEADER_REGEX,\n  SDK_VARIANT_HEADER,\n  SDK_VERSION_HEADER,\n  DEFAULT_SDK_VARIANT,\n  DEFAULT_CLIENT_VERSION,\n} from './constants';\nimport {\n  formatErrorMessage,\n  getErrorMessage,\n  validateRetries,\n  getKeyValueIfValid,\n  buildDataObjectByPath,\n  buildCombinedDataObject,\n  getErrorCause,\n  combineErrors,\n} from './utilities';\n\nexport function createGraphQLClient({\n  headers,\n  url,\n  customFetchApi = fetch,\n  retries = 0,\n  logger,\n}: ClientOptions): GraphQLClient {\n  validateRetries({client: CLIENT, retries});\n\n  const config: ClientConfig = {\n    headers,\n    url,\n    retries,\n  };\n\n  const clientLogger = generateClientLogger(logger);\n  const httpFetch = generateHttpFetch({\n    customFetchApi,\n    clientLogger,\n    defaultRetryWaitTime: RETRY_WAIT_TIME,\n  });\n  const fetchFn = generateFetch(httpFetch, config);\n  const request = generateRequest(fetchFn);\n  const requestStream = generateRequestStream(fetchFn);\n\n  return {\n    config,\n    fetch: fetchFn,\n    request,\n    requestStream,\n  };\n}\n\nexport function generateClientLogger(logger?: Logger): Logger {\n  return (logContent: LogContentTypes) => {\n    if (logger) {\n      logger(logContent);\n    }\n  };\n}\n\nasync function processJSONResponse<TData = any>(\n  response: Response,\n): Promise<ClientResponse<TData>> {\n  const {errors, data, extensions} = await response.json<any>();\n\n  return {\n    ...getKeyValueIfValid('data', data),\n    ...getKeyValueIfValid('extensions', extensions),\n    headers: response.headers,\n\n    ...(errors || !data\n      ? {\n          errors: {\n            networkStatusCode: response.status,\n            message: formatErrorMessage(\n              errors ? GQL_API_ERROR : NO_DATA_OR_ERRORS_ERROR,\n            ),\n            ...getKeyValueIfValid('graphQLErrors', errors),\n            response,\n          },\n        }\n      : {}),\n  };\n}\n\nfunction generateFetch(\n  httpFetch: ReturnType<typeof generateHttpFetch>,\n  {url, headers, retries}: ClientConfig,\n): GraphQLClient['fetch'] {\n  return async (operation, options = {}) => {\n    const {\n      variables,\n      headers: overrideHeaders,\n      url: overrideUrl,\n      retries: overrideRetries,\n      keepalive,\n      signal,\n    } = options;\n\n    const body = JSON.stringify({\n      query: operation,\n      variables,\n    });\n\n    validateRetries({client: CLIENT, retries: overrideRetries});\n\n    const flatHeaders = Object.entries({\n      ...headers,\n      ...overrideHeaders,\n    }).reduce((headers: Record<string, string>, [key, value]) => {\n      headers[key] = Array.isArray(value) ? value.join(', ') : value.toString();\n      return headers;\n    }, {});\n\n    if (!flatHeaders[SDK_VARIANT_HEADER] && !flatHeaders[SDK_VERSION_HEADER]) {\n      flatHeaders[SDK_VARIANT_HEADER] = DEFAULT_SDK_VARIANT;\n      flatHeaders[SDK_VERSION_HEADER] = DEFAULT_CLIENT_VERSION;\n    }\n\n    const fetchParams: Parameters<CustomFetchApi> = [\n      overrideUrl ?? url,\n      {\n        method: 'POST',\n        headers: flatHeaders,\n        body,\n        signal,\n        keepalive,\n      },\n    ];\n\n    return httpFetch(fetchParams, 1, overrideRetries ?? retries);\n  };\n}\n\nfunction generateRequest(\n  fetchFn: ReturnType<typeof generateFetch>,\n): GraphQLClient['request'] {\n  return async (...props) => {\n    if (DEFER_OPERATION_REGEX.test(props[0])) {\n      throw new Error(\n        formatErrorMessage(\n          'This operation will result in a streamable response - use requestStream() instead.',\n        ),\n      );\n    }\n\n    let response: Response | null = null;\n    try {\n      response = await fetchFn(...props);\n      const {status, statusText} = response;\n      const contentType = response.headers.get('content-type') || '';\n\n      if (!response.ok) {\n        return {\n          errors: {\n            networkStatusCode: status,\n            message: formatErrorMessage(statusText),\n            response,\n          },\n        };\n      }\n\n      if (!contentType.includes(CONTENT_TYPES.json)) {\n        return {\n          errors: {\n            networkStatusCode: status,\n            message: formatErrorMessage(\n              `${UNEXPECTED_CONTENT_TYPE_ERROR} ${contentType}`,\n            ),\n            response,\n          },\n        };\n      }\n\n      return await processJSONResponse(response);\n    } catch (error) {\n      return {\n        errors: {\n          message: getErrorMessage(error),\n          ...(response == null\n            ? {}\n            : {\n                networkStatusCode: response.status,\n                response,\n              }),\n        },\n      };\n    }\n  };\n}\n\nasync function* getStreamBodyIterator(\n  response: Response,\n): AsyncIterableIterator<string> {\n  const decoder = new TextDecoder();\n\n  // Response body is an async iterator\n  if ((response.body as any)![Symbol.asyncIterator]) {\n    for await (const chunk of response.body! as any) {\n      yield decoder.decode(chunk);\n    }\n  } else {\n    const reader = response.body!.getReader();\n\n    let readResult: ReadableStreamReadResult<DataChunk>;\n    try {\n      while (!(readResult = await reader.read()).done) {\n        yield decoder.decode(readResult.value);\n      }\n    } finally {\n      reader.cancel();\n    }\n  }\n}\n\nfunction readStreamChunk(\n  streamBodyIterator: AsyncIterableIterator<string>,\n  boundary: string,\n) {\n  return {\n    async *[Symbol.asyncIterator]() {\n      try {\n        let buffer = '';\n\n        for await (const textChunk of streamBodyIterator) {\n          buffer += textChunk;\n\n          if (buffer.indexOf(boundary) > -1) {\n            const lastBoundaryIndex = buffer.lastIndexOf(boundary);\n            const fullResponses = buffer.slice(0, lastBoundaryIndex);\n\n            const chunkBodies = fullResponses\n              .split(boundary)\n              .filter((chunk) => chunk.trim().length > 0)\n              .map((chunk) => {\n                const body = chunk\n                  .slice(\n                    chunk.indexOf(HEADER_SEPARATOR) + HEADER_SEPARATOR.length,\n                  )\n                  .trim();\n                return body;\n              });\n\n            if (chunkBodies.length > 0) {\n              yield chunkBodies;\n            }\n\n            buffer = buffer.slice(lastBoundaryIndex + boundary.length);\n\n            if (buffer.trim() === `--`) {\n              buffer = '';\n            }\n          }\n        }\n      } catch (error) {\n        throw new Error(\n          `Error occured while processing stream payload - ${getErrorMessage(\n            error,\n          )}`,\n        );\n      }\n    },\n  };\n}\n\nfunction createJsonResponseAsyncIterator(response: Response) {\n  return {\n    async *[Symbol.asyncIterator]() {\n      const processedResponse = await processJSONResponse(response);\n\n      yield {\n        ...processedResponse,\n        hasNext: false,\n      };\n    },\n  };\n}\n\nfunction getResponseDataFromChunkBodies(chunkBodies: string[]): {\n  data: any;\n  errors?: any;\n  extensions?: any;\n  hasNext: boolean;\n}[] {\n  return chunkBodies\n    .map((value) => {\n      try {\n        return JSON.parse(value);\n      } catch (error) {\n        throw new Error(\n          `Error in parsing multipart response - ${getErrorMessage(error)}`,\n        );\n      }\n    })\n    .map((payload) => {\n      const {data, incremental, hasNext, extensions, errors} = payload;\n\n      // initial data chunk\n      if (!incremental) {\n        return {\n          data: data || {},\n          ...getKeyValueIfValid('errors', errors),\n          ...getKeyValueIfValid('extensions', extensions),\n          hasNext,\n        };\n      }\n\n      // subsequent data chunks\n      const incrementalArray: {data: any; errors?: any}[] = incremental.map(\n        ({data, path, errors}: any) => {\n          return {\n            data: data && path ? buildDataObjectByPath(path, data) : {},\n            ...getKeyValueIfValid('errors', errors),\n          };\n        },\n      );\n\n      return {\n        data:\n          incrementalArray.length === 1\n            ? incrementalArray[0].data\n            : buildCombinedDataObject([\n                ...incrementalArray.map(({data}) => data),\n              ]),\n        ...getKeyValueIfValid('errors', combineErrors(incrementalArray)),\n        hasNext,\n      };\n    });\n}\n\nfunction validateResponseData(\n  responseErrors: any[],\n  combinedData: ReturnType<typeof buildCombinedDataObject>,\n) {\n  if (responseErrors.length > 0) {\n    throw new Error(GQL_API_ERROR, {\n      cause: {\n        graphQLErrors: responseErrors,\n      },\n    });\n  }\n\n  if (Object.keys(combinedData).length === 0) {\n    throw new Error(NO_DATA_OR_ERRORS_ERROR);\n  }\n}\n\nfunction createMultipartResponseAsyncInterator(\n  response: Response,\n  responseContentType: string,\n) {\n  const boundaryHeader = (responseContentType ?? '').match(\n    BOUNDARY_HEADER_REGEX,\n  );\n  const boundary = `--${boundaryHeader ? boundaryHeader[1] : '-'}`;\n\n  if (\n    !response.body?.getReader &&\n    !(response.body as any)?.[Symbol.asyncIterator]\n  ) {\n    throw new Error('API multipart response did not return an iterable body', {\n      cause: response,\n    });\n  }\n\n  const streamBodyIterator = getStreamBodyIterator(response);\n\n  let combinedData: Record<string, any> = {};\n  let responseExtensions: Record<string, any> | undefined;\n\n  return {\n    async *[Symbol.asyncIterator]() {\n      try {\n        let streamHasNext = true;\n\n        for await (const chunkBodies of readStreamChunk(\n          streamBodyIterator,\n          boundary,\n        )) {\n          const responseData = getResponseDataFromChunkBodies(chunkBodies);\n\n          responseExtensions =\n            responseData.find((datum) => datum.extensions)?.extensions ??\n            responseExtensions;\n\n          const responseErrors = combineErrors(responseData);\n\n          combinedData = buildCombinedDataObject([\n            combinedData,\n            ...responseData.map(({data}) => data),\n          ]);\n\n          streamHasNext = responseData.slice(-1)[0].hasNext;\n\n          validateResponseData(responseErrors, combinedData);\n\n          yield {\n            ...getKeyValueIfValid('data', combinedData),\n            ...getKeyValueIfValid('extensions', responseExtensions),\n            hasNext: streamHasNext,\n          };\n        }\n\n        if (streamHasNext) {\n          throw new Error(`Response stream terminated unexpectedly`);\n        }\n      } catch (error) {\n        const cause = getErrorCause(error);\n\n        yield {\n          ...getKeyValueIfValid('data', combinedData),\n          ...getKeyValueIfValid('extensions', responseExtensions),\n          errors: {\n            message: formatErrorMessage(getErrorMessage(error)),\n            networkStatusCode: response.status,\n            ...getKeyValueIfValid('graphQLErrors', cause?.graphQLErrors),\n            response,\n          },\n          hasNext: false,\n        };\n      }\n    },\n  };\n}\n\nfunction generateRequestStream(\n  fetchFn: ReturnType<typeof generateFetch>,\n): GraphQLClient['requestStream'] {\n  return async (...props) => {\n    if (!DEFER_OPERATION_REGEX.test(props[0])) {\n      throw new Error(\n        formatErrorMessage(\n          'This operation does not result in a streamable response - use request() instead.',\n        ),\n      );\n    }\n\n    try {\n      const response = await fetchFn(...props);\n\n      const {statusText} = response;\n\n      if (!response.ok) {\n        throw new Error(statusText, {cause: response});\n      }\n\n      const responseContentType = response.headers.get('content-type') || '';\n\n      switch (true) {\n        case responseContentType.includes(CONTENT_TYPES.json):\n          return createJsonResponseAsyncIterator(response);\n        case responseContentType.includes(CONTENT_TYPES.multipart):\n          return createMultipartResponseAsyncInterator(\n            response,\n            responseContentType,\n          );\n        default:\n          throw new Error(\n            `${UNEXPECTED_CONTENT_TYPE_ERROR} ${responseContentType}`,\n            {cause: response},\n          );\n      }\n    } catch (error) {\n      return {\n        async *[Symbol.asyncIterator]() {\n          const response = getErrorCause(error);\n\n          yield {\n            errors: {\n              message: formatErrorMessage(getErrorMessage(error)),\n              ...getKeyValueIfValid('networkStatusCode', response?.status),\n              ...getKeyValueIfValid('response', response),\n            },\n            hasNext: false,\n          };\n        },\n      };\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;IAAO,MAAM,MAAM,GAAG,gBAAgB;IAC/B,MAAM,WAAW,GAAG,CAAC;IACrB,MAAM,WAAW,GAAG,CAAC;IAErB,MAAM,aAAa,GACxB,oFAAoF;IAC/E,MAAM,6BAA6B,GACxC,4CAA4C;IACvC,MAAM,uBAAuB,GAClC,oGAAoG;IAE/F,MAAM,aAAa,GAAG;IAC3B,IAAA,IAAI,EAAE,kBAAkB;IACxB,IAAA,SAAS,EAAE,iBAAiB;KAC7B;IACM,MAAM,kBAAkB,GAAG,eAAe;IAC1C,MAAM,kBAAkB,GAAG,eAAe;IAE1C,MAAM,mBAAmB,GAAG,wBAAwB;IAC3D;IACO,MAAM,sBAAsB,GAAG,OAA+B;IAE9D,MAAM,eAAe,GAAG,IAAI;IAC5B,MAAM,sBAAsB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IACzC,MAAM,qBAAqB,GAAG,aAAa;IAC3C,MAAM,iBAAiB,GAAG,MAAM;IAChC,MAAM,qBAAqB,GAAG,yBAAyB;IACvD,MAAM,gBAAgB,GAAG,iBAAiB,GAAG,iBAAiB;;aCzBrD,kBAAkB,CAAC,OAAe,EAAE,MAAM,GAAG,MAAM,EAAA;QACjE,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,CAAA,CAAE,CAAC,GAAG,OAAO,GAAG,CAAA,EAAG,MAAM,CAAA,EAAA,EAAK,OAAO,EAAE;IAC5E;IAEM,SAAU,eAAe,CAAC,KAAU,EAAA;IACxC,IAAA,OAAO,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IACvE;IAEM,SAAU,aAAa,CAAC,KAAU,EAAA;IACtC,IAAA,OAAO,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,SAAS;IACxE;IAEM,SAAU,aAAa,CAAC,SAAgC,EAAA;QAC5D,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC,EAAC,MAAM,EAAC,KAAI;YACpC,OAAO,MAAM,IAAI,EAAE;IACrB,IAAA,CAAC,CAAC;IACJ;aAEgB,eAAe,CAAC,EAC9B,MAAM,EACN,OAAO,GAIR,EAAA;QACC,IACE,OAAO,KAAK,SAAS;aACpB,OAAO,OAAO,KAAK,QAAQ;IAC1B,YAAA,OAAO,GAAG,WAAW;IACrB,YAAA,OAAO,GAAG,WAAW,CAAC,EACxB;IACA,QAAA,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,MAAM,CAAA,gCAAA,EAAmC,OAAO,CAAA,sCAAA,EAAyC,WAAW,CAAA,iBAAA,EAAoB,WAAW,CAAA,CAAE,CACzI;QACH;IACF;IAEM,SAAU,kBAAkB,CAAC,GAAW,EAAE,KAAW,EAAA;IACzD,IAAA,OAAO,KAAK;aACT,OAAO,KAAK,KAAK,QAAQ;IACxB,YAAA,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;IACpB,aAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9D,UAAE,EAAC,CAAC,GAAG,GAAG,KAAK;cACb,EAAE;IACR;IAEM,SAAU,qBAAqB,CACnC,IAAc,EACd,IAAS,EAAA;IAET,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;IACrB,QAAA,OAAO,IAAI;QACb;IAEA,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAqB;IACzC,IAAA,MAAM,OAAO,GAAG;YACd,CAAC,GAAG,GAAG,IAAI;SACZ;IAED,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;IACrB,QAAA,OAAO,OAAO;QAChB;IAEA,IAAA,OAAO,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC;IAC7C;IAEA,SAAS,cAAc,CAAC,UAAe,EAAE,SAAc,EAAA;IACrD,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,MAAM,CACxC,CAAC,GAAQ,EAAE,GAAoB,KAAI;IACjC,QAAA,IACE,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpE,YAAA,UAAU,CAAC,GAAG,CAAC,EACf;IACA,YAAA,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;IAC1D,YAAA,OAAO,GAAG;YACZ;YAEA,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC;IACzB,QAAA,OAAO,GAAG;QACZ,CAAC,EACD,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,EAAC,GAAG,UAAU,EAAC,CAC9D;IACH;IAEM,SAAU,uBAAuB,CAAC,CACtC,YAAY,EACZ,GAAG,aAAa,CACV,EAAA;QACN,OAAO,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,EAAC,GAAG,YAAY,EAAC,CAAC;IAChE;;IC/EM,SAAU,iBAAiB,CAAC,EAChC,YAAY,EACZ,cAAc,GAAG,KAAK,EACtB,MAAM,GAAG,MAAM,EACf,oBAAoB,GAAG,eAAe,EACtC,cAAc,GAAG,sBAAsB,GACd,EAAA;QACzB,MAAM,SAAS,GAAG,OAChB,aAAyC,EACzC,KAAa,EACb,UAAkB,KACoB;IACtC,QAAA,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC;IAC3B,QAAA,MAAM,QAAQ,GAAG,UAAU,GAAG,CAAC;IAC/B,QAAA,IAAI,QAA8B;IAElC,QAAA,IAAI;IACF,YAAA,QAAQ,GAAG,MAAM,cAAc,CAAC,GAAG,aAAa,CAAC;IAEjD,YAAA,YAAY,CAAC;IACX,gBAAA,IAAI,EAAE,eAAe;IACrB,gBAAA,OAAO,EAAE;wBACP,aAAa;wBACb,QAAQ;IACT,iBAAA;IACF,aAAA,CAAC;gBAEF,IACE,CAAC,QAAQ,CAAC,EAAE;IACZ,gBAAA,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACxC,SAAS,IAAI,QAAQ,EACrB;oBACA,MAAM,IAAI,KAAK,EAAE;gBACnB;IAEA,YAAA,MAAM,iBAAiB,GACrB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,IAAI,EAAE;gBAChE,IAAI,iBAAiB,EAAE;IACrB,gBAAA,YAAY,CAAC;IACX,oBAAA,IAAI,EAAE,0CAA0C;IAChD,oBAAA,OAAO,EAAE;4BACP,aAAa;4BACb,iBAAiB;IAClB,qBAAA;IACF,iBAAA,CAAC;gBACJ;IAEA,YAAA,OAAO,QAAQ;YACjB;YAAE,OAAO,KAAK,EAAE;IACd,YAAA,IAAI,SAAS,IAAI,QAAQ,EAAE;oBACzB,MAAM,UAAU,GAAG,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;IACvD,gBAAA,MAAM,KAAK,CACT,UAAU,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,oBAAoB,CAC7D;IAED,gBAAA,YAAY,CAAC;IACX,oBAAA,IAAI,EAAE,YAAY;IAClB,oBAAA,OAAO,EAAE;4BACP,aAAa;IACb,wBAAA,YAAY,EAAE,QAAQ;IACtB,wBAAA,YAAY,EAAE,KAAK;4BACnB,UAAU;IACX,qBAAA;IACF,iBAAA,CAAC;oBAEF,OAAO,SAAS,CAAC,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC;gBACxD;gBAEA,MAAM,IAAI,KAAK,CACb,kBAAkB,CAChB,CAAA,EACE,UAAU,GAAG;kBACT,CAAA,4BAAA,EAA+B,UAAU,CAAA,iCAAA;AAC3C,kBAAE,EACN,CAAA,EAAG,eAAe,CAAC,KAAK,CAAC,CAAA,CAAE,EAC3B,MAAM,CACP,CACF;YACH;IACF,IAAA,CAAC;IAED,IAAA,OAAO,SAAS;IAClB;IAEA,eAAe,KAAK,CAAC,QAAgB,EAAA;IACnC,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE;;aC7DgB,mBAAmB,CAAC,EAClC,OAAO,EACP,GAAG,EACH,cAAc,GAAG,KAAK,EACtB,OAAO,GAAG,CAAC,EACX,MAAM,GACQ,EAAA;QACd,eAAe,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAC,CAAC;IAE1C,IAAA,MAAM,MAAM,GAAiB;YAC3B,OAAO;YACP,GAAG;YACH,OAAO;SACR;IAED,IAAA,MAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,CAAC;QACjD,MAAM,SAAS,GAAG,iBAAiB,CAAC;YAClC,cAAc;YACd,YAAY;IACZ,QAAA,oBAAoB,EAAE,eAAe;IACtC,KAAA,CAAC;QACF,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC;IAChD,IAAA,MAAM,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;IACxC,IAAA,MAAM,aAAa,GAAG,qBAAqB,CAAC,OAAO,CAAC;QAEpD,OAAO;YACL,MAAM;IACN,QAAA,KAAK,EAAE,OAAO;YACd,OAAO;YACP,aAAa;SACd;IACH;IAEM,SAAU,oBAAoB,CAAC,MAAe,EAAA;QAClD,OAAO,CAAC,UAA2B,KAAI;YACrC,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,UAAU,CAAC;YACpB;IACF,IAAA,CAAC;IACH;IAEA,eAAe,mBAAmB,CAChC,QAAkB,EAAA;IAElB,IAAA,MAAM,EAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAC,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAO;QAE7D,OAAO;IACL,QAAA,GAAG,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;IACnC,QAAA,GAAG,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC;YAC/C,OAAO,EAAE,QAAQ,CAAC,OAAO;IAEzB,QAAA,IAAI,MAAM,IAAI,CAAC;IACb,cAAE;IACE,gBAAA,MAAM,EAAE;wBACN,iBAAiB,EAAE,QAAQ,CAAC,MAAM;IAClC,oBAAA,OAAO,EAAE,kBAAkB,CACzB,MAAM,GAAG,aAAa,GAAG,uBAAuB,CACjD;IACD,oBAAA,GAAG,kBAAkB,CAAC,eAAe,EAAE,MAAM,CAAC;wBAC9C,QAAQ;IACT,iBAAA;IACF;kBACD,EAAE,CAAC;SACR;IACH;IAEA,SAAS,aAAa,CACpB,SAA+C,EAC/C,EAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAe,EAAA;QAErC,OAAO,OAAO,SAAS,EAAE,OAAO,GAAG,EAAE,KAAI;YACvC,MAAM,EACJ,SAAS,EACT,OAAO,EAAE,eAAe,EACxB,GAAG,EAAE,WAAW,EAChB,OAAO,EAAE,eAAe,EACxB,SAAS,EACT,MAAM,GACP,GAAG,OAAO;IAEX,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;IAC1B,YAAA,KAAK,EAAE,SAAS;gBAChB,SAAS;IACV,SAAA,CAAC;YAEF,eAAe,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAC,CAAC;IAE3D,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;IACjC,YAAA,GAAG,OAAO;IACV,YAAA,GAAG,eAAe;IACnB,SAAA,CAAC,CAAC,MAAM,CAAC,CAAC,OAA+B,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;gBAC1D,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE;IACzE,YAAA,OAAO,OAAO;YAChB,CAAC,EAAE,EAAE,CAAC;IAEN,QAAA,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE;IACxE,YAAA,WAAW,CAAC,kBAAkB,CAAC,GAAG,mBAAmB;IACrD,YAAA,WAAW,CAAC,kBAAkB,CAAC,GAAG,sBAAsB;YAC1D;IAEA,QAAA,MAAM,WAAW,GAA+B;IAC9C,YAAA,WAAW,IAAI,GAAG;IAClB,YAAA;IACE,gBAAA,MAAM,EAAE,MAAM;IACd,gBAAA,OAAO,EAAE,WAAW;oBACpB,IAAI;oBACJ,MAAM;oBACN,SAAS;IACV,aAAA;aACF;YAED,OAAO,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE,eAAe,IAAI,OAAO,CAAC;IAC9D,IAAA,CAAC;IACH;IAEA,SAAS,eAAe,CACtB,OAAyC,EAAA;IAEzC,IAAA,OAAO,OAAO,GAAG,KAAK,KAAI;YACxB,IAAI,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxC,MAAM,IAAI,KAAK,CACb,kBAAkB,CAChB,oFAAoF,CACrF,CACF;YACH;YAEA,IAAI,QAAQ,GAAoB,IAAI;IACpC,QAAA,IAAI;IACF,YAAA,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,KAAK,CAAC;IAClC,YAAA,MAAM,EAAC,MAAM,EAAE,UAAU,EAAC,GAAG,QAAQ;IACrC,YAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;IAE9D,YAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;oBAChB,OAAO;IACL,oBAAA,MAAM,EAAE;IACN,wBAAA,iBAAiB,EAAE,MAAM;IACzB,wBAAA,OAAO,EAAE,kBAAkB,CAAC,UAAU,CAAC;4BACvC,QAAQ;IACT,qBAAA;qBACF;gBACH;gBAEA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;oBAC7C,OAAO;IACL,oBAAA,MAAM,EAAE;IACN,wBAAA,iBAAiB,EAAE,MAAM;4BACzB,OAAO,EAAE,kBAAkB,CACzB,CAAA,EAAG,6BAA6B,CAAA,CAAA,EAAI,WAAW,EAAE,CAClD;4BACD,QAAQ;IACT,qBAAA;qBACF;gBACH;IAEA,YAAA,OAAO,MAAM,mBAAmB,CAAC,QAAQ,CAAC;YAC5C;YAAE,OAAO,KAAK,EAAE;gBACd,OAAO;IACL,gBAAA,MAAM,EAAE;IACN,oBAAA,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC;wBAC/B,IAAI,QAAQ,IAAI;IACd,0BAAE;IACF,0BAAE;gCACE,iBAAiB,EAAE,QAAQ,CAAC,MAAM;gCAClC,QAAQ;6BACT,CAAC;IACP,iBAAA;iBACF;YACH;IACF,IAAA,CAAC;IACH;IAEA,gBAAgB,qBAAqB,CACnC,QAAkB,EAAA;IAElB,IAAA,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE;;QAGjC,IAAK,QAAQ,CAAC,IAAa,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;YACjD,WAAW,MAAM,KAAK,IAAI,QAAQ,CAAC,IAAY,EAAE;IAC/C,YAAA,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7B;QACF;aAAO;YACL,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAK,CAAC,SAAS,EAAE;IAEzC,QAAA,IAAI,UAA+C;IACnD,QAAA,IAAI;IACF,YAAA,OAAO,CAAC,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;oBAC/C,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBACxC;YACF;oBAAU;gBACR,MAAM,CAAC,MAAM,EAAE;YACjB;QACF;IACF;IAEA,SAAS,eAAe,CACtB,kBAAiD,EACjD,QAAgB,EAAA;QAEhB,OAAO;IACL,QAAA,QAAQ,MAAM,CAAC,aAAa,CAAC,GAAA;IAC3B,YAAA,IAAI;oBACF,IAAI,MAAM,GAAG,EAAE;IAEf,gBAAA,WAAW,MAAM,SAAS,IAAI,kBAAkB,EAAE;wBAChD,MAAM,IAAI,SAAS;wBAEnB,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;4BACjC,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;4BACtD,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC;4BAExD,MAAM,WAAW,GAAG;iCACjB,KAAK,CAAC,QAAQ;IACd,6BAAA,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC;IACzC,6BAAA,GAAG,CAAC,CAAC,KAAK,KAAI;gCACb,MAAM,IAAI,GAAG;qCACV,KAAK,CACJ,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,MAAM;IAE1D,iCAAA,IAAI,EAAE;IACT,4BAAA,OAAO,IAAI;IACb,wBAAA,CAAC,CAAC;IAEJ,wBAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;IAC1B,4BAAA,MAAM,WAAW;4BACnB;4BAEA,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC;IAE1D,wBAAA,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAA,EAAA,CAAI,EAAE;gCAC1B,MAAM,GAAG,EAAE;4BACb;wBACF;oBACF;gBACF;gBAAE,OAAO,KAAK,EAAE;oBACd,MAAM,IAAI,KAAK,CACb,CAAA,gDAAA,EAAmD,eAAe,CAChE,KAAK,CACN,CAAA,CAAE,CACJ;gBACH;YACF,CAAC;SACF;IACH;IAEA,SAAS,+BAA+B,CAAC,QAAkB,EAAA;QACzD,OAAO;IACL,QAAA,QAAQ,MAAM,CAAC,aAAa,CAAC,GAAA;IAC3B,YAAA,MAAM,iBAAiB,GAAG,MAAM,mBAAmB,CAAC,QAAQ,CAAC;gBAE7D,MAAM;IACJ,gBAAA,GAAG,iBAAiB;IACpB,gBAAA,OAAO,EAAE,KAAK;iBACf;YACH,CAAC;SACF;IACH;IAEA,SAAS,8BAA8B,CAAC,WAAqB,EAAA;IAM3D,IAAA,OAAO;IACJ,SAAA,GAAG,CAAC,CAAC,KAAK,KAAI;IACb,QAAA,IAAI;IACF,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YAC1B;YAAE,OAAO,KAAK,EAAE;gBACd,MAAM,IAAI,KAAK,CACb,CAAA,sCAAA,EAAyC,eAAe,CAAC,KAAK,CAAC,CAAA,CAAE,CAClE;YACH;IACF,IAAA,CAAC;IACA,SAAA,GAAG,CAAC,CAAC,OAAO,KAAI;IACf,QAAA,MAAM,EAAC,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAC,GAAG,OAAO;;YAGhE,IAAI,CAAC,WAAW,EAAE;gBAChB,OAAO;oBACL,IAAI,EAAE,IAAI,IAAI,EAAE;IAChB,gBAAA,GAAG,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC;IACvC,gBAAA,GAAG,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC;oBAC/C,OAAO;iBACR;YACH;;IAGA,QAAA,MAAM,gBAAgB,GAAgC,WAAW,CAAC,GAAG,CACnE,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAM,KAAI;gBAC5B,OAAO;IACL,gBAAA,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE;IAC3D,gBAAA,GAAG,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC;iBACxC;IACH,QAAA,CAAC,CACF;YAED,OAAO;IACL,YAAA,IAAI,EACF,gBAAgB,CAAC,MAAM,KAAK;IAC1B,kBAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;sBACpB,uBAAuB,CAAC;IACtB,oBAAA,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAC,KAAK,IAAI,CAAC;qBAC1C,CAAC;gBACR,GAAG,kBAAkB,CAAC,QAAQ,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC;gBAChE,OAAO;aACR;IACH,IAAA,CAAC,CAAC;IACN;IAEA,SAAS,oBAAoB,CAC3B,cAAqB,EACrB,YAAwD,EAAA;IAExD,IAAA,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;IAC7B,QAAA,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE;IAC7B,YAAA,KAAK,EAAE;IACL,gBAAA,aAAa,EAAE,cAAc;IAC9B,aAAA;IACF,SAAA,CAAC;QACJ;QAEA,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;IAC1C,QAAA,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC;QAC1C;IACF;IAEA,SAAS,qCAAqC,CAC5C,QAAkB,EAClB,mBAA2B,EAAA;IAE3B,IAAA,MAAM,cAAc,GAAG,CAAC,mBAAmB,IAAI,EAAE,EAAE,KAAK,CACtD,qBAAqB,CACtB;IACD,IAAA,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;IAEhE,IAAA,IACE,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS;YACzB,CAAE,QAAQ,CAAC,IAAY,GAAG,MAAM,CAAC,aAAa,CAAC,EAC/C;IACA,QAAA,MAAM,IAAI,KAAK,CAAC,wDAAwD,EAAE;IACxE,YAAA,KAAK,EAAE,QAAQ;IAChB,SAAA,CAAC;QACJ;IAEA,IAAA,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,QAAQ,CAAC;QAE1D,IAAI,YAAY,GAAwB,EAAE;IAC1C,IAAA,IAAI,kBAAmD;QAEvD,OAAO;IACL,QAAA,QAAQ,MAAM,CAAC,aAAa,CAAC,GAAA;IAC3B,YAAA,IAAI;oBACF,IAAI,aAAa,GAAG,IAAI;IAExB,gBAAA,WAAW,MAAM,WAAW,IAAI,eAAe,CAC7C,kBAAkB,EAClB,QAAQ,CACT,EAAE;IACD,oBAAA,MAAM,YAAY,GAAG,8BAA8B,CAAC,WAAW,CAAC;wBAEhE,kBAAkB;IAChB,wBAAA,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU,CAAC,EAAE,UAAU;IAC1D,4BAAA,kBAAkB;IAEpB,oBAAA,MAAM,cAAc,GAAG,aAAa,CAAC,YAAY,CAAC;wBAElD,YAAY,GAAG,uBAAuB,CAAC;4BACrC,YAAY;IACZ,wBAAA,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAC,KAAK,IAAI,CAAC;IACtC,qBAAA,CAAC;IAEF,oBAAA,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;IAEjD,oBAAA,oBAAoB,CAAC,cAAc,EAAE,YAAY,CAAC;wBAElD,MAAM;IACJ,wBAAA,GAAG,kBAAkB,CAAC,MAAM,EAAE,YAAY,CAAC;IAC3C,wBAAA,GAAG,kBAAkB,CAAC,YAAY,EAAE,kBAAkB,CAAC;IACvD,wBAAA,OAAO,EAAE,aAAa;yBACvB;oBACH;oBAEA,IAAI,aAAa,EAAE;IACjB,oBAAA,MAAM,IAAI,KAAK,CAAC,CAAA,uCAAA,CAAyC,CAAC;oBAC5D;gBACF;gBAAE,OAAO,KAAK,EAAE;IACd,gBAAA,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;oBAElC,MAAM;IACJ,oBAAA,GAAG,kBAAkB,CAAC,MAAM,EAAE,YAAY,CAAC;IAC3C,oBAAA,GAAG,kBAAkB,CAAC,YAAY,EAAE,kBAAkB,CAAC;IACvD,oBAAA,MAAM,EAAE;IACN,wBAAA,OAAO,EAAE,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;4BACnD,iBAAiB,EAAE,QAAQ,CAAC,MAAM;IAClC,wBAAA,GAAG,kBAAkB,CAAC,eAAe,EAAE,KAAK,EAAE,aAAa,CAAC;4BAC5D,QAAQ;IACT,qBAAA;IACD,oBAAA,OAAO,EAAE,KAAK;qBACf;gBACH;YACF,CAAC;SACF;IACH;IAEA,SAAS,qBAAqB,CAC5B,OAAyC,EAAA;IAEzC,IAAA,OAAO,OAAO,GAAG,KAAK,KAAI;YACxB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzC,MAAM,IAAI,KAAK,CACb,kBAAkB,CAChB,kFAAkF,CACnF,CACF;YACH;IAEA,QAAA,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,KAAK,CAAC;IAExC,YAAA,MAAM,EAAC,UAAU,EAAC,GAAG,QAAQ;IAE7B,YAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;oBAChB,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE,EAAC,KAAK,EAAE,QAAQ,EAAC,CAAC;gBAChD;IAEA,YAAA,MAAM,mBAAmB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;gBAEtE,QAAQ,IAAI;IACV,gBAAA,KAAK,mBAAmB,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;IACnD,oBAAA,OAAO,+BAA+B,CAAC,QAAQ,CAAC;IAClD,gBAAA,KAAK,mBAAmB,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC;IACxD,oBAAA,OAAO,qCAAqC,CAC1C,QAAQ,EACR,mBAAmB,CACpB;IACH,gBAAA;IACE,oBAAA,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,6BAA6B,CAAA,CAAA,EAAI,mBAAmB,CAAA,CAAE,EACzD,EAAC,KAAK,EAAE,QAAQ,EAAC,CAClB;;YAEP;YAAE,OAAO,KAAK,EAAE;gBACd,OAAO;IACL,gBAAA,QAAQ,MAAM,CAAC,aAAa,CAAC,GAAA;IAC3B,oBAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC;wBAErC,MAAM;IACJ,wBAAA,MAAM,EAAE;IACN,4BAAA,OAAO,EAAE,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACnD,4BAAA,GAAG,kBAAkB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC5D,4BAAA,GAAG,kBAAkB,CAAC,UAAU,EAAE,QAAQ,CAAC;IAC5C,yBAAA;IACD,wBAAA,OAAO,EAAE,KAAK;yBACf;oBACH,CAAC;iBACF;YACH;IACF,IAAA,CAAC;IACH;;;;;;;;"}
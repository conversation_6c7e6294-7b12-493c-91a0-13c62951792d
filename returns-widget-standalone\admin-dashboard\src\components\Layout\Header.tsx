import { useAuth } from '../../hooks/useAuth'
import { LogOut, User } from 'lucide-react'
import styles from './Header.module.css'

export const Header = () => {
  const { user, logout } = useAuth()

  return (
    <header className={styles.header}>
      <div className={styles.title}>
        <h1>Returns Widget Dashboard</h1>
      </div>
      
      <div className={styles.userSection}>
        <div className={styles.userInfo}>
          <User size={20} />
          <span>{user?.email}</span>
          <small>{user?.storeName}</small>
        </div>
        
        <button 
          onClick={logout}
          className={styles.logoutButton}
          title="Sign out"
        >
          <LogOut size={20} />
        </button>
      </div>
    </header>
  )
}

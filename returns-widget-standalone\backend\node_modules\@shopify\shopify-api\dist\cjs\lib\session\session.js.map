{"version": 3, "file": "session.js", "sources": ["../../../../../../lib/session/session.ts"], "sourcesContent": ["/* eslint-disable no-fallthrough */\nimport {InvalidSession} from '../error';\nimport {OnlineAccessInfo} from '../auth/oauth/types';\nimport {AuthScopes} from '../auth/scopes';\n\nimport {SessionParams} from './types';\n\nconst propertiesToSave = [\n  'id',\n  'shop',\n  'state',\n  'isOnline',\n  'scope',\n  'accessToken',\n  'expires',\n  'onlineAccessInfo',\n];\n\n/**\n * Stores App information from logged in merchants so they can make authenticated requests to the Admin API.\n */\nexport class Session {\n  public static fromPropertyArray(\n    entries: [string, string | number | boolean][],\n    returnUserData = false,\n  ): Session {\n    if (!Array.isArray(entries)) {\n      throw new InvalidSession(\n        'The parameter is not an array: a Session cannot be created from this object.',\n      );\n    }\n\n    const obj = Object.fromEntries(\n      entries\n        .filter(([_key, value]) => value !== null && value !== undefined)\n        // Sanitize keys\n        .map(([key, value]) => {\n          switch (key.toLowerCase()) {\n            case 'isonline':\n              return ['isOnline', value];\n            case 'accesstoken':\n              return ['accessToken', value];\n            case 'onlineaccessinfo':\n              return ['onlineAccessInfo', value];\n            case 'userid':\n              return ['userId', value];\n            case 'firstname':\n              return ['firstName', value];\n            case 'lastname':\n              return ['lastName', value];\n            case 'accountowner':\n              return ['accountOwner', value];\n            case 'emailverified':\n              return ['emailVerified', value];\n            default:\n              return [key.toLowerCase(), value];\n          }\n        }),\n    );\n\n    const sessionData = {} as SessionParams;\n    const onlineAccessInfo = {\n      associated_user: {},\n    } as OnlineAccessInfo;\n    Object.entries(obj).forEach(([key, value]) => {\n      switch (key) {\n        case 'isOnline':\n          if (typeof value === 'string') {\n            sessionData[key] = value.toString().toLowerCase() === 'true';\n          } else if (typeof value === 'number') {\n            sessionData[key] = Boolean(value);\n          } else {\n            sessionData[key] = value;\n          }\n          break;\n        case 'scope':\n          sessionData[key] = value.toString();\n          break;\n        case 'expires':\n          sessionData[key] = value ? new Date(Number(value)) : undefined;\n          break;\n        case 'onlineAccessInfo':\n          onlineAccessInfo.associated_user.id = Number(value);\n          break;\n        case 'userId':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.id = Number(value);\n            break;\n          }\n        case 'firstName':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.first_name = String(value);\n            break;\n          }\n        case 'lastName':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.last_name = String(value);\n            break;\n          }\n        case 'email':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.email = String(value);\n            break;\n          }\n        case 'accountOwner':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.account_owner = Boolean(value);\n            break;\n          }\n        case 'locale':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.locale = String(value);\n            break;\n          }\n        case 'collaborator':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.collaborator = Boolean(value);\n            break;\n          }\n        case 'emailVerified':\n          if (returnUserData) {\n            onlineAccessInfo.associated_user.email_verified = Boolean(value);\n            break;\n          }\n        // Return any user keys as passed in\n        default:\n          sessionData[key] = value;\n      }\n    });\n    if (sessionData.isOnline) {\n      sessionData.onlineAccessInfo = onlineAccessInfo;\n    }\n    const session = new Session(sessionData);\n    return session;\n  }\n\n  /**\n   * The unique identifier for the session.\n   */\n  readonly id: string;\n  /**\n   * The Shopify shop domain, such as `example.myshopify.com`.\n   */\n  public shop: string;\n  /**\n   * The state of the session. Used for the OAuth authentication code flow.\n   */\n  public state: string;\n  /**\n   * Whether the access token in the session is online or offline.\n   */\n  public isOnline: boolean;\n  /**\n   * The desired scopes for the access token, at the time the session was created.\n   */\n  public scope?: string;\n  /**\n   * The date the access token expires.\n   */\n  public expires?: Date;\n  /**\n   * The access token for the session.\n   */\n  public accessToken?: string;\n  /**\n   * Information on the user for the session. Only present for online sessions.\n   */\n  public onlineAccessInfo?: OnlineAccessInfo;\n\n  constructor(params: SessionParams) {\n    Object.assign(this, params);\n  }\n\n  /**\n   * Whether the session is active. Active sessions have an access token that is not expired, and has has the given\n   * scopes if scopes is equal to a truthy value.\n   */\n  public isActive(\n    scopes: AuthScopes | string | string[] | undefined,\n    withinMillisecondsOfExpiry = 500,\n  ): boolean {\n    const hasAccessToken = Boolean(this.accessToken);\n    const isTokenNotExpired = !this.isExpired(withinMillisecondsOfExpiry);\n    const isScopeChanged = this.isScopeChanged(scopes);\n    return !isScopeChanged && hasAccessToken && isTokenNotExpired;\n  }\n\n  /**\n   * Whether the access token includes the given scopes if they are provided.\n   */\n  public isScopeChanged(\n    scopes: AuthScopes | string | string[] | undefined,\n  ): boolean {\n    if (typeof scopes === 'undefined') {\n      return false;\n    }\n\n    return !this.isScopeIncluded(scopes);\n  }\n\n  /**\n   * Whether the access token includes the given scopes.\n   */\n  public isScopeIncluded(scopes: AuthScopes | string | string[]): boolean {\n    const requiredScopes =\n      scopes instanceof AuthScopes ? scopes : new AuthScopes(scopes);\n    const sessionScopes = new AuthScopes(this.scope);\n\n    return sessionScopes.has(requiredScopes);\n  }\n\n  /**\n   * Whether the access token is expired.\n   */\n  public isExpired(withinMillisecondsOfExpiry = 0): boolean {\n    return Boolean(\n      this.expires &&\n        this.expires.getTime() - withinMillisecondsOfExpiry < Date.now(),\n    );\n  }\n\n  /**\n   * Converts an object with data into a Session.\n   */\n  public toObject(): SessionParams {\n    const object: SessionParams = {\n      id: this.id,\n      shop: this.shop,\n      state: this.state,\n      isOnline: this.isOnline,\n    };\n\n    if (this.scope) {\n      object.scope = this.scope;\n    }\n    if (this.expires) {\n      object.expires = this.expires;\n    }\n    if (this.accessToken) {\n      object.accessToken = this.accessToken;\n    }\n    if (this.onlineAccessInfo) {\n      object.onlineAccessInfo = this.onlineAccessInfo;\n    }\n    return object;\n  }\n\n  /**\n   * Checks whether the given session is equal to this session.\n   */\n  public equals(other: Session | undefined): boolean {\n    if (!other) return false;\n\n    const mandatoryPropsMatch =\n      this.id === other.id &&\n      this.shop === other.shop &&\n      this.state === other.state &&\n      this.isOnline === other.isOnline;\n\n    if (!mandatoryPropsMatch) return false;\n\n    const copyA = this.toPropertyArray(true);\n    copyA.sort(([k1], [k2]) => (k1 < k2 ? -1 : 1));\n\n    const copyB = other.toPropertyArray(true);\n    copyB.sort(([k1], [k2]) => (k1 < k2 ? -1 : 1));\n\n    return JSON.stringify(copyA) === JSON.stringify(copyB);\n  }\n\n  /**\n   * Converts the session into an array of key-value pairs.\n   */\n  public toPropertyArray(\n    returnUserData = false,\n  ): [string, string | number | boolean][] {\n    return (\n      Object.entries(this)\n        .filter(\n          ([key, value]) =>\n            propertiesToSave.includes(key) &&\n            value !== undefined &&\n            value !== null,\n        )\n        // Prepare values for db storage\n        .flatMap(([key, value]): [string, string | number | boolean][] => {\n          switch (key) {\n            case 'expires':\n              return [[key, value ? value.getTime() : undefined]];\n            case 'onlineAccessInfo':\n              // eslint-disable-next-line no-negated-condition\n              if (!returnUserData) {\n                return [[key, value.associated_user.id]];\n              } else {\n                return [\n                  ['userId', value?.associated_user?.id],\n                  ['firstName', value?.associated_user?.first_name],\n                  ['lastName', value?.associated_user?.last_name],\n                  ['email', value?.associated_user?.email],\n                  ['locale', value?.associated_user?.locale],\n                  ['emailVerified', value?.associated_user?.email_verified],\n                  ['accountOwner', value?.associated_user?.account_owner],\n                  ['collaborator', value?.associated_user?.collaborator],\n                ];\n              }\n            default:\n              return [[key, value]];\n          }\n        })\n        // Filter out tuples with undefined values\n        .filter(([_key, value]) => value !== undefined)\n    );\n  }\n}\n"], "names": ["InvalidSession", "AuthScopes"], "mappings": ";;;;;AAAA;AAOA,MAAM,gBAAgB,GAAG;IACvB,IAAI;IACJ,MAAM;IACN,OAAO;IACP,UAAU;IACV,OAAO;IACP,aAAa;IACb,SAAS;IACT,kBAAkB;CACnB;AAED;;AAEG;MACU,OAAO,CAAA;AACX,IAAA,OAAO,iBAAiB,CAC7B,OAA8C,EAC9C,cAAc,GAAG,KAAK,EAAA;QAEtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC3B,YAAA,MAAM,IAAIA,oBAAc,CACtB,8EAA8E,CAC/E;QACH;AAEA,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAC5B;AACG,aAAA,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;;aAE/D,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;AACpB,YAAA,QAAQ,GAAG,CAAC,WAAW,EAAE;AACvB,gBAAA,KAAK,UAAU;AACb,oBAAA,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC;AAC5B,gBAAA,KAAK,aAAa;AAChB,oBAAA,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC;AAC/B,gBAAA,KAAK,kBAAkB;AACrB,oBAAA,OAAO,CAAC,kBAAkB,EAAE,KAAK,CAAC;AACpC,gBAAA,KAAK,QAAQ;AACX,oBAAA,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC1B,gBAAA,KAAK,WAAW;AACd,oBAAA,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC;AAC7B,gBAAA,KAAK,UAAU;AACb,oBAAA,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC;AAC5B,gBAAA,KAAK,cAAc;AACjB,oBAAA,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;AAChC,gBAAA,KAAK,eAAe;AAClB,oBAAA,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC;AACjC,gBAAA;oBACE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC;;QAEvC,CAAC,CAAC,CACL;QAED,MAAM,WAAW,GAAG,EAAmB;AACvC,QAAA,MAAM,gBAAgB,GAAG;AACvB,YAAA,eAAe,EAAE,EAAE;SACA;AACrB,QAAA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;YAC3C,QAAQ,GAAG;AACT,gBAAA,KAAK,UAAU;AACb,oBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,wBAAA,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,KAAK,MAAM;oBAC9D;AAAO,yBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;wBACpC,WAAW,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;oBACnC;yBAAO;AACL,wBAAA,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK;oBAC1B;oBACA;AACF,gBAAA,KAAK,OAAO;oBACV,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE;oBACnC;AACF,gBAAA,KAAK,SAAS;oBACZ,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS;oBAC9D;AACF,gBAAA,KAAK,kBAAkB;oBACrB,gBAAgB,CAAC,eAAe,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;oBACnD;AACF,gBAAA,KAAK,QAAQ;oBACX,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;wBACnD;oBACF;AACF,gBAAA,KAAK,WAAW;oBACd,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC;wBAC3D;oBACF;AACF,gBAAA,KAAK,UAAU;oBACb,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;wBAC1D;oBACF;AACF,gBAAA,KAAK,OAAO;oBACV,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;wBACtD;oBACF;AACF,gBAAA,KAAK,cAAc;oBACjB,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;wBAC/D;oBACF;AACF,gBAAA,KAAK,QAAQ;oBACX,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;wBACvD;oBACF;AACF,gBAAA,KAAK,cAAc;oBACjB,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;wBAC9D;oBACF;AACF,gBAAA,KAAK,eAAe;oBAClB,IAAI,cAAc,EAAE;wBAClB,gBAAgB,CAAC,eAAe,CAAC,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC;wBAChE;oBACF;;AAEF,gBAAA;AACE,oBAAA,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK;;AAE9B,QAAA,CAAC,CAAC;AACF,QAAA,IAAI,WAAW,CAAC,QAAQ,EAAE;AACxB,YAAA,WAAW,CAAC,gBAAgB,GAAG,gBAAgB;QACjD;AACA,QAAA,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC;AACxC,QAAA,OAAO,OAAO;IAChB;AAEA;;AAEG;AACM,IAAA,EAAE;AACX;;AAEG;AACI,IAAA,IAAI;AACX;;AAEG;AACI,IAAA,KAAK;AACZ;;AAEG;AACI,IAAA,QAAQ;AACf;;AAEG;AACI,IAAA,KAAK;AACZ;;AAEG;AACI,IAAA,OAAO;AACd;;AAEG;AACI,IAAA,WAAW;AAClB;;AAEG;AACI,IAAA,gBAAgB;AAEvB,IAAA,WAAA,CAAY,MAAqB,EAAA;AAC/B,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC;IAC7B;AAEA;;;AAGG;AACI,IAAA,QAAQ,CACb,MAAkD,EAClD,0BAA0B,GAAG,GAAG,EAAA;QAEhC,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;QAChD,MAAM,iBAAiB,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC;QACrE,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;AAClD,QAAA,OAAO,CAAC,cAAc,IAAI,cAAc,IAAI,iBAAiB;IAC/D;AAEA;;AAEG;AACI,IAAA,cAAc,CACnB,MAAkD,EAAA;AAElD,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,YAAA,OAAO,KAAK;QACd;AAEA,QAAA,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;IACtC;AAEA;;AAEG;AACI,IAAA,eAAe,CAAC,MAAsC,EAAA;AAC3D,QAAA,MAAM,cAAc,GAClB,MAAM,YAAYC,gBAAU,GAAG,MAAM,GAAG,IAAIA,gBAAU,CAAC,MAAM,CAAC;QAChE,MAAM,aAAa,GAAG,IAAIA,gBAAU,CAAC,IAAI,CAAC,KAAK,CAAC;AAEhD,QAAA,OAAO,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC;IAC1C;AAEA;;AAEG;IACI,SAAS,CAAC,0BAA0B,GAAG,CAAC,EAAA;AAC7C,QAAA,OAAO,OAAO,CACZ,IAAI,CAAC,OAAO;AACV,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,0BAA0B,GAAG,IAAI,CAAC,GAAG,EAAE,CACnE;IACH;AAEA;;AAEG;IACI,QAAQ,GAAA;AACb,QAAA,MAAM,MAAM,GAAkB;YAC5B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB;AAED,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;QAC3B;AACA,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;QAC/B;AACA,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;QACvC;AACA,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;QACjD;AACA,QAAA,OAAO,MAAM;IACf;AAEA;;AAEG;AACI,IAAA,MAAM,CAAC,KAA0B,EAAA;AACtC,QAAA,IAAI,CAAC,KAAK;AAAE,YAAA,OAAO,KAAK;QAExB,MAAM,mBAAmB,GACvB,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;AACxB,YAAA,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK;AAC1B,YAAA,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;AAElC,QAAA,IAAI,CAAC,mBAAmB;AAAE,YAAA,OAAO,KAAK;QAEtC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxC,QAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAE9C,MAAM,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC;AACzC,QAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAE9C,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IACxD;AAEA;;AAEG;IACI,eAAe,CACpB,cAAc,GAAG,KAAK,EAAA;AAEtB,QAAA,QACE,MAAM,CAAC,OAAO,CAAC,IAAI;AAChB,aAAA,MAAM,CACL,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KACX,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC9B,YAAA,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,IAAI;;aAGjB,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAA2C;YAC/D,QAAQ,GAAG;AACT,gBAAA,KAAK,SAAS;AACZ,oBAAA,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,CAAC;AACrD,gBAAA,KAAK,kBAAkB;;oBAErB,IAAI,CAAC,cAAc,EAAE;wBACnB,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;oBAC1C;yBAAO;wBACL,OAAO;AACL,4BAAA,CAAC,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,CAAC;AACtC,4BAAA,CAAC,WAAW,EAAE,KAAK,EAAE,eAAe,EAAE,UAAU,CAAC;AACjD,4BAAA,CAAC,UAAU,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,CAAC;AAC/C,4BAAA,CAAC,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,CAAC;AACxC,4BAAA,CAAC,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,CAAC;AAC1C,4BAAA,CAAC,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc,CAAC;AACzD,4BAAA,CAAC,cAAc,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,CAAC;AACvD,4BAAA,CAAC,cAAc,EAAE,KAAK,EAAE,eAAe,EAAE,YAAY,CAAC;yBACvD;oBACH;AACF,gBAAA;AACE,oBAAA,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;;AAE3B,QAAA,CAAC;;AAEA,aAAA,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,KAAK,KAAK,SAAS,CAAC;IAErD;AACD;;;;"}
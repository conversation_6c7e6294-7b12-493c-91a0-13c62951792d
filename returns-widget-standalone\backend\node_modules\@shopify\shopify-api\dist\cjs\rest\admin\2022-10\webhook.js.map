{"version": 3, "file": "webhook.js", "sources": ["../../../../../../../rest/admin/2022-10/webhook.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n  fields?: unknown;\n}\ninterface DeleteArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  address?: unknown;\n  created_at_max?: unknown;\n  created_at_min?: unknown;\n  fields?: unknown;\n  limit?: unknown;\n  since_id?: unknown;\n  topic?: unknown;\n  updated_at_min?: unknown;\n  updated_at_max?: unknown;\n}\ninterface CountArgs {\n  [key: string]: unknown;\n  session: Session;\n  address?: unknown;\n  topic?: unknown;\n}\n\nexport class Webhook extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"delete\", \"operation\": \"delete\", \"ids\": [\"id\"], \"path\": \"webhooks/<id>.json\"},\n    {\"http_method\": \"get\", \"operation\": \"count\", \"ids\": [], \"path\": \"webhooks/count.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"webhooks.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"webhooks/<id>.json\"},\n    {\"http_method\": \"post\", \"operation\": \"post\", \"ids\": [], \"path\": \"webhooks.json\"},\n    {\"http_method\": \"put\", \"operation\": \"put\", \"ids\": [\"id\"], \"path\": \"webhooks/<id>.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"webhook\",\n      \"plural\": \"webhooks\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id,\n      fields = null\n    }: FindArgs\n  ): Promise<Webhook | null> {\n    const result = await this.baseFind<Webhook>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {\"fields\": fields},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async delete(\n    {\n      session,\n      id\n    }: DeleteArgs\n  ): Promise<unknown> {\n    const response = await this.request<Webhook>({\n      http_method: \"delete\",\n      operation: \"delete\",\n      session: session,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n\n    return response ? response.body : null;\n  }\n\n  public static async all(\n    {\n      session,\n      address = null,\n      created_at_max = null,\n      created_at_min = null,\n      fields = null,\n      limit = null,\n      since_id = null,\n      topic = null,\n      updated_at_min = null,\n      updated_at_max = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Webhook>> {\n    const response = await this.baseFind<Webhook>({\n      session: session,\n      urlIds: {},\n      params: {\"address\": address, \"created_at_max\": created_at_max, \"created_at_min\": created_at_min, \"fields\": fields, \"limit\": limit, \"since_id\": since_id, \"topic\": topic, \"updated_at_min\": updated_at_min, \"updated_at_max\": updated_at_max, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public static async count(\n    {\n      session,\n      address = null,\n      topic = null,\n      ...otherArgs\n    }: CountArgs\n  ): Promise<unknown> {\n    const response = await this.request<Webhook>({\n      http_method: \"get\",\n      operation: \"count\",\n      session: session,\n      urlIds: {},\n      params: {\"address\": address, \"topic\": topic, ...otherArgs},\n      body: {},\n      entity: null,\n    });\n\n    return response ? response.body : null;\n  }\n\n  public address: string | null;\n  public topic: string | null;\n  public api_version: string | null;\n  public created_at: string | null;\n  public fields: string[] | null;\n  public format: string | null;\n  public id: number | null;\n  public metafield_namespaces: string[] | null;\n  public private_metafield_namespaces: string[] | null;\n  public updated_at: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAoClH,MAAO,OAAQ,SAAQA,SAAI,CAAA;AACxB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB,EAAC;AAC7F,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,qBAAqB,EAAC;AACtF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,eAAe,EAAC;AAC9E,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB,EAAC;AACvF,QAAA,EAAC,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,eAAe,EAAC;AAChF,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oBAAoB;KACvF;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,SAAS;AACrB,YAAA,QAAQ,EAAE;AACX;KACF;AAEM,IAAA,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACF,MAAM,GAAG,IAAI,EACJ,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAU;AAC1C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAC,QAAQ,EAAE,MAAM,EAAC;AAC3B,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;IAEO,aAAa,MAAM,CACxB,EACE,OAAO,EACP,EAAE,EACS,EAAA;AAEb,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAU;AAC3C,YAAA,WAAW,EAAE,QAAQ;AACrB,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;IAEO,aAAa,GAAG,CACrB,EACE,OAAO,EACP,OAAO,GAAG,IAAI,EACd,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,MAAM,GAAG,IAAI,EACb,KAAK,GAAG,IAAI,EACZ,QAAQ,GAAG,IAAI,EACf,KAAK,GAAG,IAAI,EACZ,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAU;AAC5C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,SAAS,EAAE,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,SAAS,EAAC;AAC3P,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,aAAa,KAAK,CACvB,EACE,OAAO,EACP,OAAO,GAAG,IAAI,EACd,KAAK,GAAG,IAAI,EACZ,GAAG,SAAS,EACF,EAAA;AAEZ,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAU;AAC3C,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,SAAS,EAAE,OAAO;AAClB,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,SAAS,EAAC;AAC1D,YAAA,IAAI,EAAE,EAAE;AACR,YAAA,MAAM,EAAE,IAAI;AACb,SAAA,CAAC;QAEF,OAAO,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;IACxC;AAEO,IAAA,OAAO;AACP,IAAA,KAAK;AACL,IAAA,WAAW;AACX,IAAA,UAAU;AACV,IAAA,MAAM;AACN,IAAA,MAAM;AACN,IAAA,EAAE;AACF,IAAA,oBAAoB;AACpB,IAAA,4BAA4B;AAC5B,IAAA,UAAU;;;;;"}
{"version": 3, "file": "payout.js", "sources": ["../../../../../../../rest/admin/2022-10/payout.ts"], "sourcesContent": ["/***********************************************************************************************************************\n* This file is auto-generated. If you have an issue, please create a GitHub issue.                                     *\n***********************************************************************************************************************/\n\nimport {Base, FindAllResponse} from '../../base';\nimport {ResourcePath, ResourceNames} from '../../types';\nimport {Session} from '../../../lib/session/session';\nimport {ApiVersion} from '../../../lib/types';\n\ninterface FindArgs {\n  session: Session;\n  id: number | string;\n}\ninterface AllArgs {\n  [key: string]: unknown;\n  session: Session;\n  since_id?: unknown;\n  last_id?: unknown;\n  date_min?: unknown;\n  date_max?: unknown;\n  date?: unknown;\n  status?: unknown;\n}\n\nexport class Payout extends Base {\n  public static apiVersion = ApiVersion.October22;\n\n  protected static hasOne: {[key: string]: typeof Base} = {};\n  protected static hasMany: {[key: string]: typeof Base} = {};\n  protected static paths: ResourcePath[] = [\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [], \"path\": \"shopify_payments/payouts.json\"},\n    {\"http_method\": \"get\", \"operation\": \"get\", \"ids\": [\"id\"], \"path\": \"shopify_payments/payouts/<id>.json\"}\n  ];\n  protected static resourceNames: ResourceNames[] = [\n    {\n      \"singular\": \"payout\",\n      \"plural\": \"payouts\"\n    }\n  ];\n\n  public static async find(\n    {\n      session,\n      id\n    }: FindArgs\n  ): Promise<Payout | null> {\n    const result = await this.baseFind<Payout>({\n      session: session,\n      requireIds: true,\n      urlIds: {\"id\": id},\n      params: {},\n    });\n    return result.data ? result.data[0] : null;\n  }\n\n  public static async all(\n    {\n      session,\n      since_id = null,\n      last_id = null,\n      date_min = null,\n      date_max = null,\n      date = null,\n      status = null,\n      ...otherArgs\n    }: AllArgs\n  ): Promise<FindAllResponse<Payout>> {\n    const response = await this.baseFind<Payout>({\n      session: session,\n      urlIds: {},\n      params: {\"since_id\": since_id, \"last_id\": last_id, \"date_min\": date_min, \"date_max\": date_max, \"date\": date, \"status\": status, ...otherArgs},\n    });\n\n    return response;\n  }\n\n  public amount: string | null;\n  public currency: string | null;\n  public date: string | null;\n  public id: number | null;\n  public status: string | null;\n}\n"], "names": ["Base", "ApiVersion"], "mappings": ";;;;;AAAA;;AAEwH;AAsBlH,MAAO,MAAO,SAAQA,SAAI,CAAA;AACvB,IAAA,OAAO,UAAU,GAAGC,gBAAU,CAAC,SAAS;AAErC,IAAA,OAAO,MAAM,GAAiC,EAAE;AAChD,IAAA,OAAO,OAAO,GAAiC,EAAE;IACjD,OAAO,KAAK,GAAmB;AACvC,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,+BAA+B,EAAC;AAC9F,QAAA,EAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,oCAAoC;KACvG;IACS,OAAO,aAAa,GAAoB;AAChD,QAAA;AACE,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,QAAQ,EAAE;AACX;KACF;IAEM,aAAa,IAAI,CACtB,EACE,OAAO,EACP,EAAE,EACO,EAAA;AAEX,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAS;AACzC,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,MAAM,EAAE,EAAC,IAAI,EAAE,EAAE,EAAC;AAClB,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,CAAC;AACF,QAAA,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5C;AAEO,IAAA,aAAa,GAAG,CACrB,EACE,OAAO,EACP,QAAQ,GAAG,IAAI,EACf,OAAO,GAAG,IAAI,EACd,QAAQ,GAAG,IAAI,EACf,QAAQ,GAAG,IAAI,EACf,IAAI,GAAG,IAAI,EACX,MAAM,GAAG,IAAI,EACb,GAAG,SAAS,EACJ,EAAA;AAEV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAS;AAC3C,YAAA,OAAO,EAAE,OAAO;AAChB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,MAAM,EAAE,EAAC,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,SAAS,EAAC;AAC7I,SAAA,CAAC;AAEF,QAAA,OAAO,QAAQ;IACjB;AAEO,IAAA,MAAM;AACN,IAAA,QAAQ;AACR,IAAA,IAAI;AACJ,IAAA,EAAE;AACF,IAAA,MAAM;;;;;"}
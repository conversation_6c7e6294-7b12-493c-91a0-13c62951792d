{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/auth/index.ts"], "sourcesContent": ["import {ConfigInterface} from '../base-types';\n\nimport {OAuthBegin, OAuthCallback, begin, callback} from './oauth/oauth';\nimport {Nonce, nonce} from './oauth/nonce';\nimport {SafeCompare, safeCompare} from './oauth/safe-compare';\nimport {\n  getEmbeddedAppUrl,\n  buildEmbeddedAppUrl,\n  GetEmbeddedAppUrl,\n  BuildEmbeddedAppUrl,\n} from './get-embedded-app-url';\nimport {TokenExchange, tokenExchange} from './oauth/token-exchange';\nimport {ClientCredentials, clientCredentials} from './oauth/client-credentials';\n\nexport {AuthScopes} from './scopes';\n\nexport function shopifyAuth<Config extends ConfigInterface>(\n  config: Config,\n): ShopifyAuth {\n  const shopify = {\n    begin: begin(config),\n    callback: callback(config),\n    nonce,\n    safeCompare,\n    getEmbeddedAppUrl: getEmbeddedAppUrl(config),\n    buildEmbeddedAppUrl: buildEmbeddedAppUrl(config),\n    tokenExchange: tokenExchange(config),\n    clientCredentials: clientCredentials(config),\n  } as ShopifyAuth;\n\n  return shopify;\n}\n\nexport interface ShopifyAuth {\n  begin: OAuthBegin;\n  callback: OAuthCallback;\n  nonce: Nonce;\n  safeCompare: SafeCompare;\n  getEmbeddedAppUrl: GetEmbeddedAppUrl;\n  buildEmbeddedAppUrl: BuildEmbeddedAppUrl;\n  tokenExchange: TokenExchange;\n  clientCredentials: ClientCredentials;\n}\n"], "names": ["begin", "callback", "nonce", "safeCompare", "getEmbeddedAppUrl", "buildEmbeddedAppUrl", "tokenExchange", "clientCredentials"], "mappings": ";;;;;;;;;AAgBM,SAAU,WAAW,CACzB,MAAc,EAAA;AAEd,IAAA,MAAM,OAAO,GAAG;AACd,QAAA,KAAK,EAAEA,WAAK,CAAC,MAAM,CAAC;AACpB,QAAA,QAAQ,EAAEC,cAAQ,CAAC,MAAM,CAAC;eAC1BC,WAAK;qBACLC,uBAAW;AACX,QAAA,iBAAiB,EAAEC,mCAAiB,CAAC,MAAM,CAAC;AAC5C,QAAA,mBAAmB,EAAEC,qCAAmB,CAAC,MAAM,CAAC;AAChD,QAAA,aAAa,EAAEC,2BAAa,CAAC,MAAM,CAAC;AACpC,QAAA,iBAAiB,EAAEC,mCAAiB,CAAC,MAAM,CAAC;KAC9B;AAEhB,IAAA,OAAO,OAAO;AAChB;;;;"}
{"version": 3, "file": "log.js", "sources": ["../../../../../../lib/logger/log.ts"], "sourcesContent": ["import {LogSeverity} from '../types';\nimport {ConfigInterface} from '../base-types';\n\nimport {LogContext} from './types';\n\nexport type LoggerFunction = (\n  severity: LogSeverity,\n  message: string,\n  context?: Record<string, any>,\n) => void;\n\nexport function log(config: ConfigInterface): LoggerFunction {\n  return function (\n    severity: LogSeverity,\n    message: string,\n    context: LogContext = {},\n  ): void {\n    if (severity > config.logger.level) {\n      return;\n    }\n\n    const prefix: string[] = [];\n\n    if (config.logger.timestamps) {\n      prefix.push(`${new Date().toISOString().slice(0, -5)}Z`);\n    }\n\n    let packageString = context.package || 'shopify-api';\n    delete context.package;\n\n    switch (severity) {\n      case LogSeverity.Debug:\n        packageString = `${packageString}/DEBUG`;\n        break;\n      case LogSeverity.Info:\n        packageString = `${packageString}/INFO`;\n        break;\n      case LogSeverity.Warning:\n        packageString = `${packageString}/WARNING`;\n        break;\n      case LogSeverity.Error:\n        packageString = `${packageString}/ERROR`;\n        break;\n    }\n\n    prefix.push(packageString);\n\n    const contextParts: string[] = [];\n    Object.entries(context).forEach(([key, value]) => {\n      contextParts.push(`${key}: ${value}`);\n    });\n\n    let suffix = '';\n    if (contextParts.length > 0) {\n      suffix = ` | {${contextParts.join(', ')}}`;\n    }\n\n    config.logger.log(severity, `[${prefix.join('] [')}] ${message}${suffix}`);\n  };\n}\n"], "names": ["LogSeverity"], "mappings": ";;;;AAWM,SAAU,GAAG,CAAC,MAAuB,EAAA;AACzC,IAAA,OAAO,UACL,QAAqB,EACrB,OAAe,EACf,UAAsB,EAAE,EAAA;QAExB,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;YAClC;QACF;QAEA,MAAM,MAAM,GAAa,EAAE;AAE3B,QAAA,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;YAC5B,MAAM,CAAC,IAAI,CAAC,CAAA,EAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA,CAAA,CAAG,CAAC;QAC1D;AAEA,QAAA,IAAI,aAAa,GAAG,OAAO,CAAC,OAAO,IAAI,aAAa;QACpD,OAAO,OAAO,CAAC,OAAO;QAEtB,QAAQ,QAAQ;YACd,KAAKA,iBAAW,CAAC,KAAK;AACpB,gBAAA,aAAa,GAAG,CAAA,EAAG,aAAa,CAAA,MAAA,CAAQ;gBACxC;YACF,KAAKA,iBAAW,CAAC,IAAI;AACnB,gBAAA,aAAa,GAAG,CAAA,EAAG,aAAa,CAAA,KAAA,CAAO;gBACvC;YACF,KAAKA,iBAAW,CAAC,OAAO;AACtB,gBAAA,aAAa,GAAG,CAAA,EAAG,aAAa,CAAA,QAAA,CAAU;gBAC1C;YACF,KAAKA,iBAAW,CAAC,KAAK;AACpB,gBAAA,aAAa,GAAG,CAAA,EAAG,aAAa,CAAA,MAAA,CAAQ;gBACxC;;AAGJ,QAAA,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;QAE1B,MAAM,YAAY,GAAa,EAAE;AACjC,QAAA,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;YAC/C,YAAY,CAAC,IAAI,CAAC,CAAA,EAAG,GAAG,CAAA,EAAA,EAAK,KAAK,CAAA,CAAE,CAAC;AACvC,QAAA,CAAC,CAAC;QAEF,IAAI,MAAM,GAAG,EAAE;AACf,QAAA,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,MAAM,GAAG,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG;QAC5C;QAEA,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,EAAA,EAAK,OAAO,GAAG,MAAM,CAAA,CAAE,CAAC;AAC5E,IAAA,CAAC;AACH;;;;"}
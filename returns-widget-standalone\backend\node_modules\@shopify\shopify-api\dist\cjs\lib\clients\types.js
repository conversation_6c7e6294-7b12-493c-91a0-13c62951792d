'use strict';

require('@shopify/admin-api-client');
require('compare-versions');
require('../types.js');
require('../../runtime/crypto/crypto.js');
require('../../runtime/crypto/types.js');
require('@shopify/network');

/* eslint-disable @shopify/typescript/prefer-pascal-case-enums */
exports.DataType = void 0;
(function (DataType) {
    DataType["JSON"] = "application/json";
    DataType["GraphQL"] = "application/graphql";
    DataType["URLEncoded"] = "application/x-www-form-urlencoded";
})(exports.DataType || (exports.DataType = {}));
//# sourceMappingURL=types.js.map

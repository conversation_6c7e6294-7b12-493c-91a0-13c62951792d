{"version": 3, "file": "client.js", "sources": ["../../../../../../../../lib/clients/admin/graphql/client.ts"], "sourcesContent": ["import {\n  AdminApiClient,\n  AdminOperations,\n  ApiClientRequestOptions,\n  createAdminApiClient,\n  ReturnData,\n} from '@shopify/admin-api-client';\n\nimport {ApiVersion} from '../../../types';\nimport {ConfigInterface} from '../../../base-types';\nimport type {\n  RequestReturn,\n  GraphqlParams,\n  GraphqlClientParams,\n  GraphqlQueryOptions,\n  GraphQLClientResponse,\n} from '../../types';\nimport {Session} from '../../../session/session';\nimport {logger} from '../../../logger';\nimport * as ShopifyErrors from '../../../error';\nimport {abstractFetch, canonicalizeHeaders} from '../../../../runtime';\nimport {\n  clientLoggerFactory,\n  getUserAgent,\n  throwFailedRequest,\n} from '../../common';\n\ninterface GraphqlClientClassParams {\n  config: ConfigInterface;\n}\nexport class GraphqlClient {\n  public static config: ConfigInterface;\n\n  readonly session: Session;\n  readonly client: AdminApiClient;\n  readonly apiVersion?: ApiVersion;\n\n  constructor(params: GraphqlClientParams) {\n    const config = this.graphqlClass().config;\n\n    if (!config.isCustomStoreApp && !params.session.accessToken) {\n      throw new ShopifyErrors.MissingRequiredArgument(\n        'Missing access token when creating GraphQL client',\n      );\n    }\n\n    if (params.apiVersion) {\n      const message =\n        params.apiVersion === config.apiVersion\n          ? `Admin client has a redundant API version override to the default ${params.apiVersion}`\n          : `Admin client overriding default API version ${config.apiVersion} with ${params.apiVersion}`;\n\n      logger(config).debug(message);\n    }\n\n    this.session = params.session;\n    this.apiVersion = params.apiVersion;\n    this.client = createAdminApiClient({\n      accessToken: config.adminApiAccessToken ?? this.session.accessToken!,\n      apiVersion: this.apiVersion ?? config.apiVersion,\n      storeDomain: this.session.shop,\n      customFetchApi: abstractFetch,\n      logger: clientLoggerFactory(config),\n      userAgentPrefix: getUserAgent(config),\n      isTesting: config.isTesting,\n    });\n  }\n\n  public async query<T = undefined>(\n    params: GraphqlParams,\n  ): Promise<RequestReturn<T>> {\n    logger(this.graphqlClass().config).deprecated(\n      '12.0.0',\n      'The query method is deprecated, and was replaced with the request method.\\n' +\n        'See the migration guide: https://github.com/Shopify/shopify-app-js/blob/main/packages/apps/shopify-api/docs/migrating-to-v9.md#using-the-new-clients.',\n    );\n\n    if (\n      (typeof params.data === 'string' && params.data.length === 0) ||\n      Object.entries(params.data).length === 0\n    ) {\n      throw new ShopifyErrors.MissingRequiredArgument('Query missing.');\n    }\n\n    let operation: string;\n    let variables: Record<string, any> | undefined;\n    if (typeof params.data === 'string') {\n      operation = params.data;\n    } else {\n      operation = params.data.query;\n      variables = params.data.variables;\n    }\n\n    const headers = Object.fromEntries(\n      Object.entries(params?.extraHeaders ?? {}).map(([key, value]) => [\n        key,\n        Array.isArray(value) ? value.join(', ') : value.toString(),\n      ]),\n    );\n\n    const response = await this.request<T>(operation, {\n      headers,\n      retries: params.tries ? params.tries - 1 : undefined,\n      variables,\n    });\n\n    return {body: response as T, headers: {}};\n  }\n\n  public async request<\n    T = undefined,\n    Operation extends keyof Operations = string,\n    Operations extends AdminOperations = AdminOperations,\n  >(\n    operation: Operation,\n    options?: GraphqlQueryOptions<Operation, Operations>,\n  ): Promise<\n    GraphQLClientResponse<\n      T extends undefined ? ReturnData<Operation, Operations> : T\n    >\n  > {\n    const response = await this.client.request<\n      T extends undefined ? ReturnData<Operation, Operations> : T,\n      Operation\n    >(operation, {\n      apiVersion: this.apiVersion || this.graphqlClass().config.apiVersion,\n      ...(options as ApiClientRequestOptions<Operation, AdminOperations>),\n    });\n\n    if (response.errors) {\n      const fetchResponse = response.errors.response;\n\n      throwFailedRequest(response, (options?.retries ?? 0) > 0, fetchResponse);\n    }\n\n    const headerObject = Object.fromEntries(\n      response.headers ? response.headers.entries() : [],\n    );\n\n    return {\n      ...response,\n      headers: canonicalizeHeaders(headerObject ?? {}),\n    };\n  }\n\n  private graphqlClass() {\n    return this.constructor as typeof GraphqlClient;\n  }\n}\n\nexport function graphqlClientClass({\n  config,\n}: GraphqlClientClassParams): typeof GraphqlClient {\n  class NewGraphqlClient extends GraphqlClient {\n    public static config = config;\n  }\n\n  Reflect.defineProperty(NewGraphqlClient, 'name', {\n    value: 'GraphqlClient',\n  });\n\n  return NewGraphqlClient as typeof GraphqlClient;\n}\n"], "names": ["ShopifyErrors.MissingRequiredArgument", "logger", "createAdminApiClient", "abstractFetch", "clientLoggerFactory", "getUserAgent", "throwFailedRequest", "canonicalizeHeaders"], "mappings": ";;;;;;;;;;;MA8Ba,aAAa,CAAA;IACjB,OAAO,MAAM;AAEX,IAAA,OAAO;AACP,IAAA,MAAM;AACN,IAAA,UAAU;AAEnB,IAAA,WAAA,CAAY,MAA2B,EAAA;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM;AAEzC,QAAA,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE;AAC3D,YAAA,MAAM,IAAIA,6BAAqC,CAC7C,mDAAmD,CACpD;QACH;AAEA,QAAA,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,MAAM,OAAO,GACX,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC;AAC3B,kBAAE,CAAA,iEAAA,EAAoE,MAAM,CAAC,UAAU,CAAA;kBACrF,CAAA,4CAAA,EAA+C,MAAM,CAAC,UAAU,SAAS,MAAM,CAAC,UAAU,CAAA,CAAE;YAElGC,YAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QAC/B;AAEA,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;AAC7B,QAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU;AACnC,QAAA,IAAI,CAAC,MAAM,GAAGC,mCAAoB,CAAC;YACjC,WAAW,EAAE,MAAM,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAY;AACpE,YAAA,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU;AAChD,YAAA,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;AAC9B,YAAA,cAAc,EAAEC,qBAAa;AAC7B,YAAA,MAAM,EAAEC,0BAAmB,CAAC,MAAM,CAAC;AACnC,YAAA,eAAe,EAAEC,mBAAY,CAAC,MAAM,CAAC;YACrC,SAAS,EAAE,MAAM,CAAC,SAAS;AAC5B,SAAA,CAAC;IACJ;IAEO,MAAM,KAAK,CAChB,MAAqB,EAAA;AAErB,QAAAJ,YAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC,UAAU,CAC3C,QAAQ,EACR,6EAA6E;AAC3E,YAAA,uJAAuJ,CAC1J;AAED,QAAA,IACE,CAAC,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAC5D,YAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EACxC;AACA,YAAA,MAAM,IAAID,6BAAqC,CAAC,gBAAgB,CAAC;QACnE;AAEA,QAAA,IAAI,SAAiB;AACrB,QAAA,IAAI,SAA0C;AAC9C,QAAA,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AACnC,YAAA,SAAS,GAAG,MAAM,CAAC,IAAI;QACzB;aAAO;AACL,YAAA,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;AAC7B,YAAA,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS;QACnC;QAEA,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAChC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;YAC/D,GAAG;YACH,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE;AAC3D,SAAA,CAAC,CACH;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAI,SAAS,EAAE;YAChD,OAAO;AACP,YAAA,OAAO,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS;YACpD,SAAS;AACV,SAAA,CAAC;QAEF,OAAO,EAAC,IAAI,EAAE,QAAa,EAAE,OAAO,EAAE,EAAE,EAAC;IAC3C;AAEO,IAAA,MAAM,OAAO,CAKlB,SAAoB,EACpB,OAAoD,EAAA;QAMpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAGxC,SAAS,EAAE;AACX,YAAA,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,UAAU;AACpE,YAAA,GAAI,OAA+D;AACpE,SAAA,CAAC;AAEF,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ;AAE9C,YAAAM,yBAAkB,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC;QAC1E;QAEA,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,CACrC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,CACnD;QAED,OAAO;AACL,YAAA,GAAG,QAAQ;AACX,YAAA,OAAO,EAAEC,2BAAmB,CAAC,YAAY,IAAI,EAAE,CAAC;SACjD;IACH;IAEQ,YAAY,GAAA;QAClB,OAAO,IAAI,CAAC,WAAmC;IACjD;AACD;AAEK,SAAU,kBAAkB,CAAC,EACjC,MAAM,GACmB,EAAA;IACzB,MAAM,gBAAiB,SAAQ,aAAa,CAAA;AACnC,QAAA,OAAO,MAAM,GAAG,MAAM;;AAG/B,IAAA,OAAO,CAAC,cAAc,CAAC,gBAAgB,EAAE,MAAM,EAAE;AAC/C,QAAA,KAAK,EAAE,eAAe;AACvB,KAAA,CAAC;AAEF,IAAA,OAAO,gBAAwC;AACjD;;;;;"}
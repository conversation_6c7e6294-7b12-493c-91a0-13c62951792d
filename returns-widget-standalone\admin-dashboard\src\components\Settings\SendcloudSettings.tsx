import { useState } from 'react'
import { api } from '../../services/api'
import { CheckCircle, AlertCircle, ExternalLink, Truck } from 'lucide-react'
import styles from './SendcloudSettings.module.css'

export const SendcloudSettings = () => {
  const [credentials, setCredentials] = useState({
    publicKey: '',
    secretKey: ''
  })
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean
    carrierCount?: number
  } | null>(null)

  const handleInputChange = (field: 'publicKey' | 'secretKey', value: string) => {
    setCredentials(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      await api.saveSendcloudCredentials(credentials)
      setSuccess('Sendcloud credentials saved successfully!')
      
      // Auto-test connection after saving
      await testConnection()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save credentials')
    } finally {
      setLoading(false)
    }
  }

  const testConnection = async () => {
    if (!credentials.publicKey.trim() || !credentials.secretKey.trim()) {
      setError('Please enter both public and secret keys')
      return
    }

    setTesting(true)
    setError(null)

    try {
      const result = await api.testSendcloudConnection()
      setConnectionStatus(result)
      
      if (result.connected) {
        setSuccess(`Connected! Found ${result.carrierCount || 0} available carriers.`)
      } else {
        setError('Failed to connect to Sendcloud. Please check your credentials.')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection test failed')
      setConnectionStatus({ connected: false })
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Sendcloud Integration</h2>
        <p>Connect Sendcloud to generate return shipping labels for your customers.</p>
      </div>

      <div className={styles.setupGuide}>
        <h3>Setup Instructions</h3>
        <ol>
          <li>Sign up for a <strong>Sendcloud</strong> account if you don't have one</li>
          <li>Go to <strong>Settings → API</strong> in your Sendcloud panel</li>
          <li>Generate API credentials (Public Key & Secret Key)</li>
          <li>Configure your sender addresses in Sendcloud</li>
          <li>Set up your preferred carriers and shipping methods</li>
          <li>Copy your API keys below</li>
        </ol>
        <a 
          href="https://docs.sendcloud.sc/api/v2/"
          target="_blank"
          rel="noopener noreferrer"
          className={styles.helpLink}
        >
          <ExternalLink size={16} />
          View Sendcloud API documentation
        </a>
      </div>

      <form onSubmit={handleSave} className={styles.form}>
        <div className={styles.field}>
          <label htmlFor="publicKey">
            Public Key
            <span className={styles.required}>*</span>
          </label>
          <input
            id="publicKey"
            type="text"
            value={credentials.publicKey}
            onChange={(e) => handleInputChange('publicKey', e.target.value)}
            placeholder="your-public-key"
            required
            disabled={loading || testing}
            className={styles.input}
          />
        </div>

        <div className={styles.field}>
          <label htmlFor="secretKey">
            Secret Key
            <span className={styles.required}>*</span>
          </label>
          <input
            id="secretKey"
            type="password"
            value={credentials.secretKey}
            onChange={(e) => handleInputChange('secretKey', e.target.value)}
            placeholder="your-secret-key"
            required
            disabled={loading || testing}
            className={styles.input}
          />
          <small className={styles.hint}>
            Keep your secret key secure and never share it publicly
          </small>
        </div>

        {connectionStatus && (
          <div className={`${styles.status} ${connectionStatus.connected ? styles.success : styles.error}`}>
            {connectionStatus.connected ? (
              <>
                <CheckCircle size={20} />
                <span>
                  <Truck size={16} className={styles.inline} />
                  Connected - {connectionStatus.carrierCount} carriers available
                </span>
              </>
            ) : (
              <>
                <AlertCircle size={20} />
                <span>Connection failed</span>
              </>
            )}
          </div>
        )}

        {error && (
          <div className={styles.errorMessage}>
            <AlertCircle size={16} />
            {error}
          </div>
        )}

        {success && (
          <div className={styles.successMessage}>
            <CheckCircle size={16} />
            {success}
          </div>
        )}

        <div className={styles.actions}>
          <button
            type="button"
            onClick={testConnection}
            disabled={!credentials.publicKey.trim() || !credentials.secretKey.trim() || testing || loading}
            className={styles.testButton}
          >
            {testing ? 'Testing...' : 'Test Connection'}
          </button>
          
          <button
            type="submit"
            disabled={loading || testing || !credentials.publicKey.trim() || !credentials.secretKey.trim()}
            className={styles.saveButton}
          >
            {loading ? 'Saving...' : 'Save Credentials'}
          </button>
        </div>
      </form>

      <div className={styles.supportedCarriers}>
        <h3>Supported Carriers</h3>
        <div className={styles.carriers}>
          <div className={styles.carrier}>Royal Mail</div>
          <div className={styles.carrier}>DPD</div>
          <div className={styles.carrier}>Evri (Hermes)</div>
          <div className={styles.carrier}>UPS</div>
          <div className={styles.carrier}>DHL</div>
          <div className={styles.carrier}>And more...</div>
        </div>
      </div>
    </div>
  )
}

{"version": 3, "file": "index.js", "sources": ["../../../../../../lib/logger/index.ts"], "sourcesContent": ["import {compare} from 'compare-versions';\n\nimport {LogSeverity} from '../types';\nimport {ConfigInterface} from '../base-types';\nimport {FeatureDeprecatedError} from '../error';\nimport {SHOPIFY_API_LIBRARY_VERSION} from '../version';\n\nimport {log, LoggerFunction} from './log';\nimport {LogContext} from './types';\n\nexport function logger(config: ConfigInterface) {\n  const logFunction = log(config);\n\n  return {\n    log: logFunction,\n    debug: async (message: string, context: LogContext = {}) =>\n      logFunction(LogSeverity.Debug, message, context),\n    info: async (message: string, context: LogContext = {}) =>\n      logFunction(LogSeverity.Info, message, context),\n    warning: async (message: string, context: LogContext = {}) =>\n      logFunction(LogSeverity.Warning, message, context),\n    error: async (message: string, context: LogContext = {}) =>\n      logFunction(LogSeverity.Error, message, context),\n    deprecated: deprecated(logFunction),\n  };\n}\n\nexport type ShopifyLogger = ReturnType<typeof logger>;\n\nfunction deprecated(logFunction: LoggerFunction) {\n  return function (version: string, message: string): void {\n    if (compare(SHOPIFY_API_LIBRARY_VERSION, version, '>=')) {\n      throw new FeatureDeprecatedError(\n        `Feature was deprecated in version ${version}`,\n      );\n    }\n\n    return logFunction(\n      LogSeverity.Warning,\n      `[Deprecated | ${version}] ${message}`,\n    );\n  };\n}\n"], "names": ["log", "LogSeverity", "version", "compare", "SHOPIFY_API_LIBRARY_VERSION", "FeatureDeprecatedError"], "mappings": ";;;;;;;;AAUM,SAAU,MAAM,CAAC,MAAuB,EAAA;AAC5C,IAAA,MAAM,WAAW,GAAGA,OAAG,CAAC,MAAM,CAAC;IAE/B,OAAO;AACL,QAAA,GAAG,EAAE,WAAW;AAChB,QAAA,KAAK,EAAE,OAAO,OAAe,EAAE,OAAA,GAAsB,EAAE,KACrD,WAAW,CAACC,iBAAW,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;AAClD,QAAA,IAAI,EAAE,OAAO,OAAe,EAAE,OAAA,GAAsB,EAAE,KACpD,WAAW,CAACA,iBAAW,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC;AACjD,QAAA,OAAO,EAAE,OAAO,OAAe,EAAE,OAAA,GAAsB,EAAE,KACvD,WAAW,CAACA,iBAAW,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACpD,QAAA,KAAK,EAAE,OAAO,OAAe,EAAE,OAAA,GAAsB,EAAE,KACrD,WAAW,CAACA,iBAAW,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;AAClD,QAAA,UAAU,EAAE,UAAU,CAAC,WAAW,CAAC;KACpC;AACH;AAIA,SAAS,UAAU,CAAC,WAA2B,EAAA;IAC7C,OAAO,UAAUC,SAAe,EAAE,OAAe,EAAA;QAC/C,IAAIC,uBAAO,CAACC,mCAA2B,EAAEF,SAAO,EAAE,IAAI,CAAC,EAAE;AACvD,YAAA,MAAM,IAAIG,4BAAsB,CAC9B,qCAAqCH,SAAO,CAAA,CAAE,CAC/C;QACH;AAEA,QAAA,OAAO,WAAW,CAChBD,iBAAW,CAAC,OAAO,EACnB,CAAA,cAAA,EAAiBC,SAAO,CAAA,EAAA,EAAK,OAAO,CAAA,CAAE,CACvC;AACH,IAAA,CAAC;AACH;;;;"}